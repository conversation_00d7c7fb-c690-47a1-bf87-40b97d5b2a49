'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/auth-context';
import { SignInRequest, SignUpRequest, VerifyOTPRequest } from '@/types/auth';
import { PrivacyPolicyModal } from './privacy-policy-modal';
import { TermsOfUseModal } from './terms-of-use-modal';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type AuthStep = 'signin' | 'signup' | 'verify-otp';

export function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const [step, setStep] = useState<AuthStep>('signin');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingAuth, setPendingAuth] = useState<{ email: string; provider: string } | null>(null);
  
  const { signIn, signUp, verifyOTP, signInWithGoogle } = useAuth();

  const [signInData, setSignInData] = useState<SignInRequest>({
    provider: 'email',
    email: '',
  });

  const [signUpData, setSignUpData] = useState<SignUpRequest>({
    provider: 'email',
    name: '',
    email: '',
    dateOfBirth: '',
    gender: '',
    about: '',
  });
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);

  const [otpCode, setOtpCode] = useState('');

  const resetForm = () => {
    setSignInData({ provider: 'email', email: '' });
    setSignUpData({ provider: 'email', name: '', email: '', dateOfBirth: '', gender: '', about: '' });
    setOtpCode('');
    setError(null);
    setPendingAuth(null);
    setAgreedToTerms(false);
    setStep('signin');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signIn(signInData);
      setPendingAuth({ email: signInData.email!, provider: signInData.provider });
      setStep('verify-otp');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Gagal masuk');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signUp(signUpData);
      setPendingAuth({ email: signUpData.email!, provider: signUpData.provider });
      setStep('verify-otp');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Gagal mendaftar');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!pendingAuth) return;

    setIsLoading(true);
    setError(null);

    try {
      const verifyData: VerifyOTPRequest = {
        provider: pendingAuth.provider as 'email',
        email: pendingAuth.email,
        code: otpCode,
      };
      
      await verifyOTP(verifyData);
      handleClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Verifikasi OTP gagal');
    } finally {
      setIsLoading(false);
    }
  };

  const renderSignInForm = () => (
    <form onSubmit={handleSignIn} className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="signin-email" className="block text-sm font-semibold text-foreground">
          Email
        </label>
        <Input
          id="signin-email"
          type="email"
          value={signInData.email}
          onChange={(e) => setSignInData({ ...signInData, email: e.target.value })}
          placeholder="Masukkan email Anda"
          className="h-12 rounded-xl border-2 focus:border-bestieku-primary transition-all duration-200 bg-background/50"
          required
        />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-600 text-sm">
          {error}
        </div>
      )}

      <Button
        type="submit"
        className="w-full h-12 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
        disabled={isLoading}
      >
        {isLoading ? 'Mengirim OTP...' : 'Kirim OTP'}
      </Button>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-muted" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">Atau</span>
        </div>
      </div>

      {/* Google Login Button */}
      <Button
        type="button"
        onClick={signInWithGoogle}
        variant="outline"
        className="w-full h-12 border-2 hover:bg-muted/50 rounded-xl font-semibold text-base transition-all duration-200 hover:scale-[1.02]"
        disabled={isLoading}
      >
        <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Masuk dengan Google
      </Button>

      <div className="text-center pt-4 border-t border-muted">
        <button
          type="button"
          onClick={() => setStep('signup')}
          className="text-sm text-bestieku-primary hover:text-bestieku-primary-dark hover:underline font-medium transition-colors"
        >
          Belum punya akun? Daftar
        </button>
      </div>
    </form>
  );

  const renderSignUpForm = () => (
    <form onSubmit={handleSignUp} className="space-y-5">
      <div className="grid grid-cols-1 gap-5">
        <div className="space-y-2">
          <label htmlFor="signup-name" className="block text-sm font-semibold text-foreground">
            Nama *
          </label>
          <Input
            id="signup-name"
            type="text"
            value={signUpData.name}
            onChange={(e) => setSignUpData({ ...signUpData, name: e.target.value })}
            placeholder="Masukkan nama lengkap Anda (min 3 karakter)"
            className="h-11 rounded-xl border-2 focus:border-bestieku-primary transition-all duration-200 bg-background/50"
            required
            minLength={3}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="signup-email" className="block text-sm font-semibold text-foreground">
            Email *
          </label>
          <Input
            id="signup-email"
            type="email"
            value={signUpData.email}
            onChange={(e) => setSignUpData({ ...signUpData, email: e.target.value })}
            placeholder="Masukkan email Anda"
            className="h-11 rounded-xl border-2 focus:border-bestieku-primary transition-all duration-200 bg-background/50"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="signup-dob" className="block text-sm font-semibold text-foreground">
            Tanggal Lahir *
          </label>
          <Input
            id="signup-dob"
            type="date"
            value={signUpData.dateOfBirth}
            onChange={(e) => setSignUpData({ ...signUpData, dateOfBirth: e.target.value })}
            className="h-11 rounded-xl border-2 focus:border-bestieku-primary transition-all duration-200 bg-background/50"
            required
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="signup-gender" className="block text-sm font-semibold text-foreground">
            Jenis Kelamin *
          </label>
          <select
            id="signup-gender"
            value={signUpData.gender}
            onChange={(e) => setSignUpData({ ...signUpData, gender: e.target.value })}
            className="flex h-11 w-full rounded-xl border-2 border-input bg-background/50 px-3 py-2 text-sm transition-all duration-200 focus:border-bestieku-primary focus:outline-none"
            required
          >
            <option value="">Pilih jenis kelamin</option>
            <option value="male">Laki-laki</option>
            <option value="female">Perempuan</option>
            <option value="other">Lainnya</option>
          </select>
        </div>
      </div>

        <div className="space-y-2">
          <label htmlFor="signup-about" className="block text-sm font-semibold text-foreground">
            Tentang Anda
          </label>
          <textarea
            id="signup-about"
            value={signUpData.about}
            onChange={(e) => setSignUpData({ ...signUpData, about: e.target.value })}
            placeholder="Ceritakan tentang diri Anda (opsional)"
            className="flex min-h-[80px] w-full rounded-xl border-2 border-input bg-background/50 px-3 py-3 text-sm transition-all duration-200 focus:border-bestieku-primary focus:outline-none resize-none"
            rows={3}
          />
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-600 text-sm">
          {error}
        </div>
      )}

      <div className="space-y-3">
        <div className="flex items-start space-x-2">
          <input
            type="checkbox"
            id="agree-terms"
            checked={agreedToTerms}
            onChange={(e) => setAgreedToTerms(e.target.checked)}
            className="mt-1 h-4 w-4 rounded border-gray-300 text-bestieku-primary focus:ring-bestieku-primary"
            required
          />
          <label htmlFor="agree-terms" className="text-sm text-muted-foreground">
            Saya menyetujui{' '}
            <button
              type="button"
              onClick={() => setShowTermsModal(true)}
              className="text-bestieku-primary hover:text-bestieku-primary-dark hover:underline font-medium"
            >
              Ketentuan Penggunaan
            </button>{' '}
            dan{' '}
            <button
              type="button"
              onClick={() => setShowPrivacyModal(true)}
              className="text-bestieku-primary hover:text-bestieku-primary-dark hover:underline font-medium"
            >
              Kebijakan Privasi
            </button>
          </label>
        </div>
      </div>

      <Button
        type="submit"
        className="w-full h-12 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
        disabled={isLoading || !agreedToTerms}
      >
        {isLoading ? 'Mengirim OTP...' : 'Daftar'}
      </Button>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-muted" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">Atau</span>
        </div>
      </div>

      {/* Google Login Button */}
      <Button
        type="button"
        onClick={signInWithGoogle}
        variant="outline"
        className="w-full h-12 border-2 hover:bg-muted/50 rounded-xl font-semibold text-base transition-all duration-200 hover:scale-[1.02]"
        disabled={isLoading}
      >
        <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Daftar dengan Google
      </Button>

      <div className="text-center pt-4 border-t border-muted">
        <button
          type="button"
          onClick={() => setStep('signin')}
          className="text-sm text-bestieku-primary hover:text-bestieku-primary-dark hover:underline font-medium transition-colors"
        >
          Sudah punya akun? Masuk
        </button>
      </div>
    </form>
  );

  const renderOTPForm = () => (
    <form onSubmit={handleVerifyOTP} className="space-y-6">
      <div className="text-center bg-muted/30 rounded-xl p-4">
        <div className="w-16 h-16 bg-bestieku-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg className="w-8 h-8 text-bestieku-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <p className="text-sm text-muted-foreground">
          Kami telah mengirim kode verifikasi ke
        </p>
        <p className="font-semibold text-foreground mt-1">
          {pendingAuth?.email}
        </p>
      </div>

      <div className="space-y-2">
        <label htmlFor="otp-code" className="block text-sm font-semibold text-foreground text-center">
          Kode Verifikasi
        </label>
        <Input
          id="otp-code"
          type="text"
          value={otpCode}
          onChange={(e) => setOtpCode(e.target.value)}
          placeholder="Masukkan kode 4 digit"
          className="h-14 rounded-xl border-2 focus:border-bestieku-primary transition-all duration-200 bg-background/50 text-center text-lg font-mono tracking-widest"
          maxLength={6}
          required
        />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-600 text-sm text-center">
          {error}
        </div>
      )}

      <Button
        type="submit"
        className="w-full h-12 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
        disabled={isLoading}
      >
        {isLoading ? 'Memverifikasi...' : 'Verifikasi Kode'}
      </Button>

      <div className="text-center pt-4 border-t border-muted">
        <button
          type="button"
          onClick={() => setStep('signin')}
          className="text-sm text-bestieku-primary hover:text-bestieku-primary-dark hover:underline font-medium transition-colors"
        >
          Kembali ke masuk
        </button>
      </div>
    </form>
  );

  const getTitle = () => {
    switch (step) {
      case 'signin':
        return 'Masuk';
      case 'signup':
        return 'Daftar';
      case 'verify-otp':
        return 'Verifikasi Email';
      default:
        return 'Autentikasi';
    }
  };

  const renderContent = () => {
    switch (step) {
      case 'signin':
        return renderSignInForm();
      case 'signup':
        return renderSignUpForm();
      case 'verify-otp':
        return renderOTPForm();
      default:
        return null;
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-background">
          <DialogHeader className="text-center space-y-3 pb-6">
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              {getTitle()}
            </DialogTitle>
            <p className="text-sm text-muted-foreground">
              {step === 'signin' && 'Masuk ke akun Bestieku Anda'}
              {step === 'signup' && 'Bergabung dengan komunitas Bestieku'}
              {step === 'verify-otp' && 'Verifikasi untuk melanjutkan'}
            </p>
          </DialogHeader>
          <div className="space-y-6">
            {renderContent()}
          </div>
        </DialogContent>
      </Dialog>

      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
      <TermsOfUseModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />
    </>
  );
}
