'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RotateCcw } from 'lucide-react';

interface ResetChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  characterName?: string;
  messageCount: number;
  isLoading?: boolean;
}

export function ResetChatModal({
  isOpen,
  onClose,
  onConfirm,
  characterName,
  messageCount,
  isLoading = false
}: ResetChatModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold">
                Reset Chat dengan {characterName}?
              </DialogTitle>
            </div>
          </div>
          <DialogDescription className="text-sm text-muted-foreground space-y-3">
            <p>
              Anda akan menghapus <strong>{messageCount} pesan</strong> dalam percakapan ini.
            </p>
            
            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
              <h4 className="font-medium text-amber-800 dark:text-amber-200 mb-2">
                Konsekuensi reset chat:
              </h4>
              <ul className="text-sm text-amber-700 dark:text-amber-300 space-y-1">
                <li>• Semua riwayat percakapan akan hilang permanen</li>
                <li>• {characterName} tidak akan mengingat percakapan sebelumnya</li>
                <li>• Anda akan memulai dari awal dengan adegan pembuka</li>
                <li>• Aksi ini tidak dapat dibatalkan</li>
              </ul>
            </div>

            <p className="text-xs text-muted-foreground">
              Tip: Jika Anda hanya ingin mengubah topik percakapan, coba kirim pesan baru tanpa mereset chat.
            </p>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Batal
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isLoading}
            className="w-full sm:w-auto flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Mereset...
              </>
            ) : (
              <>
                <RotateCcw className="w-4 h-4" />
                Ya, Reset Chat
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
