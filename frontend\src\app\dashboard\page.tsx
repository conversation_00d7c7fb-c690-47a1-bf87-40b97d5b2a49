'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { useAuth } from "@/contexts/auth-context"
import { CharacterCard } from "@/components/character-card"
import { CharacterSearch } from "@/components/character-search"
// Remove pagination import - we'll use infinity scroll instead
import { characterService } from "@/services/character"
import { Character, GetCharactersParams } from "@/types/character"

export default function Page() {
  const { isAuthenticated, user, isLoading } = useAuth();
  const router = useRouter();
  const [characters, setCharacters] = useState<Character[]>([]);
  const [charactersLoading, setCharactersLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchParams, setSearchParams] = useState<GetCharactersParams>({
    page: 1,
    limit: 12,
    sortBy: 'favoriteCount', // Default to most liked characters
    sortDirection: 'desc' // Default to descending (highest first)
  });
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Fetch characters (initial load or new search)
  const fetchCharacters = async (params: GetCharactersParams, reset = true) => {
    try {
      if (reset) {
        setCharactersLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await characterService.getCharacters(params);

      if (reset) {
        setCharacters(response.data);
        setCurrentPage(1);
      } else {
        setCharacters(prev => [...prev, ...response.data]);
        setCurrentPage(params.page || 1);
      }

      // Check if there are more pages
      setHasMore(response.currentPage < response.totalPages);

    } catch (error) {
      console.error('Failed to fetch characters:', error);
    } finally {
      if (reset) {
        setCharactersLoading(false);
      } else {
        setLoadingMore(false);
      }
    }
  };

  // Load available tags on mount
  useEffect(() => {
    const loadTags = async () => {
      const tags = await characterService.getAllTags();
      setAvailableTags(tags);
    };
    loadTags();
  }, []);

  // Load more characters
  const loadMoreCharacters = useCallback(async () => {
    if (loadingMore || !hasMore) return;

    const nextPage = currentPage + 1;
    const params = { ...searchParams, page: nextPage };
    await fetchCharacters(params, false);
  }, [searchParams, currentPage, loadingMore, hasMore]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore && !charactersLoading) {
          loadMoreCharacters();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadMoreCharacters, hasMore, loadingMore, charactersLoading]);

  // Load characters on mount and when search params change (reset)
  useEffect(() => {
    fetchCharacters(searchParams, true);
  }, [searchParams]);

  // Handle search (always reset to page 1)
  const handleSearch = (params: GetCharactersParams) => {
    const newParams = { ...searchParams, ...params, page: 1 };

    // Remove empty search parameters to avoid keeping old values
    if (newParams.search === '') {
      delete newParams.search;
    }
    if (newParams.tags === '') {
      delete newParams.tags;
    }
    if (newParams.storyMode === '') {
      delete newParams.storyMode;
    }

    // Reset pagination state
    setCurrentPage(1);
    setHasMore(true);
    setSearchParams(newParams);
  };

  // Remove handlePageChange - not needed for infinity scroll

  // Handle start chat
  const handleStartChat = async (characterId: string) => {
    if (!isAuthenticated) {
      alert('Please sign in to start chatting');
      return;
    }

    try {
      const chatSession = await characterService.initiateChat(characterId);
      console.log('Chat initiated:', chatSession);
      // Navigate to chat page with the new chat
      router.push(`/chat?id=${chatSession.id}`);
    } catch (error) {
      console.error('Failed to initiate chat:', error);
      alert('Failed to start chat. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading...</p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Eksplor</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-6 p-4 pt-0">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold mb-2">
              {isAuthenticated ? `Hai, ${user?.name}!` : 'Selamat datang di Bestieku'}
            </h1>
            <p className="text-muted-foreground">
              {isAuthenticated
                ? 'Pilih karakter untuk mulai mengobrol dengan teman AI dalam cerita yang imersif.'
                : 'Jelajahi karakter AI kami dan masuk untuk mulai mengobrol dalam cerita yang imersif.'
              }
            </p>
          </div>

          {/* Search and Filters */}
          <CharacterSearch
            onSearch={handleSearch}
            isLoading={charactersLoading}
            availableTags={availableTags}
          />

          {/* Characters Grid */}
          {charactersLoading ? (
            <div className="grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="bg-card border rounded-xl p-4 animate-pulse">
                  <div className="bg-muted/50 aspect-square rounded-lg mb-3" />
                  <div className="h-4 bg-muted/50 rounded mb-2" />
                  <div className="h-3 bg-muted/50 rounded mb-2" />
                  <div className="h-3 bg-muted/50 rounded w-2/3" />
                </div>
              ))}
            </div>
          ) : characters.length > 0 ? (
            <div className="grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4">
              {characters.map((character) => (
                <CharacterCard
                  key={character.id}
                  character={character}
                  onStartChat={handleStartChat}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">Tidak ada karakter yang cocok dengan kriteria kamu.</p>
              <p className="text-sm text-muted-foreground">Coba sesuaikan pencarian atau filter kamu.</p>
            </div>
          )}

          {/* Infinity Scroll Loader */}
          {characters.length > 0 && hasMore && (
            <div ref={loadMoreRef} className="flex justify-center py-8">
              {loadingMore ? (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-bestieku-primary"></div>
                  <span>Memunculkan lebih banyak karakter...</span>
                </div>
              ) : (
                <div className="text-muted-foreground text-sm">
                  Geser ke bawah untuk melihat lebih banyak karakter
                </div>
              )}
            </div>
          )}

          {/* End of results indicator */}
          {characters.length > 0 && !hasMore && !charactersLoading && (
            <div className="text-center py-8">
              <p className="text-muted-foreground text-sm">
              Kamu sudah melihat semua karakter! Coba atur ulang filter-mu untuk menemukan lebih banyak.
              </p>
            </div>
          )}

          {/* Sign in CTA for non-authenticated users */}
          {!isAuthenticated && characters.length > 0 && (
            <div className="mt-8 p-6 bg-muted/30 rounded-xl text-center">
              <h2 className="text-lg font-semibold mb-2">Ready to start chatting?</h2>
              <p className="text-muted-foreground mb-4">
                Sign in to unlock personalized conversations and save your chat history.
              </p>
            </div>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
