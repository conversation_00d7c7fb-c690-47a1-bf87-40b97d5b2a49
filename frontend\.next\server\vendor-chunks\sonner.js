"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ function __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            // Keep height up to date with the content in case it updates\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (event.button === 2) return; // Return early on right click\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (true) return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme :  false ? 0 : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": ({ id })=>id !== toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (true) return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;