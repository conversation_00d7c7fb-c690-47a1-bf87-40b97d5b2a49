import { useState, useEffect } from 'react';
import { CreditPackage } from '@/types/credit';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Coins, Calculator, TrendingDown } from 'lucide-react';

interface FlexibleCreditPurchaseProps {
  packages: CreditPackage[];
  onPurchase: (quantity: number) => void;
  isAuthenticated: boolean;
  loading: boolean;
}

export function FlexibleCreditPurchase({ 
  packages, 
  onPurchase, 
  isAuthenticated, 
  loading 
}: FlexibleCreditPurchaseProps) {
  const [quantity, setQuantity] = useState(100);
  const [inputValue, setInputValue] = useState('100');

  // Calculate price based on tiering
  const calculatePrice = (qty: number) => {
    if (!packages || packages.length === 0) {
      return {
        pricePerCredit: 150, // fallback price
        totalPrice: 150 * qty
      };
    }

    // Sort packages by quantity to ensure correct tiering
    const sortedPackages = [...packages].sort((a, b) => a.quantity - b.quantity);

    // Find the appropriate tier
    let pricePerCredit = sortedPackages[0]?.price || 150; // fallback

    for (const pkg of sortedPackages) {
      if (qty >= pkg.quantity) {
        pricePerCredit = pkg.price;
      }
    }

    return {
      pricePerCredit,
      totalPrice: pricePerCredit * qty
    };
  };

  const { pricePerCredit, totalPrice } = calculatePrice(quantity);

  // Get current tier info
  const getCurrentTier = () => {
    if (!packages || packages.length === 0) {
      return {
        current: null,
        next: null
      };
    }

    const sortedPackages = [...packages].sort((a, b) => a.quantity - b.quantity);

    for (let i = sortedPackages.length - 1; i >= 0; i--) {
      if (quantity >= sortedPackages[i].quantity) {
        return {
          current: sortedPackages[i],
          next: sortedPackages[i + 1] || null
        };
      }
    }

    return {
      current: sortedPackages[0],
      next: sortedPackages[1] || null
    };
  };

  const { current: currentTier, next: nextTier } = getCurrentTier();

  // Calculate savings compared to base price
  const basePricePerCredit = packages.find(p => p.quantity === 100)?.price || 150;
  const savingsPercentage = Math.round(((basePricePerCredit - pricePerCredit) / basePricePerCredit) * 100);

  const handleQuantityChange = (value: string) => {
    setInputValue(value);
    const numValue = parseInt(value) || 0;
    if (numValue >= 100 && numValue <= 10000) {
      setQuantity(numValue);
    }
  };

  const handleSliderChange = (value: number[]) => {
    const newQuantity = value[0];
    setQuantity(newQuantity);
    setInputValue(newQuantity.toString());
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            Beli B Coin
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-20 bg-muted rounded" />
            <div className="h-6 bg-muted rounded" />
            <div className="h-10 bg-muted rounded" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="w-5 h-5 text-bestieku-primary" />
          Beli B Coin Fleksibel
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Tentukan jumlah B Coin yang ingin dibeli. Semakin banyak, semakin hemat!
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Quantity Input */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Jumlah B Coin</label>
            <div className="flex items-center gap-2">
              {savingsPercentage > 0 && (
                <Badge className="bg-green-100 text-green-700 border-green-200">
                  <TrendingDown className="w-3 h-3 mr-1" />
                  Hemat {savingsPercentage}%
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex gap-3">
            <Input
              type="number"
              value={inputValue}
              onChange={(e) => handleQuantityChange(e.target.value)}
              min={100}
              max={10000}
              className="flex-1"
              placeholder="Minimal 100 B Coin"
            />
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Coins className="w-4 h-4" />
              B Coin
            </div>
          </div>
        </div>

        {/* Slider */}
        <div className="space-y-3">
          <Slider
            value={[quantity]}
            onValueChange={handleSliderChange}
            min={100}
            max={10000}
            step={25}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>100</span>
            <span>5,000</span>
            <span>10,000</span>
          </div>
        </div>

        {/* Price Calculation */}
        <div className="bg-muted/30 rounded-lg p-4 space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm">Harga per B Coin:</span>
            <span className="font-semibold">{formatPrice(pricePerCredit)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm">Jumlah B Coin:</span>
            <span className="font-semibold">{quantity.toLocaleString('id-ID')}</span>
          </div>
          <div className="border-t pt-3">
            <div className="flex justify-between items-center">
              <span className="font-semibold">Total Pembayaran:</span>
              <span className="text-xl font-bold text-bestieku-primary">
                {formatPrice(totalPrice)}
              </span>
            </div>
          </div>
        </div>

        {/* Tier Information */}
        {currentTier && (
          <div className="space-y-2">
            <div className="text-sm">
              <span className="text-muted-foreground">Tier saat ini: </span>
              <span className="font-medium">
                {currentTier.quantity.toLocaleString('id-ID')}+ B Coin ({formatPrice(currentTier.price)}/B Coin)
              </span>
            </div>
            {nextTier && quantity < nextTier.quantity && (
              <div className="text-xs text-muted-foreground">
                Beli {nextTier.quantity.toLocaleString('id-ID')}+ B Coin untuk harga {formatPrice(nextTier.price)}/B Coin
              </div>
            )}
          </div>
        )}

        {/* Purchase Button */}
        <Button
          onClick={() => onPurchase(quantity)}
          disabled={!isAuthenticated || quantity < 100}
          className="w-full bg-bestieku-primary hover:bg-bestieku-primary-dark text-white font-semibold h-12"
        >
          {isAuthenticated ? (
            <>
              <Coins className="w-5 h-5 mr-2" />
              Beli {quantity.toLocaleString('id-ID')} B Coin - {formatPrice(totalPrice)}
            </>
          ) : (
            'Masuk untuk Membeli B Coin'
          )}
        </Button>

        {/* Quick Amount Buttons */}
        <div className="grid grid-cols-4 gap-2">
          {[100, 500, 1000, 5000].map((amount) => (
            <Button
              key={amount}
              variant="outline"
              size="sm"
              onClick={() => {
                setQuantity(amount);
                setInputValue(amount.toString());
              }}
              className="text-xs"
            >
              {amount >= 1000 ? `${amount/1000}K` : amount}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
