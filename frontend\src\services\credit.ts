import { 
  CreditBalance, 
  CreditPackage, 
  BuyCreditRequest, 
  BuyCreditResponse 
} from '@/types/credit';
import { env } from '@/lib/env';

class CreditService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async getCreditBalance(): Promise<CreditBalance> {
    const response = await fetch(`${env.API_BASE_URL}/credits/balance`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch credit balance');
    }

    return await response.json();
  }

  async getCreditPricing(): Promise<CreditPackage[]> {
    const response = await fetch(`${env.API_BASE_URL}/credits/pricing`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch credit pricing');
    }

    return await response.json();
  }

  async buyCredits(data: BuyCreditRequest): Promise<BuyCreditResponse> {
    const response = await fetch(`${env.API_BASE_URL}/credits/buy`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to purchase credits');
    }

    return await response.json();
  }
}

export const creditService = new CreditService();
