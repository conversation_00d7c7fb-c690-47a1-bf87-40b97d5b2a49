'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X, Send, Minimize2, Volume2, VolumeX } from 'lucide-react';
import { Message } from '@/types/chat';
import { CharacterAsset } from '@/types/character';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { EmojiPicker } from './emoji-picker';

interface ImmersiveModeProps {
  isOpen: boolean;
  onClose: () => void;
  character: any;
  messages: Message[];
  streamingMessage: string;
  isStreaming: boolean;
  onSendMessage: (message: string) => void;
  disabled: boolean;
  characterAssets: CharacterAsset[];
  profileImageUrl: string | null;
}

export function ImmersiveMode({
  isOpen,
  onClose,
  character,
  messages,
  streamingMessage,
  isStreaming,
  onSendMessage,
  disabled,
  characterAssets,
  profileImageUrl
}: ImmersiveModeProps) {
  const [newMessage, setNewMessage] = useState('');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [showParticles, setShowParticles] = useState(true);
  const [characterMood, setCharacterMood] = useState<'neutral' | 'happy' | 'thinking'>('neutral');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Get profile assets for character display
  const profileAssets = characterAssets.filter(
    asset => asset.purpose === 'profile' && asset.isPublished
  );

  // Get background assets for ambient backgrounds
  const backgroundAssets = characterAssets.filter(
    asset => asset.purpose === 'background' && asset.isPublished
  );

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, streamingMessage]);

  // Cycle through character images every 10 seconds
  useEffect(() => {
    if (profileAssets.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentImageIndex(prev => (prev + 1) % profileAssets.length);
    }, 10000);

    return () => clearInterval(interval);
  }, [profileAssets.length]);

  // Focus input when opening
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Update character mood based on chat state
  useEffect(() => {
    if (isStreaming) {
      setCharacterMood('thinking');
    } else if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'user') {
        setCharacterMood('happy');
      } else {
        setCharacterMood('neutral');
      }
    }
  }, [isStreaming, messages]);

  // Handle keyboard shortcuts
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'F11') {
        e.preventDefault();
        // Toggle fullscreen if supported
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
      } else if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        setShowParticles(!showParticles);
      } else if (e.ctrlKey && e.key === 'm') {
        e.preventDefault();
        setIsAudioEnabled(!isAudioEnabled);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, showParticles, isAudioEnabled]);

  const handleSendMessage = () => {
    const message = newMessage.trim();
    if (!message || disabled) return;
    
    onSendMessage(message);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setNewMessage(prev => prev + emoji);
    inputRef.current?.focus();
  };

  if (!isOpen) return null;

  const currentProfileAsset = profileAssets[currentImageIndex];
  const currentProfileImage = currentProfileAsset?.url || profileImageUrl || character?.image;
  const currentBackground = backgroundAssets[0]?.url;

  return (
    <div className="fixed inset-0 z-50 bg-black">
      {/* Background Layer */}
      <div 
        className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20"
        style={{
          backgroundImage: currentBackground 
            ? `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)), url(${currentBackground})`
            : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      />

      {/* Dynamic Particle Effects */}
      {showParticles && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: characterMood === 'thinking' ? 30 : 20 }).map((_, i) => (
            <div
              key={i}
              className={`absolute rounded-full animate-pulse ${
                characterMood === 'thinking'
                  ? 'w-1 h-1 bg-blue-400/30'
                  : characterMood === 'happy'
                  ? 'w-2 h-2 bg-yellow-400/20'
                  : 'w-1 h-1 bg-white/20'
              }`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${characterMood === 'thinking' ? 1 + Math.random() * 2 : 2 + Math.random() * 3}s`
              }}
            />
          ))}

          {/* Special effects for thinking mode */}
          {characterMood === 'thinking' && (
            <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2">
              <div className="flex space-x-1">
                {[0, 1, 2].map((i) => (
                  <div
                    key={i}
                    className="w-2 h-2 bg-blue-400/50 rounded-full animate-bounce"
                    style={{
                      animationDelay: `${i * 0.2}s`,
                      animationDuration: '1s'
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Header Controls */}
      <div className="absolute top-4 left-4 right-4 flex justify-between items-center z-10">
        <div className="flex items-center gap-3">
          <h1 className="text-white text-xl font-bold">
            {character?.name || 'Character'}
          </h1>
          <div className="text-white/70 text-sm">
            Theater Mode
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsAudioEnabled(!isAudioEnabled)}
            className="text-white hover:bg-white/10"
            title={isAudioEnabled ? "Disable Audio" : "Enable Audio"}
          >
            {isAudioEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowParticles(!showParticles)}
            className="text-white hover:bg-white/10"
            title={showParticles ? "Hide Effects" : "Show Effects"}
          >
            ✨
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-white/10"
            title="Exit Immersive Mode"
          >
            <Minimize2 className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-full pt-16 pb-4 flex-col md:flex-row">
        {/* Character Display */}
        <div className="flex-1 flex items-center justify-center p-4 md:p-8">
          <div className="relative">
            {/* Character Image - Responsive */}
            <div className={`relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 shadow-2xl transition-all duration-1000 ${
              characterMood === 'thinking'
                ? 'border-blue-400/50 shadow-blue-400/20'
                : characterMood === 'happy'
                ? 'border-yellow-400/50 shadow-yellow-400/20'
                : 'border-white/20 shadow-white/10'
            }`}>
              {currentProfileImage && /\.mp4(\?|$)/i.test(currentProfileImage) ? (
                <video
                  src={currentProfileImage}
                  className="w-full h-full object-cover"
                  autoPlay
                  muted
                  loop
                  playsInline
                />
              ) : (
                <img
                  src={currentProfileImage}
                  alt={character?.name}
                  className="w-full h-full object-cover"
                />
              )}
              
              {/* Dynamic Breathing Animation Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-t from-transparent via-transparent transition-all duration-1000 ${
                characterMood === 'thinking'
                  ? 'to-blue-400/10 animate-pulse'
                  : characterMood === 'happy'
                  ? 'to-yellow-400/10 animate-bounce'
                  : 'to-white/5 animate-pulse'
              }`} />

              {/* Glow effect for active states */}
              {(characterMood === 'thinking' || characterMood === 'happy') && (
                <div className={`absolute inset-0 rounded-full ${
                  characterMood === 'thinking'
                    ? 'shadow-[0_0_50px_rgba(59,130,246,0.3)]'
                    : 'shadow-[0_0_50px_rgba(251,191,36,0.3)]'
                } animate-pulse`} />
              )}
            </div>

            {/* Enhanced Character Status */}
            <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
              <div className={`backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm transition-all duration-500 ${
                isStreaming
                  ? 'bg-blue-500/60 shadow-lg shadow-blue-500/20'
                  : 'bg-black/50'
              }`}>
                {isStreaming ? (
                  <div className="flex items-center gap-2">
                    <div className="flex space-x-1">
                      {[0, 1, 2].map((i) => (
                        <div
                          key={i}
                          className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"
                          style={{
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: '1s'
                          }}
                        />
                      ))}
                    </div>
                    <span className="ml-1">Thinking...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    Ready to chat
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Chat Panel - Responsive */}
        <div className="w-full md:w-96 bg-black/40 backdrop-blur-md border-t md:border-t-0 md:border-l border-white/10 flex flex-col max-h-[50vh] md:max-h-none">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.slice(-10).map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                    message.role === 'user'
                      ? 'bg-bestieku-primary text-white'
                      : 'bg-white/10 backdrop-blur-sm text-white border border-white/20'
                  }`}
                >
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    className="text-sm leading-relaxed prose prose-sm max-w-none prose-invert"
                  >
                    {message.content}
                  </ReactMarkdown>
                </div>
              </div>
            ))}

            {/* Streaming Message */}
            {isStreaming && streamingMessage && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-2xl px-4 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    className="text-sm leading-relaxed prose prose-sm max-w-none prose-invert"
                  >
                    {streamingMessage}
                  </ReactMarkdown>
                  <div className="inline-block w-2 h-4 bg-white/60 animate-pulse ml-1" />
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-4 border-t border-white/10">
            <div className="flex items-end space-x-2">
              <div className="flex-1 relative">
                <Input
                  ref={inputRef}
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={`Message ${character?.name || 'character'}...`}
                  disabled={disabled}
                  className="bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-white/60 rounded-2xl pr-12"
                />
                
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  <EmojiPicker
                    onEmojiSelect={handleEmojiSelect}
                    disabled={disabled}
                  />
                </div>
              </div>

              <Button
                onClick={handleSendMessage}
                disabled={!newMessage.trim() || disabled}
                className="bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-4 py-3"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
