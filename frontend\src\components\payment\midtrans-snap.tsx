import { useEffect, useRef } from 'react';
import { PaymentInvoice } from '@/types/payment';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CreditCard, X } from 'lucide-react';

interface MidtransSnapProps {
  invoice: PaymentInvoice | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (transactionId: string) => void;
  onError: (error: any) => void;
}

declare global {
  interface Window {
    snap: {
      pay: (token: string, options: any) => void;
      embed: (token: string, options: any) => void;
    };
  }
}

export function MidtransSnap({ invoice, isOpen, onClose, onSuccess, onError }: MidtransSnapProps) {
  const snapContainerRef = useRef<HTMLDivElement>(null);
  const isSnapLoaded = useRef(false);

  // Load Midtrans Snap script
  useEffect(() => {
    if (!isSnapLoaded.current) {
      const script = document.createElement('script');
      script.src = 'https://app.sandbox.midtrans.com/snap/snap.js'; // Use sandbox for staging
      script.setAttribute('data-client-key', process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY || '');
      script.onload = () => {
        isSnapLoaded.current = true;
      };
      document.head.appendChild(script);

      return () => {
        // Cleanup script when component unmounts
        const existingScript = document.querySelector('script[src*="snap.js"]');
        if (existingScript) {
          document.head.removeChild(existingScript);
        }
      };
    }
  }, []);

  // Initialize Snap payment when modal opens
  useEffect(() => {
    if (isOpen && invoice && isSnapLoaded.current && window.snap) {
      initializeSnap();
    }
  }, [isOpen, invoice, isSnapLoaded.current]);

  const initializeSnap = () => {
    if (!invoice || !window.snap) return;

    const snapOptions = {
      embedId: 'snap-container',
      onSuccess: (result: any) => {
        console.log('Payment success:', result);
        onSuccess(result.transaction_id);
        onClose();
      },
      onPending: (result: any) => {
        console.log('Payment pending:', result);
        // Handle pending payment if needed
      },
      onError: (result: any) => {
        console.log('Payment error:', result);
        onError(result);
      },
      onClose: () => {
        console.log('Payment popup closed');
        // Don't call onClose here as user might just close the payment popup
      }
    };

    // Use embed mode for better UX
    window.snap.embed(invoice.paymentToken, snapOptions);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getTotalAmount = () => {
    if (!invoice) return 0;
    const total = invoice.totals.find(t => t.type === 'total');
    return total ? total.amount : 0;
  };

  const getCreditsQuantity = () => {
    if (!invoice) return 0;
    const creditItem = invoice.items.find(item => item.id === 'credit');
    return creditItem ? creditItem.quantity : 0;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5 text-bestieku-primary" />
              Pembayaran Credits
            </DialogTitle>
            <Button
              onClick={onClose}
              variant="ghost"
              size="icon"
              className="h-8 w-8"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </DialogHeader>

        {invoice && (
          <div className="space-y-6">
            {/* Invoice Details */}
            <div className="bg-muted/30 rounded-lg p-4">
              <h3 className="font-semibold mb-3">Detail Pesanan</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Invoice:</span>
                  <div className="font-medium">{invoice.invoiceCode}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Credits:</span>
                  <div className="font-medium">{getCreditsQuantity().toLocaleString('id-ID')} credits</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Total:</span>
                  <div className="font-semibold text-bestieku-primary text-lg">
                    {formatPrice(getTotalAmount())}
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground">Status:</span>
                  <div className="font-medium text-yellow-600">Menunggu Pembayaran</div>
                </div>
              </div>
            </div>

            {/* Snap Payment Container */}
            <div className="space-y-4">
              <h3 className="font-semibold">Pilih Metode Pembayaran</h3>
              <div 
                id="snap-container" 
                ref={snapContainerRef}
                className="min-h-[400px] border rounded-lg"
              >
                {!isSnapLoaded.current && (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4"></div>
                      <p className="text-muted-foreground">Loading payment options...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Security Notice */}
            <div className="text-xs text-muted-foreground bg-blue-50 p-3 rounded-lg">
              <p className="font-medium mb-1">🔒 Pembayaran Aman</p>
              <p>Transaksi Anda dilindungi dengan enkripsi SSL dan diproses oleh Midtrans, payment gateway terpercaya di Indonesia.</p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
