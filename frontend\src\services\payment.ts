import { 
  PaymentInvoicesResponse,
  ValidatePaymentRequest,
  ValidatePaymentResponse,
  GetPaymentInvoicesParams
} from '@/types/payment';
import { env } from '@/lib/env';

class PaymentService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async getInvoices(params: GetPaymentInvoicesParams = {}): Promise<PaymentInvoicesResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());

    const response = await fetch(`${env.API_BASE_URL}/payments?${searchParams}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch invoices');
    }

    return await response.json();
  }

  async validatePayment(data: ValidatePaymentRequest): Promise<ValidatePaymentResponse> {
    const response = await fetch(`${env.API_BASE_URL}/payments/validate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to validate payment');
    }

    return await response.json();
  }
}

export const paymentService = new PaymentService();
