"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx":
/*!***********************************************************!*\
  !*** ./src/components/chat/character-profile-sidebar.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterProfileSidebar: () => (/* binding */ CharacterProfileSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _services_favorite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/favorite */ \"(app-pages-browser)/./src/services/favorite.ts\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _phone_number_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./phone-number-modal */ \"(app-pages-browser)/./src/components/chat/phone-number-modal.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* __next_internal_client_entry_do_not_use__ CharacterProfileSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-2xl flex size-full items-center justify-center rounded-full bg-muted \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nfunction CharacterProfileSidebar(param) {\n    let { characterId, messageCount, isOpen, onToggle, onBackgroundChange } = param;\n    var _getProfileAssets_currentProfileImageIndex, _getProfileAssets_modalImageIndex;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [assetsLoading, setAssetsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentProfileImageIndex, setCurrentProfileImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedBackground, setSelectedBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBackgroundSettings, setShowBackgroundSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavoriteLoading, setIsFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPhoneModal, setShowPhoneModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkCopied, setLinkCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalImageIndex, setModalImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            loadCharacter();\n            loadAssets();\n            checkFavoriteStatus();\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        characterId\n    ]);\n    // Keyboard navigation for image modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            if (!showImageModal) return;\n            const handleKeyDown = {\n                \"CharacterProfileSidebar.useEffect.handleKeyDown\": (e)=>{\n                    switch(e.key){\n                        case 'Escape':\n                            handleModalClose();\n                            break;\n                        case 'ArrowLeft':\n                            e.preventDefault();\n                            handleModalPrevious();\n                            break;\n                        case 'ArrowRight':\n                            e.preventDefault();\n                            handleModalNext();\n                            break;\n                    }\n                }\n            }[\"CharacterProfileSidebar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"CharacterProfileSidebar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"CharacterProfileSidebar.useEffect\"];\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        showImageModal,\n        modalImageIndex\n    ]);\n    const loadCharacter = async ()=>{\n        try {\n            setLoading(true);\n            const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterById(characterId);\n            setCharacter(characterData);\n        } catch (error) {\n            console.error('Failed to load character:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAssets = async ()=>{\n        try {\n            setAssetsLoading(true);\n            const assetsData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterAssets(characterId);\n            setAssets(assetsData);\n            // Auto-select first background if character has backgrounds and no background is currently selected\n            const backgroundAssets = assetsData.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n            if (backgroundAssets.length > 0 && selectedBackground === null) {\n                const defaultBackground = backgroundAssets[0].id;\n                setSelectedBackground(defaultBackground);\n                onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(defaultBackground);\n            }\n        } catch (error) {\n            console.error('Failed to load character assets:', error);\n        } finally{\n            setAssetsLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            const favoritesResponse = await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.getFavorites();\n            const isFavorited = favoritesResponse.data.some((fav)=>fav.id === characterId);\n            setIsFavorite(isFavorited);\n        } catch (error) {\n            console.error('Failed to check favorite status:', error);\n        // Don't show error toast for this, just keep default state\n        }\n    };\n    const formatDate = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true,\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.id\n            });\n        } catch (e) {\n            return 'Unknown';\n        }\n    };\n    // Helper functions for assets\n    const getProfileAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    };\n    const getBackgroundAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    };\n    const handleBackgroundChange = (backgroundId)=>{\n        setSelectedBackground(backgroundId);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(backgroundId);\n    };\n    const handleFavoriteToggle = async ()=>{\n        if (!character) return;\n        setIsFavoriteLoading(true);\n        try {\n            if (isFavorite) {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.removeFromFavorite(character.id);\n                setIsFavorite(false);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Dihapus dari favorit\", {\n                    description: \"\".concat(character.name, \" telah dihapus dari daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            } else {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.addToFavorite(character.id);\n                setIsFavorite(true);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Ditambahkan ke favorit\", {\n                    description: \"\".concat(character.name, \" telah ditambahkan ke daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal mengubah favorit\", {\n                description: error instanceof Error ? error.message : \"Terjadi kesalahan saat mengubah status favorit.\",\n                duration: 4000\n            });\n        } finally{\n            setIsFavoriteLoading(false);\n        }\n    };\n    const handleWhatsAppClick = ()=>{\n        if (!(character === null || character === void 0 ? void 0 : character.whatsappUrl)) return;\n        // Check if user has phone number\n        if (!(user === null || user === void 0 ? void 0 : user.phoneNumber)) {\n            setShowPhoneModal(true);\n            return;\n        }\n        // If user has phone number, proceed to WhatsApp\n        window.open(character.whatsappUrl, '_blank');\n    };\n    const handlePhoneModalSuccess = ()=>{\n        if (character === null || character === void 0 ? void 0 : character.whatsappUrl) {\n            window.open(character.whatsappUrl, '_blank');\n        }\n    };\n    const handleCopyLink = async ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        try {\n            await navigator.clipboard.writeText(characterUrl);\n            setLinkCopied(true);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Link disalin!\", {\n                description: \"Link karakter telah disalin ke clipboard. Bagikan ke teman-temanmu!\",\n                duration: 3000\n            });\n            // Reset copied state after 3 seconds\n            setTimeout(()=>setLinkCopied(false), 3000);\n        } catch (error) {\n            console.error('Failed to copy link:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal menyalin link\", {\n                description: \"Terjadi kesalahan saat menyalin link karakter.\",\n                duration: 3000\n            });\n        }\n    };\n    const handleShareToWhatsApp = ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        const message = \"Halo! Ayo chat dengan \".concat(character.name, \" di Bestieku! \\uD83E\\uDD16\\n\\n\").concat(character.description, \"\\n\\nKlik link ini: \").concat(characterUrl);\n        const whatsappUrl = \"https://wa.me/?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleImageClick = (index)=>{\n        setModalImageIndex(index);\n        setShowImageModal(true);\n        setImageLoading(true);\n    };\n    const handleModalClose = ()=>{\n        setShowImageModal(false);\n    };\n    const handleModalPrevious = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : profileAssets.length - 1);\n        setImageLoading(true);\n    };\n    const handleModalNext = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex < profileAssets.length - 1 ? modalImageIndex + 1 : 0);\n        setImageLoading(true);\n    };\n    // Touch handlers for swipe navigation\n    const handleTouchStart = (e)=>{\n        setTouchEnd(null);\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && getProfileAssets().length > 1) {\n            handleModalNext();\n        }\n        if (isRightSwipe && getProfileAssets().length > 1) {\n            handleModalPrevious();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-32 bg-muted rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    if (!character) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Karakter tidak ditemukan\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    onClick: onToggle,\n                    className: \"w-full\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 60\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 relative flex shrink-0 overflow-hidden rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                        src: (()=>{\n                                            // Get first profile image asset (not video)\n                                            const profileAssets = getProfileAssets().filter((asset)=>asset.type === 'image');\n                                            return profileAssets.length > 0 ? profileAssets[0].url : character.image;\n                                        })(),\n                                        alt: character.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-1\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this),\n                                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mb-3 font-medium\",\n                                    children: character.title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2 mb-3\",\n                                    children: [\n                                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-bestieku-primary text-white\",\n                                            children: \"Mode Cerita\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: character.status\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this),\n                                character.whatsappUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        onClick: handleWhatsAppClick,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chat di WhatsApp\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: isFavorite ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleFavoriteToggle,\n                                        disabled: isFavoriteLoading,\n                                        className: \"flex items-center gap-2 \".concat(isFavorite ? 'bg-red-500 hover:bg-red-600 text-white' : 'hover:bg-red-50 hover:text-red-600 hover:border-red-300'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isFavorite ? 'fill-current' : '')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            isFavoriteLoading ? 'Loading...' : isFavorite ? 'Favorit' : 'Tambah ke Favorit'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleCopyLink,\n                                            className: \"flex items-center gap-2 flex-1\",\n                                            children: linkCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Link Disalin!\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Salin Link\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShareToWhatsApp,\n                                            className: \"flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"WhatsApp\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Tentang\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                    children: character.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 13\n                        }, this),\n                        character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Tag\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: character.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Galeri Profil\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            onTouchStart: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onTouchEnd: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            children: [\n                                                (()=>{\n                                                    const asset = getProfileAssets()[currentProfileImageIndex];\n                                                    const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                                    return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        src: url,\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        muted: true,\n                                                        playsInline: true,\n                                                        preload: \"metadata\",\n                                                        poster: url + \"#t=0.1\",\n                                                        onLoadedData: (e)=>{\n                                                            e.currentTarget.currentTime = 0;\n                                                        },\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: url,\n                                                        alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })(),\n                                                getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-x-0 bottom-2 flex justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex > 0 ? currentProfileImageIndex - 1 : getProfileAssets().length - 1),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex < getProfileAssets().length - 1 ? currentProfileImageIndex + 1 : 0),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this),\n                                        ((_getProfileAssets_currentProfileImageIndex = getProfileAssets()[currentProfileImageIndex]) === null || _getProfileAssets_currentProfileImageIndex === void 0 ? void 0 : _getProfileAssets_currentProfileImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground text-center\",\n                                            children: getProfileAssets()[currentProfileImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 21\n                                        }, this),\n                                        getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-1\",\n                                            children: getProfileAssets().map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentProfileImageIndex ? 'bg-bestieku-primary' : 'bg-muted'),\n                                                    onClick: ()=>setCurrentProfileImageIndex(index)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 65\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Statistik Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Pesan Anda\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Pesan\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-bestieku-primary\",\n                                                    children: character.messageCount > 999 ? '999+' : character.messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Jenis Karakter\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: character.storyMode ? 'Story' : 'Chat'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Info Karakter\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Dibuat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Diperbarui\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Latar Belakang Chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: ()=>setShowBackgroundSettings(!showBackgroundSettings),\n                                            className: \"w-6 h-6\",\n                                            children: showBackgroundSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 47\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 83\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 17\n                                }, this),\n                                showBackgroundSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === null ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                            onClick: ()=>handleBackgroundChange(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-8 bg-gradient-to-b from-background to-muted/20 rounded border\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Latar belakang polos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 21\n                                        }, this),\n                                        getBackgroundAssets().map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === asset.id ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                                onClick: ()=>handleBackgroundChange(asset.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /\\.mp4(\\?|$)/i.test(asset.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: asset.url,\n                                                            className: \"w-12 h-8 object-cover rounded border pointer-events-none\",\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            preload: \"metadata\",\n                                                            onMouseEnter: (e)=>e.currentTarget.play(),\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.pause();\n                                                                e.currentTarget.currentTime = 0;\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: asset.url,\n                                                            alt: asset.caption || 'Background',\n                                                            className: \"w-12 h-8 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: asset.caption || 'Latar Belakang Kustom'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Latar belakang karakter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, asset.id, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 23\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 68\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this),\n            showImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl max-h-[90vh] w-full mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModalClose,\n                                className: \"absolute top-4 right-4 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalPrevious,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalNext,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-black rounded-lg overflow-hidden\",\n                                onTouchStart: handleTouchStart,\n                                onTouchMove: handleTouchMove,\n                                onTouchEnd: handleTouchEnd,\n                                children: [\n                                    (()=>{\n                                        const asset = getProfileAssets()[modalImageIndex];\n                                        const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                        return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: url,\n                                            className: \"w-full max-h-[80vh] object-contain\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            muted: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: url,\n                                            alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                            className: \"w-full max-h-[80vh] object-contain\",\n                                            onLoad: ()=>setImageLoading(false),\n                                            onError: ()=>setImageLoading(false)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 19\n                                        }, this);\n                                    })(),\n                                    imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center bg-black/50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 847,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((_getProfileAssets_modalImageIndex = getProfileAssets()[modalImageIndex]) === null || _getProfileAssets_modalImageIndex === void 0 ? void 0 : _getProfileAssets_modalImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-center\",\n                                            children: getProfileAssets()[modalImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm\",\n                                children: [\n                                    modalImageIndex + 1,\n                                    \" / \",\n                                    getProfileAssets().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        onClick: handleModalClose\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 870,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 786,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phone_number_modal__WEBPACK_IMPORTED_MODULE_8__.PhoneNumberModal, {\n                isOpen: showPhoneModal,\n                onClose: ()=>setShowPhoneModal(false),\n                onSuccess: handlePhoneModalSuccess,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 878,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterProfileSidebar, \"3mCKT5IcF9Xt3P2BS7RMtQHfEtw=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c1 = CharacterProfileSidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"CharacterProfileSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\n"));

/***/ })

});