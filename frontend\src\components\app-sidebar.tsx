"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { useTheme } from "next-themes"
import Image from "next/image"
import { useState, useEffect } from "react"
import {
  Heart,
  Home,
  MessageCircle,
  Settings,
  BookOpen,
} from "lucide-react"

import { NavUser } from "@/components/nav-user"
import { CreditBalanceWidget } from "@/components/credit/credit-balance-widget"
import { ModeToggleEnhanced } from "@/components/mode-toggle"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar"

const data = {
  navMain: [
    {
      title: "Eksplor",
      url: "/dashboard",
      icon: Home,
      description: "Jelajahi karakter AI",
    },
    {
      title: "Chat Saya",
      url: "/chat",
      icon: MessageCircle,
      description: "Lanjutkan percakapan",
    },
    {
      title: "Favorit",
      url: "/favorites",
      icon: Heart,
      description: "Karakter tersimpan",
    },
    {
      title: "Pengaturan",
      url: "/settings",
      icon: Settings,
      description: "Akun & preferensi",
    },
    {
      title: "Panduan",
      url: "/guide",
      icon: BookOpen,
      description: "Cara pakai Bestieku",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const { theme } = useTheme()
  // Menghapus state mounted agar logo bisa langsung tampil tanpa delay

  return (
    <Sidebar variant="inset" {...props} className="border-r-0">
      <SidebarHeader className="border-b-0 px-4 py-4">
        <a href="/dashboard" className="flex items-center justify-start w-full px-3">
          {/* Render kedua logo dan tampilkan sesuai theme dengan className dark: */}
          <Image
            src="/bestiekulight.png"
            alt="Bestieku Logo Light"
            width={300}
            height={112}
            style={{ width: '170px', height: 'auto' }}
            className="transition-opacity duration-200 hover:opacity-80 dark:hidden"
            priority
          />
          <Image
            src="/blue2.png"
            alt="Bestieku Logo Dark"
            width={300}
            height={112}
            style={{ width: '170px', height: 'auto' }}
            className="transition-opacity duration-200 hover:opacity-80 hidden dark:block"
            priority
          />
        </a>
      </SidebarHeader>

      <SidebarContent className="px-4">
        {/* Main Navigation */}
        <div className="space-y-2 mb-8">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3">
            Navigasi
          </h3>
          {data.navMain.map((item) => {
            const isActive = pathname === item.url
            return (
              <div key={item.title}>
                <a
                  href={item.url}
                  className={`flex items-center gap-3 px-3 h-12 rounded-xl transition-all duration-200 hover:bg-gradient-to-r hover:from-bestieku-primary/10 hover:to-bestieku-primary-dark/10 hover:scale-[1.02] group ${
                    isActive ? 'bg-gradient-to-r from-bestieku-primary/20 to-bestieku-primary-dark/20 border border-bestieku-primary/30' : ''
                  }`}
                >
                  <div className={`p-2 rounded-lg ${
                    isActive
                      ? 'bg-bestieku-primary text-white shadow-md'
                      : 'bg-muted/50 text-muted-foreground group-hover:bg-bestieku-primary/20 group-hover:text-bestieku-primary'
                  } transition-all duration-200`}>
                    <item.icon className="size-4" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">
                      {item.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {item.description}
                    </div>
                  </div>
                </a>
              </div>
            )
          })}
        </div>


      </SidebarContent>

      <SidebarFooter className="p-4 border-t-0 space-y-3">
        {/* Theme Toggle */}
        <ModeToggleEnhanced />
        
        {/* Credit Balance Widget */}
        <CreditBalanceWidget />

        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
