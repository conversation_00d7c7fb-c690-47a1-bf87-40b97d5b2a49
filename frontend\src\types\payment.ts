export interface PaymentInvoiceItem {
  id: string;
  description: string;
  quantity: number;
  price: number;
}

export interface PaymentInvoiceTotal {
  type: string;
  description: string;
  amount: number;
}

export interface PaymentCustomerDetails {
  name: string;
  email: string;
  phone: string;
}

export interface PaymentInvoice {
  id: string;
  userId: string;
  customerDetails: PaymentCustomerDetails;
  orderId: string;
  items: PaymentInvoiceItem[];
  totals: PaymentInvoiceTotal[];
  paidDate: string | null;
  metadata: Record<string, any>;
  status: string; // More flexible to handle any status from API
  invoiceCode: string;
  invoiceLink: string;
  paymentLink: string;
  paymentToken: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentInvoicesResponse {
  data: PaymentInvoice[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface ValidatePaymentRequest {
  invoiceId: string;
  transactionId: string;
  invoiceCode: string;
}

export interface ValidatePaymentResponse {
  status: string;
  message: string;
}

export interface GetPaymentInvoicesParams {
  page?: number;
  limit?: number;
}
