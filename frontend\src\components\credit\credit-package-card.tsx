import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CreditPackage } from "@/types/credit";
import { Coins, Star, Zap } from "lucide-react";

interface CreditPackageCardProps {
  package: CreditPackage;
  onPurchase: (quantity: number) => void;
  isAuthenticated: boolean;
  popular?: boolean;
}

export function CreditPackageCard({ 
  package: pkg, 
  onPurchase, 
  isAuthenticated, 
  popular = false 
}: CreditPackageCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatQuantity = (quantity: number) => {
    if (quantity >= 1000) {
      return `${(quantity / 1000).toFixed(0)}K`;
    }
    return quantity.toString();
  };

  const getPricePerCredit = () => {
    return pkg.price;
  };

  const getSavingsPercentage = () => {
    // Assuming base price is 1500 (from the first package)
    const basePrice = 1500;
    const savings = ((basePrice - pkg.price) / basePrice) * 100;
    return Math.round(savings);
  };

  const getCardStyle = () => {
    if (popular) {
      return "bg-gradient-to-br from-bestieku-primary/20 to-bestieku-primary/10 border-bestieku-primary border-2 relative";
    }
    return "bg-card border hover:border-bestieku-primary/50 transition-all duration-200 hover:shadow-lg";
  };

  const getButtonStyle = () => {
    if (popular) {
      return "w-full bg-bestieku-primary hover:bg-bestieku-primary-dark text-white font-semibold";
    }
    return "w-full";
  };

  return (
    <Card className={getCardStyle()}>
      {popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-bestieku-primary text-white font-semibold px-3 py-1">
            <Star className="w-3 h-3 mr-1" />
            Terpopuler
          </Badge>
        </div>
      )}
      
      <CardHeader className="text-center pb-4">
        <div className="w-12 h-12 bg-bestieku-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
          <Coins className="w-6 h-6 text-bestieku-primary" />
        </div>
        <CardTitle className="text-xl">
          {formatQuantity(pkg.quantity)} Credits
        </CardTitle>
        <div className="space-y-1">
          <div className="text-2xl font-bold text-bestieku-primary">
            {formatPrice(pkg.price * pkg.quantity)}
          </div>
          <div className="text-sm text-muted-foreground">
            {formatPrice(pkg.price)} per credit
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Savings Badge */}
        {pkg.price < 1500 && (
          <div className="text-center">
            <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
              <Zap className="w-3 h-3 mr-1" />
              Hemat {getSavingsPercentage()}%
            </Badge>
          </div>
        )}
        
        {/* Features */}
        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-1.5 h-1.5 bg-bestieku-primary rounded-full" />
            <span>Credits tidak expired</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-1.5 h-1.5 bg-bestieku-primary rounded-full" />
            <span>Bisa digunakan untuk semua karakter</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-1.5 h-1.5 bg-bestieku-primary rounded-full" />
            <span>Instant activation</span>
          </div>
        </div>
        
        <Button
          onClick={() => onPurchase(pkg.quantity)}
          className={getButtonStyle()}
          disabled={!isAuthenticated}
        >
          {isAuthenticated ? 'Beli Sekarang' : 'Masuk untuk Membeli'}
        </Button>
      </CardContent>
    </Card>
  );
}
