'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AlertCircle, Home, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

function AuthErrorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const errorParam = searchParams.get('error');
    setError(errorParam || 'unknown_error');
  }, [searchParams]);

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case 'access_denied':
        return 'Anda membatalkan proses login dengan Google.';
      case 'missing_code':
        return 'Kode otorisasi tidak ditemukan.';
      case 'token_exchange_failed':
        return 'Gagal menukar kode otorisasi dengan token.';
      case 'missing_id_token':
        return 'Token ID tidak ditemukan dari Google.';
      case 'auth_failed':
        return 'Gagal melakukan autentikasi dengan server.';
      case 'token_transfer_failed':
        return 'Gagal mentransfer token ke browser.';
      case 'internal_error':
        return 'Terjadi kesalahan internal server.';
      default:
        return 'Terjadi kesalahan yang tidak diketahui.';
    }
  };

  const getErrorTitle = (errorCode: string) => {
    switch (errorCode) {
      case 'access_denied':
        return 'Login Dibatalkan';
      case 'missing_code':
      case 'token_exchange_failed':
      case 'missing_id_token':
        return 'Kesalahan OAuth';
      case 'auth_failed':
        return 'Autentikasi Gagal';
      case 'token_transfer_failed':
        return 'Transfer Token Gagal';
      case 'internal_error':
        return 'Kesalahan Server';
      default:
        return 'Login Gagal';
    }
  };

  const handleRetry = () => {
    // Redirect back to the page that initiated login
    router.push('/dashboard');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 p-4">
      <div className="max-w-md w-full space-y-8 text-center">
        <div className="space-y-6">
          {/* Error Icon */}
          <div className="flex justify-center">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="w-10 h-10 text-red-600" />
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
              {getErrorTitle(error)}
            </h1>
            <p className="text-muted-foreground">
              {getErrorMessage(error)}
            </p>
          </div>

          {/* Error Code */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-600">
                Kode Error: <code className="font-mono">{error}</code>
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3 pt-4">
            <Button
              onClick={handleRetry}
              className="w-full bg-bestieku-primary hover:bg-bestieku-primary-dark text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Coba Lagi
            </Button>
            
            <Button
              onClick={handleGoHome}
              variant="outline"
              className="w-full"
            >
              <Home className="w-4 h-4 mr-2" />
              Kembali ke Beranda
            </Button>
          </div>

          {/* Help Text */}
          <div className="pt-4 border-t border-muted">
            <p className="text-xs text-muted-foreground">
              Jika masalah terus berlanjut, silakan hubungi tim dukungan kami.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 p-4">
        <div className="max-w-md w-full space-y-8 text-center">
          <div className="space-y-6">
            <div className="flex justify-center">
              <div className="w-20 h-20 bg-muted rounded-full flex items-center justify-center animate-pulse">
                <AlertCircle className="w-10 h-10 text-muted-foreground" />
              </div>
            </div>
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-foreground">
                Memuat...
              </h1>
              <p className="text-muted-foreground">
                Sedang memproses informasi error...
              </p>
            </div>
          </div>
        </div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
