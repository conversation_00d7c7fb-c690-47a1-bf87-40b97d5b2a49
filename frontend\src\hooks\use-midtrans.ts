import { useEffect, useRef } from 'react';

declare global {
  interface Window {
    snap: {
      pay: (token: string, options?: {
        onSuccess?: (result: any) => void;
        onPending?: (result: any) => void;
        onError?: (result: any) => void;
        onClose?: () => void;
      }) => void;
    };
  }
}

export function useMidtrans() {
  const isSnapLoaded = useRef(false);

  useEffect(() => {
    // Check if script already exists
    const existingScript = document.querySelector('script[src*="snap.js"]');
    if (existingScript) {
      isSnapLoaded.current = true;
      return;
    }

    // Load Midtrans SNAP script
    const script = document.createElement('script');
    script.src = 'https://app.sandbox.midtrans.com/snap/snap.js'; // Sandbox untuk staging
    script.setAttribute('data-client-key', process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY || '');
    
    script.onload = () => {
      isSnapLoaded.current = true;
    };
    
    script.onerror = () => {
      console.error('Failed to load Midtrans SNAP script');
    };

    document.head.appendChild(script);
  }, []);

  const payWithSnap = (token: string, callbacks: {
    onSuccess?: (result: any) => void;
    onPending?: (result: any) => void;
    onError?: (result: any) => void;
    onClose?: () => void;
  }) => {
    if (!isSnapLoaded.current || !window.snap) {
      // Return error instead of alert, let caller handle it
      if (callbacks.onError) {
        callbacks.onError({ message: 'Payment system sedang loading, silakan coba lagi.' });
      }
      return;
    }

    // Langsung munculin modal SNAP Midtrans
    window.snap.pay(token, callbacks);
  };

  return {
    isSnapLoaded: isSnapLoaded.current,
    payWithSnap,
  };
}
