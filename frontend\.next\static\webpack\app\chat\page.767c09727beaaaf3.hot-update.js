"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./reset-chat-modal */ \"(app-pages-browser)/./src/components/chat/reset-chat-modal.tsx\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, profileImageUrl, characterName } = param;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c2 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { streamingMessage, profileImageUrl, characterName } = param;\n    _s1();\n    const [thinkingText, setThinkingText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('Berpikir');\n    // Cycle through thinking messages when not streaming actual content\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamingMessage.useEffect\": ()=>{\n            if (!streamingMessage) {\n                const messages = [\n                    'Berpikir',\n                    'Merenungkan',\n                    'Mempertimbangkan',\n                    'Memikirkan jawaban'\n                ];\n                let index = 0;\n                const interval = setInterval({\n                    \"StreamingMessage.useEffect.interval\": ()=>{\n                        index = (index + 1) % messages.length;\n                        setThinkingText(messages[index]);\n                    }\n                }[\"StreamingMessage.useEffect.interval\"], 2000);\n                return ({\n                    \"StreamingMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMessage.useEffect\"];\n            }\n        }\n    }[\"StreamingMessage.useEffect\"], [\n        streamingMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: thinkingText\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n}, \"W7FciA9Z3UpxXy1pJlVC9GvQ8tw=\"));\n_c3 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount, profileImageUrl } = param;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                src: profileImageUrl,\n                                alt: character.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Mode Cerita\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages, profileImageUrl } = param;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                        src: profileImageUrl,\n                        alt: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s2((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s2();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    // Detect mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInput.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInput.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInput.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInput.useEffect\"];\n        }\n    }[\"ChatInput.useEffect\"], []);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    const handleEmojiSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleEmojiSelect]\": (emoji)=>{\n            const newValue = newMessage + emoji;\n            setNewMessage(newValue);\n            messageRef.current = newValue;\n            // Focus back to input after emoji selection\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatInput.useCallback[handleEmojiSelect]\"], [\n        newMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 \".concat(isMobile ? 'p-3 pb-6' : 'p-4'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"\".concat(isMobile ? 'pr-20' : 'pr-24', \" rounded-2xl border-2 focus:border-bestieku-primary transition-colors \").concat(isMobile ? 'py-3 text-base' : 'py-3'),\n                                style: isMobile ? {\n                                    fontSize: '16px'\n                                } : {}\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_9__.EmojiPicker, {\n                                        onEmojiSelect: handleEmojiSelect,\n                                        disabled: disabled\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 \".concat(isMobile ? 'px-4 py-3' : 'px-6 py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 383,\n        columnNumber: 5\n    }, undefined);\n}, \"0TgdyP+W7Ya7Fi9I41+kS8az6FU=\"));\n_c6 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s3((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c7 = _s3((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5;\n    _s3();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileImageUrl, setProfileImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show reset confirmation modal\n    const handleResetClick = ()=>{\n        setShowResetModal(true);\n    };\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Close modal\n            setShowResetModal(false);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                        // Get profile image from assets (prefer static image over video)\n                        const profileAssets = assets.filter({\n                            \"ChatInterface.useEffect.loadCharacterData.profileAssets\": (asset)=>asset.purpose === 'profile' && asset.type === 'image' && asset.isPublished\n                        }[\"ChatInterface.useEffect.loadCharacterData.profileAssets\"]);\n                        if (profileAssets.length > 0) {\n                            setProfileImageUrl(profileAssets[0].url);\n                        } else {\n                            // Fallback to character.image if no profile assets\n                            setProfileImageUrl(characterData.image);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Reset all streaming states when chat changes\n            setIsStreaming(false);\n            setStreamingMessage('');\n            setSending(false);\n            // Cleanup any existing connections and timeouts\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n                streamConnectionRef.current = null;\n            }\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n                streamingTimeoutRef.current = null;\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n                streamTimeoutRef.current = null;\n            }\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const currentChatId = chat.id; // Capture current chat ID\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Show AI thinking for 3-9 seconds before sending to backend\n                setIsStreaming(true);\n                setStreamingMessage('');\n                const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms\n                console.log(\"AI thinking for \".concat(thinkingDelay, \"ms before processing...\"));\n                await new Promise({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (resolve)=>setTimeout(resolve, thinkingDelay)\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Check if chat hasn't changed during the thinking delay\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed during thinking delay, aborting message send');\n                    return;\n                }\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Check again if chat hasn't changed after API call\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed after message send, aborting streaming');\n                    return;\n                }\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming(currentChatId);\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Only show error and remove message if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                    alert('Failed to send message. Please try again.');\n                }\n            } finally{\n                // Only reset sending state if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setSending(false);\n                }\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async (expectedChatId)=>{\n        const targetChatId = expectedChatId || chat.id;\n        console.log('Starting stream for chat:', targetChatId);\n        // Check if chat hasn't changed before starting stream\n        if (expectedChatId && chat.id !== expectedChatId) {\n            console.log('Chat changed before streaming started, aborting');\n            return;\n        }\n        // isStreaming already set to true in handleSendMessage\n        // setStreamingMessage(''); // Keep current thinking state\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            // Check if chat hasn't changed during streaming\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed during streaming, ignoring SSE event');\n                return;\n            }\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message only if chat hasn't changed\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        var _data_data;\n                        const finalMessage = {\n                            id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                            role: 'assistant',\n                            content: currentStreamingMessage,\n                            contentType: 'text',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                finalMessage\n                            ]);\n                        setStreamingMessage('');\n                        setIsStreaming(false);\n                    }\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                setStreamingMessage('');\n            }\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error, streaming was actually active, and chat hasn't changed\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming && (!expectedChatId || chat.id === expectedChatId)) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Check one more time if chat hasn't changed before creating connection\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed before creating stream connection, aborting');\n                return;\n            }\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(targetChatId, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active, connection exists, and chat hasn't changed\n                if (isStreaming && streamConnectionRef.current && (!expectedChatId || chat.id === expectedChatId)) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                alert('Failed to establish connection for AI response. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 894,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 892,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 891,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 902,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 900,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 890,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full \".concat(isMobile ? 'h-16 p-3' : 'h-20 p-4'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 935,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 920,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleResetClick,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 918,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 982,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    profileImageUrl: profileImageUrl,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                profileImageUrl: profileImageUrl,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1021,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 969,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1025,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__.ResetChatModal, {\n                isOpen: showResetModal,\n                onClose: ()=>setShowResetModal(false),\n                onConfirm: handleDeleteMessages,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || ((_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name),\n                messageCount: messages.length,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1033,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 916,\n        columnNumber: 5\n    }, undefined);\n}, \"TWWU90YYrjWW8OsXbApf3QOv7v8=\")), \"TWWU90YYrjWW8OsXbApf3QOv7v8=\");\n_c8 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"StreamingMessage\");\n$RefreshReg$(_c4, \"WelcomeMessage\");\n$RefreshReg$(_c5, \"OpeningScene\");\n$RefreshReg$(_c6, \"ChatInput\");\n$RefreshReg$(_c7, \"ChatInterface$memo\");\n$RefreshReg$(_c8, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});