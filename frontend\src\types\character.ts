export interface CharacterAsset {
  id: string;
  caption: string;
  purpose: 'avatar' | 'profile' | 'background';
  key: string;
  url: string;
  type: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Character {
  id: string;
  name: string;
  title: string;
  description: string;
  storyMode: boolean;
  status: string;
  image: string;
  openingScene: string;
  tags: string[];
  messageCount: number;
  whatsappUrl: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface CharactersResponse {
  data: Character[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface GetCharactersParams {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string;
  storyMode?: string;
  sortBy?: 'createdAt' | 'name' | 'messageCount' | 'favoriteCount';
  sortDirection?: 'asc' | 'desc';
}

export interface ChatSession {
  id: string;
  userId: string;
  characterId: string;
  messageCount: number;
  platform: string;
  createdAt: string;
  updatedAt: string;
}
