'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  AuthContextType, 
  User, 
  SignInRequest, 
  SignUpRequest, 
  VerifyOTPRequest, 
  UpdateProfileRequest 
} from '@/types/auth';
import { authService } from '@/services/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated on app load
    const initializeAuth = async () => {
      try {
        if (authService.isTokenValid()) {
          const userProfile = await authService.getProfile();
          setUser(userProfile);
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        authService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (data: SignInRequest): Promise<void> => {
    try {
      await authService.signIn(data);
    } catch (error) {
      throw error;
    }
  };

  const signUp = async (data: SignUpRequest): Promise<void> => {
    try {
      await authService.signUp(data);
    } catch (error) {
      throw error;
    }
  };

  const verifyOTP = async (data: VerifyOTPRequest): Promise<void> => {
    try {
      await authService.verifyOTP(data);
      // After successful OTP verification, get user profile
      const userProfile = await authService.getProfile();
      setUser(userProfile);
    } catch (error) {
      throw error;
    }
  };

  const logout = (): void => {
    authService.logout();
    setUser(null);
  };

  const updateProfile = async (data: UpdateProfileRequest): Promise<void> => {
    try {
      const updatedUser = await authService.updateProfile(data);
      setUser(updatedUser);
    } catch (error) {
      throw error;
    }
  };

  const uploadProfileImage = async (file: File): Promise<string> => {
    try {
      const imageUrl = await authService.uploadProfileImage(file);
      // Refresh user profile to get updated image
      const userProfile = await authService.getProfile();
      setUser(userProfile);
      return imageUrl;
    } catch (error) {
      throw error;
    }
  };

  const signInWithGoogle = (): void => {
    authService.initiateGoogleLogin();
  };

  const initializeAfterLogin = async (): Promise<void> => {
    try {
      if (authService.isTokenValid()) {
        const userProfile = await authService.getProfile();
        setUser(userProfile);
      }
    } catch (error) {
      console.error('Failed to initialize after login:', error);
      authService.logout();
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    signIn,
    signUp,
    verifyOTP,
    logout,
    updateProfile,
    uploadProfileImage,
    signInWithGoogle,
    initializeAfterLogin,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
