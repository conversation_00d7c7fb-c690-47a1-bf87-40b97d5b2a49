'use client';

import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { HelpCircle, Search, MessageCircle, Heart, ChevronDown, Sparkles } from 'lucide-react';

export default function GuidePage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Panduan</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-6 p-4 pt-0">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold mb-2">Panduan Pengguna</h1>
            <p className="text-muted-foreground">
              Kenali Bestieku dan pelajari cara memulai percakapan seru dengan karakter AI favoritmu.
            </p>
          </div>

          {/* Content with Cards for better visual grouping */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg">Apa itu Bestieku?</CardTitle>
                <CardDescription>
                  Bestieku adalah platform untuk mengobrol dengan karakter AI yang imersif. Kamu bisa memilih karakter favorit, memulai percakapan, dan menjelajahi cerita yang seru. Beberapa karakter memiliki Mode Cerita untuk pengalaman yang lebih terarah.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Mulai dari sini</CardTitle>
                <CardDescription>Langkah cepat untuk mulai menggunakan Bestieku</CardDescription>
              </CardHeader>
              <CardContent>
                <ol className="list-decimal list-inside space-y-2 text-muted-foreground">
                  <li>Jelajahi karakter di menu Eksplor. Gunakan pencarian, filter tag, dan Mode Cerita.</li>
                  <li>Klik kartu karakter untuk memulai chat. Lihat info karakter di panel kanan.</li>
                  <li>Lanjutkan percakapan di menu Chat Saya.</li>
                  <li>Tambah karakter ke Favorit untuk akses cepat.</li>
                </ol>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tips Percakapan</CardTitle>
                <CardDescription>Supaya obrolanmu lebih nyambung dan asyik</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                  <li>Mulai dengan sapaan dan perkenalan singkat.</li>
                  <li>Gunakan bahasa yang jelas agar AI memahami konteks.</li>
                  <li>Untuk karakter Mode Cerita, ikuti arahan cerita agar pengalaman lebih seru.</li>
                  <li>Jika ingin memulai ulang, gunakan tombol Reset di header chat (akan menghapus riwayat percakapan).</li>
                </ul>
              </CardContent>
            </Card>

            {/* Quick Tips */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Sparkles className="w-4 h-4" /> Tips Cepat
                </CardTitle>
                <CardDescription>Beberapa hal yang memudahkan kamu memulai</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-3">
                  <div className="p-3 rounded-lg border bg-background/50">
                    <div className="flex items-center gap-2 mb-2 text-bestieku-primary">
                      <Search className="w-4 h-4" />
                      <span className="text-sm font-medium">Eksplor</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Gunakan pencarian & tag untuk menemukan karakter yang cocok.</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background/50">
                    <div className="flex items-center gap-2 mb-2 text-bestieku-primary">
                      <MessageCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Mulai Chat</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Klik kartu karakter untuk mulai mengobrol seketika.</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background/50">
                    <div className="flex items-center gap-2 mb-2 text-bestieku-primary">
                      <Heart className="w-4 h-4" />
                      <span className="text-sm font-medium">Favorit</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Simpan karakter favorit untuk akses cepat berikutnya.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* FAQ */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <HelpCircle className="w-4 h-4" /> FAQ
                </CardTitle>
                <CardDescription>Pertanyaan yang sering ditanyakan</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Collapsible>
                  <CollapsibleTrigger className="w-full text-left flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50">
                    <span className="font-medium text-sm">Apakah semua karakter punya Mode Cerita?</span>
                    <ChevronDown className="w-4 h-4 text-muted-foreground" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="px-3 pt-2 text-sm text-muted-foreground">
                    Tidak semua. Beberapa karakter memiliki Mode Cerita untuk pengalaman naratif yang lebih terarah, lainnya fokus pada percakapan bebas.
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible>
                  <CollapsibleTrigger className="w-full text-left flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50">
                    <span className="font-medium text-sm">Bagaimana melanjutkan chat lama?</span>
                    <ChevronDown className="w-4 h-4 text-muted-foreground" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="px-3 pt-2 text-sm text-muted-foreground">
                    Buka menu "Chat Saya" di sidebar. Pilih percakapan yang ingin kamu lanjutkan.
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible>
                  <CollapsibleTrigger className="w-full text-left flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50">
                    <span className="font-medium text-sm">Apa yang terjadi jika menekan Reset di chat?</span>
                    <ChevronDown className="w-4 h-4 text-muted-foreground" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="px-3 pt-2 text-sm text-muted-foreground">
                    Semua pesan di percakapan tersebut akan dihapus dan kamu akan memulai dari awal. Gunakan saat kamu ingin memulai topik baru.
                  </CollapsibleContent>
                </Collapsible>
              </CardContent>
            </Card>

            {/* CTA */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg">Siap menjelajah?</CardTitle>
                <CardDescription>Temukan karakter yang cocok untukmu</CardDescription>
              </CardHeader>
              <CardFooter className="justify-between">
                <span className="text-sm text-muted-foreground">Mulai dari halaman Eksplor</span>
                <a
                  href="/dashboard"
                  className="inline-flex items-center px-4 py-2 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-lg transition-colors text-white"
                >
                  Mulai Eksplor
                </a>
              </CardFooter>
            </Card>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

