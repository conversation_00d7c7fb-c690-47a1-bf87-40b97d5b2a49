"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx":
/*!***********************************************************!*\
  !*** ./src/components/chat/character-profile-sidebar.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterProfileSidebar: () => (/* binding */ CharacterProfileSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _services_favorite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/favorite */ \"(app-pages-browser)/./src/services/favorite.ts\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _phone_number_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./phone-number-modal */ \"(app-pages-browser)/./src/components/chat/phone-number-modal.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* __next_internal_client_entry_do_not_use__ CharacterProfileSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-2xl flex size-full items-center justify-center rounded-full bg-muted \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nfunction CharacterProfileSidebar(param) {\n    let { characterId, messageCount, isOpen, onToggle, onBackgroundChange } = param;\n    var _getProfileAssets_currentProfileImageIndex, _getProfileAssets_modalImageIndex;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [assetsLoading, setAssetsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentProfileImageIndex, setCurrentProfileImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedBackground, setSelectedBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBackgroundSettings, setShowBackgroundSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavoriteLoading, setIsFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPhoneModal, setShowPhoneModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkCopied, setLinkCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalImageIndex, setModalImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            loadCharacter();\n            loadAssets();\n            checkFavoriteStatus();\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        characterId\n    ]);\n    // Keyboard navigation for image modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            if (!showImageModal) return;\n            const handleKeyDown = {\n                \"CharacterProfileSidebar.useEffect.handleKeyDown\": (e)=>{\n                    switch(e.key){\n                        case 'Escape':\n                            handleModalClose();\n                            break;\n                        case 'ArrowLeft':\n                            e.preventDefault();\n                            handleModalPrevious();\n                            break;\n                        case 'ArrowRight':\n                            e.preventDefault();\n                            handleModalNext();\n                            break;\n                    }\n                }\n            }[\"CharacterProfileSidebar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"CharacterProfileSidebar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"CharacterProfileSidebar.useEffect\"];\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        showImageModal,\n        modalImageIndex\n    ]);\n    const loadCharacter = async ()=>{\n        try {\n            setLoading(true);\n            const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterById(characterId);\n            setCharacter(characterData);\n        } catch (error) {\n            console.error('Failed to load character:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAssets = async ()=>{\n        try {\n            setAssetsLoading(true);\n            const assetsData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterAssets(characterId);\n            setAssets(assetsData);\n            // Auto-select first background if character has backgrounds and no background is currently selected\n            const backgroundAssets = assetsData.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n            if (backgroundAssets.length > 0 && selectedBackground === null) {\n                const defaultBackground = backgroundAssets[0].id;\n                setSelectedBackground(defaultBackground);\n                onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(defaultBackground);\n            }\n        } catch (error) {\n            console.error('Failed to load character assets:', error);\n        } finally{\n            setAssetsLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            const favoritesResponse = await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.getFavorites();\n            const isFavorited = favoritesResponse.data.some((fav)=>fav.id === characterId);\n            setIsFavorite(isFavorited);\n        } catch (error) {\n            console.error('Failed to check favorite status:', error);\n        // Don't show error toast for this, just keep default state\n        }\n    };\n    const formatDate = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true,\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.id\n            });\n        } catch (e) {\n            return 'Unknown';\n        }\n    };\n    // Helper functions for assets\n    const getProfileAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    };\n    const getBackgroundAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    };\n    const handleBackgroundChange = (backgroundId)=>{\n        setSelectedBackground(backgroundId);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(backgroundId);\n    };\n    const handleFavoriteToggle = async ()=>{\n        if (!character) return;\n        setIsFavoriteLoading(true);\n        try {\n            if (isFavorite) {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.removeFromFavorite(character.id);\n                setIsFavorite(false);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Dihapus dari favorit\", {\n                    description: \"\".concat(character.name, \" telah dihapus dari daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            } else {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.addToFavorite(character.id);\n                setIsFavorite(true);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Ditambahkan ke favorit\", {\n                    description: \"\".concat(character.name, \" telah ditambahkan ke daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal mengubah favorit\", {\n                description: error instanceof Error ? error.message : \"Terjadi kesalahan saat mengubah status favorit.\",\n                duration: 4000\n            });\n        } finally{\n            setIsFavoriteLoading(false);\n        }\n    };\n    const handleWhatsAppClick = ()=>{\n        if (!(character === null || character === void 0 ? void 0 : character.whatsappUrl)) return;\n        // Check if user has phone number\n        if (!(user === null || user === void 0 ? void 0 : user.phoneNumber)) {\n            setShowPhoneModal(true);\n            return;\n        }\n        // If user has phone number, proceed to WhatsApp\n        window.open(character.whatsappUrl, '_blank');\n    };\n    const handlePhoneModalSuccess = ()=>{\n        if (character === null || character === void 0 ? void 0 : character.whatsappUrl) {\n            window.open(character.whatsappUrl, '_blank');\n        }\n    };\n    const handleCopyLink = async ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        try {\n            await navigator.clipboard.writeText(characterUrl);\n            setLinkCopied(true);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Link disalin!\", {\n                description: \"Link karakter telah disalin ke clipboard. Bagikan ke teman-temanmu!\",\n                duration: 3000\n            });\n            // Reset copied state after 3 seconds\n            setTimeout(()=>setLinkCopied(false), 3000);\n        } catch (error) {\n            console.error('Failed to copy link:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal menyalin link\", {\n                description: \"Terjadi kesalahan saat menyalin link karakter.\",\n                duration: 3000\n            });\n        }\n    };\n    const handleShareToWhatsApp = ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        const message = \"Halo! Ayo chat dengan \".concat(character.name, \" di Bestieku! \\uD83E\\uDD16\\n\\n\").concat(character.description, \"\\n\\nKlik link ini: \").concat(characterUrl);\n        const whatsappUrl = \"https://wa.me/?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleImageClick = (index)=>{\n        setModalImageIndex(index);\n        setShowImageModal(true);\n    };\n    const handleModalClose = ()=>{\n        setShowImageModal(false);\n    };\n    const handleModalPrevious = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : profileAssets.length - 1);\n    };\n    const handleModalNext = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex < profileAssets.length - 1 ? modalImageIndex + 1 : 0);\n    };\n    // Touch handlers for swipe navigation\n    const handleTouchStart = (e)=>{\n        setTouchEnd(null);\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && getProfileAssets().length > 1) {\n            handleModalNext();\n        }\n        if (isRightSwipe && getProfileAssets().length > 1) {\n            handleModalPrevious();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-32 bg-muted rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 316,\n            columnNumber: 7\n        }, this);\n    }\n    if (!character) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Karakter tidak ditemukan\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 346,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    onClick: onToggle,\n                    className: \"w-full\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 60\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 relative flex shrink-0 overflow-hidden rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                        src: (()=>{\n                                            // Get first profile image asset (not video)\n                                            const profileAssets = getProfileAssets().filter((asset)=>asset.type === 'image');\n                                            return profileAssets.length > 0 ? profileAssets[0].url : character.image;\n                                        })(),\n                                        alt: character.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-1\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this),\n                                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mb-3 font-medium\",\n                                    children: character.title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2 mb-3\",\n                                    children: [\n                                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-bestieku-primary text-white\",\n                                            children: \"Mode Cerita\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: character.status\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this),\n                                character.whatsappUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        onClick: handleWhatsAppClick,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chat di WhatsApp\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: isFavorite ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleFavoriteToggle,\n                                        disabled: isFavoriteLoading,\n                                        className: \"flex items-center gap-2 \".concat(isFavorite ? 'bg-red-500 hover:bg-red-600 text-white' : 'hover:bg-red-50 hover:text-red-600 hover:border-red-300'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isFavorite ? 'fill-current' : '')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 19\n                                            }, this),\n                                            isFavoriteLoading ? 'Loading...' : isFavorite ? 'Favorit' : 'Tambah ke Favorit'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleCopyLink,\n                                            className: \"flex items-center gap-2 flex-1\",\n                                            children: linkCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Link Disalin!\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Salin Link\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShareToWhatsApp,\n                                            className: \"flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"WhatsApp\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Tentang\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                    children: character.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this),\n                        character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Tag\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: character.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Galeri Profil\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            onTouchStart: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onTouchEnd: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            children: [\n                                                (()=>{\n                                                    const asset = getProfileAssets()[currentProfileImageIndex];\n                                                    const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                                    return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        src: url,\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        muted: true,\n                                                        playsInline: true,\n                                                        preload: \"metadata\",\n                                                        poster: url + \"#t=0.1\",\n                                                        onLoadedData: (e)=>{\n                                                            e.currentTarget.currentTime = 0;\n                                                        },\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: url,\n                                                        alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })(),\n                                                getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-x-0 bottom-2 flex justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex > 0 ? currentProfileImageIndex - 1 : getProfileAssets().length - 1),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex < getProfileAssets().length - 1 ? currentProfileImageIndex + 1 : 0),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, this),\n                                        ((_getProfileAssets_currentProfileImageIndex = getProfileAssets()[currentProfileImageIndex]) === null || _getProfileAssets_currentProfileImageIndex === void 0 ? void 0 : _getProfileAssets_currentProfileImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground text-center\",\n                                            children: getProfileAssets()[currentProfileImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 21\n                                        }, this),\n                                        getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-1\",\n                                            children: getProfileAssets().map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentProfileImageIndex ? 'bg-bestieku-primary' : 'bg-muted'),\n                                                    onClick: ()=>setCurrentProfileImageIndex(index)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 65\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Statistik Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Pesan Anda\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Pesan\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-bestieku-primary\",\n                                                    children: character.messageCount > 999 ? '999+' : character.messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Jenis Karakter\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: character.storyMode ? 'Story' : 'Chat'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Info Karakter\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Dibuat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Diperbarui\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Latar Belakang Chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: ()=>setShowBackgroundSettings(!showBackgroundSettings),\n                                            className: \"w-6 h-6\",\n                                            children: showBackgroundSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 47\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 83\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 17\n                                }, this),\n                                showBackgroundSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === null ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                            onClick: ()=>handleBackgroundChange(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-8 bg-gradient-to-b from-background to-muted/20 rounded border\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Latar belakang polos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 21\n                                        }, this),\n                                        getBackgroundAssets().map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === asset.id ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                                onClick: ()=>handleBackgroundChange(asset.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /\\.mp4(\\?|$)/i.test(asset.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: asset.url,\n                                                            className: \"w-12 h-8 object-cover rounded border pointer-events-none\",\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            preload: \"metadata\",\n                                                            onMouseEnter: (e)=>e.currentTarget.play(),\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.pause();\n                                                                e.currentTarget.currentTime = 0;\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: asset.url,\n                                                            alt: asset.caption || 'Background',\n                                                            className: \"w-12 h-8 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: asset.caption || 'Latar Belakang Kustom'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Latar belakang karakter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, asset.id, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 23\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 68\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, this),\n            showImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl max-h-[90vh] w-full mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModalClose,\n                                className: \"absolute top-4 right-4 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalPrevious,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalNext,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-black rounded-lg overflow-hidden\",\n                                children: [\n                                    (()=>{\n                                        const asset = getProfileAssets()[modalImageIndex];\n                                        const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                        return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: url,\n                                            className: \"w-full max-h-[80vh] object-contain\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            muted: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: url,\n                                            alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                            className: \"w-full max-h-[80vh] object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 19\n                                        }, this);\n                                    })(),\n                                    ((_getProfileAssets_modalImageIndex = getProfileAssets()[modalImageIndex]) === null || _getProfileAssets_modalImageIndex === void 0 ? void 0 : _getProfileAssets_modalImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-center\",\n                                            children: getProfileAssets()[modalImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm\",\n                                children: [\n                                    modalImageIndex + 1,\n                                    \" / \",\n                                    getProfileAssets().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 783,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        onClick: handleModalClose\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 852,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 782,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phone_number_modal__WEBPACK_IMPORTED_MODULE_8__.PhoneNumberModal, {\n                isOpen: showPhoneModal,\n                onClose: ()=>setShowPhoneModal(false),\n                onSuccess: handlePhoneModalSuccess,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 860,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterProfileSidebar, \"+qX2Wivszt4LAgo8abAc1LMRvKc=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c1 = CharacterProfileSidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"CharacterProfileSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\n"));

/***/ })

});