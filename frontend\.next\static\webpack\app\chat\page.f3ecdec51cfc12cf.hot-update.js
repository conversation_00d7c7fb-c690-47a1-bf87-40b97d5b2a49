"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx":
/*!***********************************************************!*\
  !*** ./src/components/chat/character-profile-sidebar.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterProfileSidebar: () => (/* binding */ CharacterProfileSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _services_favorite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/favorite */ \"(app-pages-browser)/./src/services/favorite.ts\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _phone_number_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./phone-number-modal */ \"(app-pages-browser)/./src/components/chat/phone-number-modal.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* __next_internal_client_entry_do_not_use__ CharacterProfileSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-2xl flex size-full items-center justify-center rounded-full bg-muted \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nfunction CharacterProfileSidebar(param) {\n    let { characterId, messageCount, isOpen, onToggle, onBackgroundChange } = param;\n    var _getProfileAssets_currentProfileImageIndex, _getProfileAssets_modalImageIndex;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [assetsLoading, setAssetsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentProfileImageIndex, setCurrentProfileImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedBackground, setSelectedBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBackgroundSettings, setShowBackgroundSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavoriteLoading, setIsFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPhoneModal, setShowPhoneModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkCopied, setLinkCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalImageIndex, setModalImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            loadCharacter();\n            loadAssets();\n            checkFavoriteStatus();\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        characterId\n    ]);\n    // Keyboard navigation for image modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            if (!showImageModal) return;\n            const handleKeyDown = {\n                \"CharacterProfileSidebar.useEffect.handleKeyDown\": (e)=>{\n                    switch(e.key){\n                        case 'Escape':\n                            handleModalClose();\n                            break;\n                        case 'ArrowLeft':\n                            e.preventDefault();\n                            handleModalPrevious();\n                            break;\n                        case 'ArrowRight':\n                            e.preventDefault();\n                            handleModalNext();\n                            break;\n                    }\n                }\n            }[\"CharacterProfileSidebar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"CharacterProfileSidebar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"CharacterProfileSidebar.useEffect\"];\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        showImageModal,\n        modalImageIndex\n    ]);\n    const loadCharacter = async ()=>{\n        try {\n            setLoading(true);\n            const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterById(characterId);\n            setCharacter(characterData);\n        } catch (error) {\n            console.error('Failed to load character:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAssets = async ()=>{\n        try {\n            setAssetsLoading(true);\n            const assetsData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterAssets(characterId);\n            setAssets(assetsData);\n            // Auto-select first background if character has backgrounds and no background is currently selected\n            const backgroundAssets = assetsData.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n            if (backgroundAssets.length > 0 && selectedBackground === null) {\n                const defaultBackground = backgroundAssets[0].id;\n                setSelectedBackground(defaultBackground);\n                onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(defaultBackground);\n            }\n        } catch (error) {\n            console.error('Failed to load character assets:', error);\n        } finally{\n            setAssetsLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            const favoritesResponse = await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.getFavorites();\n            const isFavorited = favoritesResponse.data.some((fav)=>fav.id === characterId);\n            setIsFavorite(isFavorited);\n        } catch (error) {\n            console.error('Failed to check favorite status:', error);\n        // Don't show error toast for this, just keep default state\n        }\n    };\n    const formatDate = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true,\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.id\n            });\n        } catch (e) {\n            return 'Unknown';\n        }\n    };\n    // Helper functions for assets\n    const getProfileAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    };\n    const getBackgroundAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    };\n    const handleBackgroundChange = (backgroundId)=>{\n        setSelectedBackground(backgroundId);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(backgroundId);\n    };\n    const handleFavoriteToggle = async ()=>{\n        if (!character) return;\n        setIsFavoriteLoading(true);\n        try {\n            if (isFavorite) {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.removeFromFavorite(character.id);\n                setIsFavorite(false);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Dihapus dari favorit\", {\n                    description: \"\".concat(character.name, \" telah dihapus dari daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            } else {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.addToFavorite(character.id);\n                setIsFavorite(true);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Ditambahkan ke favorit\", {\n                    description: \"\".concat(character.name, \" telah ditambahkan ke daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal mengubah favorit\", {\n                description: error instanceof Error ? error.message : \"Terjadi kesalahan saat mengubah status favorit.\",\n                duration: 4000\n            });\n        } finally{\n            setIsFavoriteLoading(false);\n        }\n    };\n    const handleWhatsAppClick = ()=>{\n        if (!(character === null || character === void 0 ? void 0 : character.whatsappUrl)) return;\n        // Check if user has phone number\n        if (!(user === null || user === void 0 ? void 0 : user.phoneNumber)) {\n            setShowPhoneModal(true);\n            return;\n        }\n        // If user has phone number, proceed to WhatsApp\n        window.open(character.whatsappUrl, '_blank');\n    };\n    const handlePhoneModalSuccess = ()=>{\n        if (character === null || character === void 0 ? void 0 : character.whatsappUrl) {\n            window.open(character.whatsappUrl, '_blank');\n        }\n    };\n    const handleCopyLink = async ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        try {\n            await navigator.clipboard.writeText(characterUrl);\n            setLinkCopied(true);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Link disalin!\", {\n                description: \"Link karakter telah disalin ke clipboard. Bagikan ke teman-temanmu!\",\n                duration: 3000\n            });\n            // Reset copied state after 3 seconds\n            setTimeout(()=>setLinkCopied(false), 3000);\n        } catch (error) {\n            console.error('Failed to copy link:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal menyalin link\", {\n                description: \"Terjadi kesalahan saat menyalin link karakter.\",\n                duration: 3000\n            });\n        }\n    };\n    const handleShareToWhatsApp = ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        const message = \"Halo! Ayo chat dengan \".concat(character.name, \" di Bestieku! \\uD83E\\uDD16\\n\\n\").concat(character.description, \"\\n\\nKlik link ini: \").concat(characterUrl);\n        const whatsappUrl = \"https://wa.me/?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleImageClick = (index)=>{\n        setModalImageIndex(index);\n        setShowImageModal(true);\n    };\n    const handleModalClose = ()=>{\n        setShowImageModal(false);\n    };\n    const handleModalPrevious = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : profileAssets.length - 1);\n    };\n    const handleModalNext = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex < profileAssets.length - 1 ? modalImageIndex + 1 : 0);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-32 bg-muted rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, this);\n    }\n    if (!character) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Karakter tidak ditemukan\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 319,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    onClick: onToggle,\n                    className: \"w-full\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 60\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 relative flex shrink-0 overflow-hidden rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                        src: (()=>{\n                                            // Get first profile image asset (not video)\n                                            const profileAssets = getProfileAssets().filter((asset)=>asset.type === 'image');\n                                            return profileAssets.length > 0 ? profileAssets[0].url : character.image;\n                                        })(),\n                                        alt: character.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-1\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mb-3 font-medium\",\n                                    children: character.title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2 mb-3\",\n                                    children: [\n                                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-bestieku-primary text-white\",\n                                            children: \"Mode Cerita\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: character.status\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this),\n                                character.whatsappUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        onClick: handleWhatsAppClick,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chat di WhatsApp\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: isFavorite ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleFavoriteToggle,\n                                        disabled: isFavoriteLoading,\n                                        className: \"flex items-center gap-2 \".concat(isFavorite ? 'bg-red-500 hover:bg-red-600 text-white' : 'hover:bg-red-50 hover:text-red-600 hover:border-red-300'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isFavorite ? 'fill-current' : '')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this),\n                                            isFavoriteLoading ? 'Loading...' : isFavorite ? 'Favorit' : 'Tambah ke Favorit'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleCopyLink,\n                                            className: \"flex items-center gap-2 flex-1\",\n                                            children: linkCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Link Disalin!\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Salin Link\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShareToWhatsApp,\n                                            className: \"flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"WhatsApp\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Tentang\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                    children: character.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 13\n                        }, this),\n                        character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Tag\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: character.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Galeri Profil\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            onTouchStart: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onTouchEnd: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            children: [\n                                                (()=>{\n                                                    const asset = getProfileAssets()[currentProfileImageIndex];\n                                                    const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                                    return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        src: url,\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        muted: true,\n                                                        playsInline: true,\n                                                        preload: \"metadata\",\n                                                        poster: url + \"#t=0.1\",\n                                                        onLoadedData: (e)=>{\n                                                            e.currentTarget.currentTime = 0;\n                                                        },\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: url,\n                                                        alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })(),\n                                                getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-x-0 bottom-2 flex justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex > 0 ? currentProfileImageIndex - 1 : getProfileAssets().length - 1),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex < getProfileAssets().length - 1 ? currentProfileImageIndex + 1 : 0),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, this),\n                                        ((_getProfileAssets_currentProfileImageIndex = getProfileAssets()[currentProfileImageIndex]) === null || _getProfileAssets_currentProfileImageIndex === void 0 ? void 0 : _getProfileAssets_currentProfileImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground text-center\",\n                                            children: getProfileAssets()[currentProfileImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 21\n                                        }, this),\n                                        getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-1\",\n                                            children: getProfileAssets().map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentProfileImageIndex ? 'bg-bestieku-primary' : 'bg-muted'),\n                                                    onClick: ()=>setCurrentProfileImageIndex(index)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 65\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Statistik Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Pesan Anda\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Pesan\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-bestieku-primary\",\n                                                    children: character.messageCount > 999 ? '999+' : character.messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Jenis Karakter\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: character.storyMode ? 'Story' : 'Chat'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Info Karakter\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Dibuat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Diperbarui\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Latar Belakang Chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: ()=>setShowBackgroundSettings(!showBackgroundSettings),\n                                            className: \"w-6 h-6\",\n                                            children: showBackgroundSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 47\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 83\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 17\n                                }, this),\n                                showBackgroundSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === null ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                            onClick: ()=>handleBackgroundChange(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-8 bg-gradient-to-b from-background to-muted/20 rounded border\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Latar belakang polos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 21\n                                        }, this),\n                                        getBackgroundAssets().map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === asset.id ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                                onClick: ()=>handleBackgroundChange(asset.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /\\.mp4(\\?|$)/i.test(asset.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: asset.url,\n                                                            className: \"w-12 h-8 object-cover rounded border pointer-events-none\",\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            preload: \"metadata\",\n                                                            onMouseEnter: (e)=>e.currentTarget.play(),\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.pause();\n                                                                e.currentTarget.currentTime = 0;\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: asset.url,\n                                                            alt: asset.caption || 'Background',\n                                                            className: \"w-12 h-8 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: asset.caption || 'Latar Belakang Kustom'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Latar belakang karakter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, asset.id, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 23\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 747,\n                            columnNumber: 68\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this),\n            showImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl max-h-[90vh] w-full mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModalClose,\n                                className: \"absolute top-4 right-4 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalPrevious,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalNext,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-black rounded-lg overflow-hidden\",\n                                children: [\n                                    (()=>{\n                                        const asset = getProfileAssets()[modalImageIndex];\n                                        const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                        return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: url,\n                                            className: \"w-full max-h-[80vh] object-contain\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            muted: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: url,\n                                            alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                            className: \"w-full max-h-[80vh] object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 19\n                                        }, this);\n                                    })(),\n                                    ((_getProfileAssets_modalImageIndex = getProfileAssets()[modalImageIndex]) === null || _getProfileAssets_modalImageIndex === void 0 ? void 0 : _getProfileAssets_modalImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-center\",\n                                            children: getProfileAssets()[modalImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 784,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm\",\n                                children: [\n                                    modalImageIndex + 1,\n                                    \" / \",\n                                    getProfileAssets().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        onClick: handleModalClose\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 825,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 755,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phone_number_modal__WEBPACK_IMPORTED_MODULE_8__.PhoneNumberModal, {\n                isOpen: showPhoneModal,\n                onClose: ()=>setShowPhoneModal(false),\n                onSuccess: handlePhoneModalSuccess,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 833,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterProfileSidebar, \"IGnReBBvqXz2AngS2luzTgUvjzI=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c1 = CharacterProfileSidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"CharacterProfileSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvY2hhcmFjdGVyLXByb2ZpbGUtc2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFFSztBQUNGO0FBQ0o7QUFFSjtBQUNFO0FBQ007QUFDRTtBQUN6QjtBQW1CVDtBQUN5QjtBQUNWO0FBRXJDLHdEQUF3RDtBQUN4RCxNQUFNOEIsZUFBZTtRQUFDLEVBQUVDLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxTQUFTLEVBSTFDO0lBQ0MsSUFBSSxDQUFDRixLQUFLO1lBR0hDO1FBRkwscUJBQ0UsOERBQUNFO1lBQUlELFdBQVcsNkVBQTZGLE9BQWhCQSxhQUFhO3NCQUN2R0QsQ0FBQUEsZ0JBQUFBLDJCQUFBQSxjQUFBQSxJQUFLRyxNQUFNLENBQUMsZ0JBQVpILGtDQUFBQSxZQUFnQkksV0FBVyxPQUFNOzs7Ozs7SUFHeEM7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ04sS0FBS0E7UUFDTEMsS0FBS0E7UUFDTEMsV0FBVyx3Q0FBd0QsT0FBaEJBLGFBQWE7Ozs7OztBQUd0RTtLQXBCTUg7QUE4QkMsU0FBU1Esd0JBQXdCLEtBTVQ7UUFOUyxFQUN0Q0MsV0FBVyxFQUNYQyxZQUFZLEVBQ1pDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxrQkFBa0IsRUFDVyxHQU5TO1FBd2dCckJDLDRDQTROSkE7O0lBN3RCYixNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHeEMsK0RBQU9BO0lBQ3hCLE1BQU0sQ0FBQ3lDLFdBQVdDLGFBQWEsR0FBRzlDLCtDQUFRQSxDQUFtQjtJQUM3RCxNQUFNLENBQUMrQyxRQUFRQyxVQUFVLEdBQUdoRCwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUN6RCxNQUFNLENBQUNpRCxTQUFTQyxXQUFXLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNtRCxlQUFlQyxpQkFBaUIsR0FBR3BELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3FELDBCQUEwQkMsNEJBQTRCLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUN6RSxNQUFNLENBQUN1RCxvQkFBb0JDLHNCQUFzQixHQUFHeEQsK0NBQVFBLENBQWdCO0lBQzVFLE1BQU0sQ0FBQ3lELHdCQUF3QkMsMEJBQTBCLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUNyRSxNQUFNLENBQUMyRCxZQUFZQyxjQUFjLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2RCxtQkFBbUJDLHFCQUFxQixHQUFHOUQsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDK0QsZ0JBQWdCQyxrQkFBa0IsR0FBR2hFLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ2lFLFlBQVlDLGNBQWMsR0FBR2xFLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ21FLGdCQUFnQkMsa0JBQWtCLEdBQUdwRSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNxRSxpQkFBaUJDLG1CQUFtQixHQUFHdEUsK0NBQVFBLENBQUM7SUFFdkRDLGdEQUFTQTs2Q0FBQztZQUNSc0U7WUFDQUM7WUFDQUM7UUFDRjs0Q0FBRztRQUFDbkM7S0FBWTtJQUVoQixzQ0FBc0M7SUFDdENyQyxnREFBU0E7NkNBQUM7WUFDUixJQUFJLENBQUNrRSxnQkFBZ0I7WUFFckIsTUFBTU87bUVBQWdCLENBQUNDO29CQUNyQixPQUFRQSxFQUFFQyxHQUFHO3dCQUNYLEtBQUs7NEJBQ0hDOzRCQUNBO3dCQUNGLEtBQUs7NEJBQ0hGLEVBQUVHLGNBQWM7NEJBQ2hCQzs0QkFDQTt3QkFDRixLQUFLOzRCQUNISixFQUFFRyxjQUFjOzRCQUNoQkU7NEJBQ0E7b0JBQ0o7Z0JBQ0Y7O1lBRUFDLFNBQVNDLGdCQUFnQixDQUFDLFdBQVdSO1lBQ3JDO3FEQUFPLElBQU1PLFNBQVNFLG1CQUFtQixDQUFDLFdBQVdUOztRQUN2RDs0Q0FBRztRQUFDUDtRQUFnQkU7S0FBZ0I7SUFFcEMsTUFBTUUsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRnJCLFdBQVc7WUFDWCxNQUFNa0MsZ0JBQWdCLE1BQU1sRixpRUFBZ0JBLENBQUNtRixnQkFBZ0IsQ0FBQy9DO1lBQzlEUSxhQUFhc0M7UUFDZixFQUFFLE9BQU9FLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDN0MsU0FBVTtZQUNScEMsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNc0IsYUFBYTtRQUNqQixJQUFJO1lBQ0ZwQixpQkFBaUI7WUFDakIsTUFBTW9DLGFBQWEsTUFBTXRGLGlFQUFnQkEsQ0FBQ3VGLGtCQUFrQixDQUFDbkQ7WUFDN0RVLFVBQVV3QztZQUVWLG9HQUFvRztZQUNwRyxNQUFNRSxtQkFBbUJGLFdBQVdHLE1BQU0sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTUMsT0FBTyxLQUFLLGdCQUFnQkQsTUFBTUUsV0FBVztZQUN2RyxJQUFJSixpQkFBaUJLLE1BQU0sR0FBRyxLQUFLeEMsdUJBQXVCLE1BQU07Z0JBQzlELE1BQU15QyxvQkFBb0JOLGdCQUFnQixDQUFDLEVBQUUsQ0FBQzlELEVBQUU7Z0JBQ2hENEIsc0JBQXNCd0M7Z0JBQ3RCdEQsK0JBQUFBLHlDQUFBQSxtQkFBcUJzRDtZQUN2QjtRQUNGLEVBQUUsT0FBT1YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtRQUNwRCxTQUFVO1lBQ1JsQyxpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU1xQixzQkFBc0I7UUFDMUIsSUFBSTtZQUNGLE1BQU13QixvQkFBb0IsTUFBTTlGLCtEQUFlQSxDQUFDK0YsWUFBWTtZQUM1RCxNQUFNQyxjQUFjRixrQkFBa0JHLElBQUksQ0FBQ0MsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJMUUsRUFBRSxLQUFLVTtZQUNsRXNCLGNBQWN1QztRQUNoQixFQUFFLE9BQU9iLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG9DQUFvQ0E7UUFDbEQsMkRBQTJEO1FBQzdEO0lBQ0Y7SUFFQSxNQUFNaUIsYUFBYSxDQUFDQztRQUNsQixJQUFJO1lBQ0YsT0FBTzdFLHlHQUFtQkEsQ0FBQyxJQUFJOEUsS0FBS0QsYUFBYTtnQkFBRUUsV0FBVztnQkFBTUMsUUFBUS9FLGdEQUFFQTtZQUFDO1FBQ2pGLEVBQUUsVUFBTTtZQUNOLE9BQU87UUFDVDtJQUNGO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1lLG1CQUFtQjtRQUN2QixPQUFPSSxPQUFPNEMsTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNQyxPQUFPLEtBQUssYUFBYUQsTUFBTUUsV0FBVztJQUNoRjtJQUVBLE1BQU1jLHNCQUFzQjtRQUMxQixPQUFPN0QsT0FBTzRDLE1BQU0sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTUMsT0FBTyxLQUFLLGdCQUFnQkQsTUFBTUUsV0FBVztJQUNuRjtJQUVBLE1BQU1lLHlCQUF5QixDQUFDQztRQUM5QnRELHNCQUFzQnNEO1FBQ3RCcEUsK0JBQUFBLHlDQUFBQSxtQkFBcUJvRTtJQUN2QjtJQUVBLE1BQU1DLHVCQUF1QjtRQUMzQixJQUFJLENBQUNsRSxXQUFXO1FBRWhCaUIscUJBQXFCO1FBQ3JCLElBQUk7WUFDRixJQUFJSCxZQUFZO2dCQUNkLE1BQU14RCwrREFBZUEsQ0FBQzZHLGtCQUFrQixDQUFDbkUsVUFBVWpCLEVBQUU7Z0JBQ3JEZ0MsY0FBYztnQkFDZG5ELHlDQUFLQSxDQUFDd0csT0FBTyxDQUFDLHdCQUF3QjtvQkFDcENDLGFBQWEsR0FBa0IsT0FBZnJFLFVBQVVzRSxJQUFJLEVBQUM7b0JBQy9CQyxVQUFVO2dCQUNaO1lBQ0YsT0FBTztnQkFDTCxNQUFNakgsK0RBQWVBLENBQUNrSCxhQUFhLENBQUN4RSxVQUFVakIsRUFBRTtnQkFDaERnQyxjQUFjO2dCQUNkbkQseUNBQUtBLENBQUN3RyxPQUFPLENBQUMsMEJBQTBCO29CQUN0Q0MsYUFBYSxHQUFrQixPQUFmckUsVUFBVXNFLElBQUksRUFBQztvQkFDL0JDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGLEVBQUUsT0FBTzlCLE9BQU87WUFDZDdFLHlDQUFLQSxDQUFDNkUsS0FBSyxDQUFDLDBCQUEwQjtnQkFDcEM0QixhQUFhNUIsaUJBQWlCZ0MsUUFBUWhDLE1BQU1pQyxPQUFPLEdBQUc7Z0JBQ3RESCxVQUFVO1lBQ1o7UUFDRixTQUFVO1lBQ1J0RCxxQkFBcUI7UUFDdkI7SUFDRjtJQUVBLE1BQU0wRCxzQkFBc0I7UUFDMUIsSUFBSSxFQUFDM0Usc0JBQUFBLGdDQUFBQSxVQUFXNEUsV0FBVyxHQUFFO1FBRTdCLGlDQUFpQztRQUNqQyxJQUFJLEVBQUM3RSxpQkFBQUEsMkJBQUFBLEtBQU04RSxXQUFXLEdBQUU7WUFDdEIxRCxrQkFBa0I7WUFDbEI7UUFDRjtRQUVBLGdEQUFnRDtRQUNoRDJELE9BQU9DLElBQUksQ0FBQy9FLFVBQVU0RSxXQUFXLEVBQUU7SUFDckM7SUFFQSxNQUFNSSwwQkFBMEI7UUFDOUIsSUFBSWhGLHNCQUFBQSxnQ0FBQUEsVUFBVzRFLFdBQVcsRUFBRTtZQUMxQkUsT0FBT0MsSUFBSSxDQUFDL0UsVUFBVTRFLFdBQVcsRUFBRTtRQUNyQztJQUNGO0lBRUEsTUFBTUssaUJBQWlCO1FBQ3JCLElBQUksQ0FBQ2pGLFdBQVc7UUFFaEIsTUFBTWtGLGVBQWUsR0FBaURsRixPQUE5QzhFLE9BQU9LLFFBQVEsQ0FBQ0MsTUFBTSxFQUFDLHlCQUFvQyxPQUFicEYsVUFBVWpCLEVBQUU7UUFFbEYsSUFBSTtZQUNGLE1BQU1zRyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ0w7WUFDcEM3RCxjQUFjO1lBQ2R6RCx5Q0FBS0EsQ0FBQ3dHLE9BQU8sQ0FBQyxpQkFBaUI7Z0JBQzdCQyxhQUFhO2dCQUNiRSxVQUFVO1lBQ1o7WUFFQSxxQ0FBcUM7WUFDckNpQixXQUFXLElBQU1uRSxjQUFjLFFBQVE7UUFFekMsRUFBRSxPQUFPb0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtZQUN0QzdFLHlDQUFLQSxDQUFDNkUsS0FBSyxDQUFDLHVCQUF1QjtnQkFDakM0QixhQUFhO2dCQUNiRSxVQUFVO1lBQ1o7UUFDRjtJQUNGO0lBRUEsTUFBTWtCLHdCQUF3QjtRQUM1QixJQUFJLENBQUN6RixXQUFXO1FBRWhCLE1BQU1rRixlQUFlLEdBQWlEbEYsT0FBOUM4RSxPQUFPSyxRQUFRLENBQUNDLE1BQU0sRUFBQyx5QkFBb0MsT0FBYnBGLFVBQVVqQixFQUFFO1FBQ2xGLE1BQU0yRixVQUFVLHlCQUE4RDFFLE9BQXJDQSxVQUFVc0UsSUFBSSxFQUFDLGtDQUFpRVksT0FBM0NsRixVQUFVcUUsV0FBVyxFQUFDLHVCQUFrQyxPQUFiYTtRQUN6SCxNQUFNTixjQUFjLHVCQUFtRCxPQUE1QmMsbUJBQW1CaEI7UUFFOURJLE9BQU9DLElBQUksQ0FBQ0gsYUFBYTtJQUMzQjtJQUVBLE1BQU1lLG1CQUFtQixDQUFDQztRQUN4Qm5FLG1CQUFtQm1FO1FBQ25CckUsa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTVMsbUJBQW1CO1FBQ3ZCVCxrQkFBa0I7SUFDcEI7SUFFQSxNQUFNVyxzQkFBc0I7UUFDMUIsTUFBTTJELGdCQUFnQi9GO1FBQ3RCMkIsbUJBQW1CRCxrQkFBa0IsSUFBSUEsa0JBQWtCLElBQUlxRSxjQUFjM0MsTUFBTSxHQUFHO0lBQ3hGO0lBRUEsTUFBTWYsa0JBQWtCO1FBQ3RCLE1BQU0wRCxnQkFBZ0IvRjtRQUN0QjJCLG1CQUFtQkQsa0JBQWtCcUUsY0FBYzNDLE1BQU0sR0FBRyxJQUFJMUIsa0JBQWtCLElBQUk7SUFDeEY7SUFFQSxJQUFJcEIsU0FBUztRQUNYLHFCQUNFLDhEQUFDaEI7WUFBSUQsV0FBVywyRkFFZixPQURDUSxTQUFTLFNBQVM7OzhCQUVsQiw4REFBQ1A7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUMxQix5REFBTUE7d0JBQ0xxSSxTQUFRO3dCQUNSQyxNQUFLO3dCQUNMQyxTQUFTcEc7d0JBQ1RULFdBQVU7a0NBRVRRLHVCQUFTLDhEQUFDeEIsaU5BQVlBOzRCQUFDZ0IsV0FBVTs7Ozs7aURBQWUsOERBQUNmLGlOQUFXQTs0QkFBQ2UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztnQkFJM0VRLHdCQUNDLDhEQUFDUDtvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDQztnQ0FBSUQsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDQztnQ0FBSUQsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDQztnQ0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNM0I7SUFFQSxJQUFJLENBQUNhLFdBQVc7UUFDZCxxQkFDRSw4REFBQ1o7WUFBSUQsV0FBVywyRkFFZixPQURDUSxTQUFTLFNBQVM7OzhCQUVsQiw4REFBQ1A7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUMxQix5REFBTUE7d0JBQ0xxSSxTQUFRO3dCQUNSQyxNQUFLO3dCQUNMQyxTQUFTcEc7d0JBQ1RULFdBQVU7a0NBRVRRLHVCQUFTLDhEQUFDeEIsaU5BQVlBOzRCQUFDZ0IsV0FBVTs7Ozs7aURBQWUsOERBQUNmLGlOQUFXQTs0QkFBQ2UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztnQkFJM0VRLHdCQUNDLDhEQUFDUDtvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDbEIsaU5BQUlBO2dDQUFDa0IsV0FBVTs7Ozs7OzBDQUNoQiw4REFBQzhHO2dDQUFFOUcsV0FBVTswQ0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNbkM7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVywyRkFFZixPQURDUSxTQUFTLFNBQVM7OzBCQUdsQiw4REFBQ1A7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUMxQix5REFBTUE7b0JBQ0xxSSxTQUFRO29CQUNSQyxNQUFLO29CQUNMQyxTQUFTcEc7b0JBQ1RULFdBQVU7OEJBRVRRLHVCQUFTLDhEQUFDeEIsaU5BQVlBO3dCQUFDZ0IsV0FBVTs7Ozs7NkNBQWUsOERBQUNmLGlOQUFXQTt3QkFBQ2UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztZQUszRVEsd0JBQ0MsOERBQUNQO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNIO3dDQUNDQyxLQUFLLENBQUM7NENBQ0osNENBQTRDOzRDQUM1QyxNQUFNNEcsZ0JBQWdCL0YsbUJBQW1CZ0QsTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNbUQsSUFBSSxLQUFLOzRDQUN4RSxPQUFPTCxjQUFjM0MsTUFBTSxHQUFHLElBQUkyQyxhQUFhLENBQUMsRUFBRSxDQUFDTSxHQUFHLEdBQUduRyxVQUFVb0csS0FBSzt3Q0FDMUU7d0NBQ0FsSCxLQUFLYyxVQUFVc0UsSUFBSTs7Ozs7Ozs7Ozs7OENBSXZCLDhEQUFDK0I7b0NBQUdsSCxXQUFVOzhDQUEwQmEsVUFBVXNFLElBQUk7Ozs7OztnQ0FFckR0RSxVQUFVc0csS0FBSyxrQkFDZCw4REFBQ0w7b0NBQUU5RyxXQUFVOzhDQUNWYSxVQUFVc0csS0FBSzs7Ozs7OzhDQUlwQiw4REFBQ2xIO29DQUFJRCxXQUFVOzt3Q0FDWmEsVUFBVXVHLFNBQVMsa0JBQ2xCLDhEQUFDL0ksdURBQUtBOzRDQUFDMkIsV0FBVTtzREFBaUM7Ozs7OztzREFJcEQsOERBQUMzQix1REFBS0E7NENBQUNzSSxTQUFROzRDQUFVM0csV0FBVTtzREFDaENhLFVBQVV3RyxNQUFNOzs7Ozs7Ozs7Ozs7Z0NBS3BCeEcsVUFBVTRFLFdBQVcsa0JBQ3BCLDhEQUFDeEY7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUMxQix5REFBTUE7d0NBQ0xxSSxTQUFRO3dDQUNSQyxNQUFLO3dDQUNMQyxTQUFTckI7d0NBQ1R4RixXQUFVOzswREFFViw4REFBQ3BCLGlOQUFhQTtnREFBQ29CLFdBQVU7Ozs7Ozs0Q0FBWTswREFFckMsOERBQUNULGlOQUFZQTtnREFBQ1MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTTlCLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQzFCLHlEQUFNQTt3Q0FDTHFJLFNBQVNoRixhQUFhLFlBQVk7d0NBQ2xDaUYsTUFBSzt3Q0FDTEMsU0FBUzlCO3dDQUNUdUMsVUFBVXpGO3dDQUNWN0IsV0FBVywyQkFJVixPQUhDMkIsYUFDSSwyQ0FDQTs7MERBR04sOERBQUM5QyxpTkFBS0E7Z0RBQ0ptQixXQUFXLFdBQTRDLE9BQWpDMkIsYUFBYSxpQkFBaUI7Ozs7Ozs0Q0FFckRFLG9CQUNHLGVBQ0FGLGFBQ0UsWUFDQTs7Ozs7Ozs7Ozs7OzhDQU1WLDhEQUFDMUI7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDMUIseURBQU1BOzRDQUNMcUksU0FBUTs0Q0FDUkMsTUFBSzs0Q0FDTEMsU0FBU2Y7NENBQ1Q5RixXQUFVO3NEQUVUaUMsMkJBQ0M7O2tFQUNFLDhEQUFDdkMsaU5BQUtBO3dEQUFDTSxXQUFVOzs7Ozs7b0RBQVk7OzZFQUkvQjs7a0VBQ0UsOERBQUNQLGlOQUFJQTt3REFBQ08sV0FBVTs7Ozs7O29EQUFZOzs7Ozs7OztzREFLbEMsOERBQUMxQix5REFBTUE7NENBQ0xxSSxTQUFROzRDQUNSQyxNQUFLOzRDQUNMQyxTQUFTUDs0Q0FDVHRHLFdBQVU7OzhEQUVWLDhEQUFDUixpTkFBTUE7b0RBQUNRLFdBQVU7Ozs7OztnREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNcEMsOERBQUN6QiwrREFBU0E7Ozs7O3NDQUdWLDhEQUFDMEI7OzhDQUNDLDhEQUFDc0g7b0NBQUd2SCxXQUFVOztzREFDWiw4REFBQ2xCLGlOQUFJQTs0Q0FBQ2tCLFdBQVU7Ozs7Ozt3Q0FBWTs7Ozs7Ozs4Q0FHOUIsOERBQUM4RztvQ0FBRTlHLFdBQVU7OENBQ1ZhLFVBQVVxRSxXQUFXOzs7Ozs7Ozs7Ozs7c0NBSTFCLDhEQUFDM0csK0RBQVNBOzs7Ozt3QkFHVHNDLFVBQVUyRyxJQUFJLElBQUkzRyxVQUFVMkcsSUFBSSxDQUFDekQsTUFBTSxHQUFHLG1CQUN6Qyw4REFBQzlEOzs4Q0FDQyw4REFBQ3NIO29DQUFHdkgsV0FBVTs7c0RBQ1osOERBQUNyQixpTkFBR0E7NENBQUNxQixXQUFVOzs7Ozs7d0NBQVk7Ozs7Ozs7OENBRzdCLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDWmEsVUFBVTJHLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLG9CQUNuQiw4REFBQ3JKLHVEQUFLQTs0Q0FBV3NJLFNBQVE7NENBQVkzRyxXQUFVO3NEQUM1QzBIOzJDQURTQTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRcEIsOERBQUNuSiwrREFBU0E7Ozs7O3dCQUdULENBQUM0QyxpQkFBaUJSLG1CQUFtQm9ELE1BQU0sR0FBRyxtQkFDN0MsOERBQUM5RDs7OENBQ0MsOERBQUNzSDtvQ0FBR3ZILFdBQVU7O3NEQUNaLDhEQUFDWCxpTkFBU0E7NENBQUNXLFdBQVU7Ozs7Ozt3Q0FBWTs7Ozs7Ozs4Q0FHbkMsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQ0NELFdBQVU7NENBQ1YySCxjQUFjLENBQUNoRjtnREFDYixNQUFNaUYsUUFBUWpGLEVBQUVrRixhQUFhLENBQUNDLGFBQWEsQ0FBQztnREFDNUMsSUFBSUYsT0FBT0EsTUFBTUcsSUFBSTs0Q0FDdkI7NENBQ0FDLGNBQWMsQ0FBQ3JGO2dEQUNiLE1BQU1pRixRQUFRakYsRUFBRWtGLGFBQWEsQ0FBQ0MsYUFBYSxDQUFDO2dEQUM1QyxJQUFJRixPQUFPO29EQUNUQSxNQUFNSyxLQUFLO29EQUNYTCxNQUFNTSxXQUFXLEdBQUc7Z0RBQ3RCOzRDQUNGOzRDQUNBQyxjQUFjLENBQUN4RjtnREFDYixNQUFNaUYsUUFBUWpGLEVBQUVrRixhQUFhLENBQUNDLGFBQWEsQ0FBQztnREFDNUMsSUFBSUYsT0FBT0EsTUFBTUcsSUFBSTs0Q0FDdkI7NENBQ0FLLFlBQVksQ0FBQ3pGO2dEQUNYLE1BQU1pRixRQUFRakYsRUFBRWtGLGFBQWEsQ0FBQ0MsYUFBYSxDQUFDO2dEQUM1QyxJQUFJRixPQUFPO29EQUNUQSxNQUFNSyxLQUFLO29EQUNYTCxNQUFNTSxXQUFXLEdBQUc7Z0RBQ3RCOzRDQUNGOztnREFFRTtvREFDQSxNQUFNdEUsUUFBUWpELGtCQUFrQixDQUFDVSx5QkFBeUI7b0RBQzFELE1BQU0yRixNQUFNcEQsa0JBQUFBLDRCQUFBQSxNQUFPb0QsR0FBRztvREFDdEIsT0FBT0EsT0FBTyxlQUFlcUIsSUFBSSxDQUFDckIscUJBQ2hDLDhEQUFDWTt3REFDQzlILEtBQUtrSDt3REFDTGhILFdBQVU7d0RBQ1ZzSSxLQUFLO3dEQUNMQyxXQUFXO3dEQUNYQyxTQUFRO3dEQUNSQyxRQUFRekIsTUFBTTt3REFDZDBCLGNBQWMsQ0FBQy9GOzREQUNiQSxFQUFFa0YsYUFBYSxDQUFDSyxXQUFXLEdBQUc7d0RBQ2hDO3dEQUNBckIsU0FBUyxJQUFNTCxpQkFBaUJuRjs7Ozs7NkVBR2xDLDhEQUFDakI7d0RBQ0NOLEtBQUtrSDt3REFDTGpILEtBQUs2RCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU8rRSxPQUFPLEtBQUk7d0RBQ3ZCM0ksV0FBVTt3REFDVjZHLFNBQVMsSUFBTUwsaUJBQWlCbkY7Ozs7OztnREFHdEM7Z0RBQ0NWLG1CQUFtQm9ELE1BQU0sR0FBRyxtQkFDM0IsOERBQUM5RDtvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUMxQix5REFBTUE7NERBQ0xxSSxTQUFROzREQUNSQyxNQUFLOzREQUNMNUcsV0FBVTs0REFDVjZHLFNBQVMsSUFBTXZGLDRCQUNiRCwyQkFBMkIsSUFBSUEsMkJBQTJCLElBQUlWLG1CQUFtQm9ELE1BQU0sR0FBRztzRUFHNUYsNEVBQUM5RSxpTkFBV0E7Z0VBQUNlLFdBQVU7Ozs7Ozs7Ozs7O3NFQUV6Qiw4REFBQzFCLHlEQUFNQTs0REFDTHFJLFNBQVE7NERBQ1JDLE1BQUs7NERBQ0w1RyxXQUFVOzREQUNWNkcsU0FBUyxJQUFNdkYsNEJBQ2JELDJCQUEyQlYsbUJBQW1Cb0QsTUFBTSxHQUFHLElBQUkxQywyQkFBMkIsSUFBSTtzRUFHNUYsNEVBQUNyQyxpTkFBWUE7Z0VBQUNnQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FLL0JXLEVBQUFBLDZDQUFBQSxrQkFBa0IsQ0FBQ1UseUJBQXlCLGNBQTVDVixpRUFBQUEsMkNBQThDZ0ksT0FBTyxtQkFDcEQsOERBQUM3Qjs0Q0FBRTlHLFdBQVU7c0RBQ1ZXLGtCQUFrQixDQUFDVSx5QkFBeUIsQ0FBQ3NILE9BQU87Ozs7Ozt3Q0FHeERoSSxtQkFBbUJvRCxNQUFNLEdBQUcsbUJBQzNCLDhEQUFDOUQ7NENBQUlELFdBQVU7c0RBQ1pXLG1CQUFtQjhHLEdBQUcsQ0FBQyxDQUFDbUIsR0FBR25DLHNCQUMxQiw4REFBQ29DO29EQUVDN0ksV0FBVywwQ0FFVixPQURDeUcsVUFBVXBGLDJCQUEyQix3QkFBd0I7b0RBRS9Ed0YsU0FBUyxJQUFNdkYsNEJBQTRCbUY7bURBSnRDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFhbEIsQ0FBQ3RGLGlCQUFpQlIsbUJBQW1Cb0QsTUFBTSxHQUFHLG1CQUFLLDhEQUFDeEYsK0RBQVNBOzs7OztzQ0FHOUQsOERBQUMwQjs7OENBQ0MsOERBQUNzSDtvQ0FBR3ZILFdBQVU7O3NEQUNaLDhEQUFDcEIsaU5BQWFBOzRDQUFDb0IsV0FBVTs7Ozs7O3dDQUFZOzs7Ozs7OzhDQUd2Qyw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUM4STtvREFBSzlJLFdBQVU7OERBQWdDOzs7Ozs7OERBQ2hELDhEQUFDOEk7b0RBQUs5SSxXQUFVOzhEQUF1Qk87Ozs7Ozs7Ozs7OztzREFFekMsOERBQUNOOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQzhJO29EQUFLOUksV0FBVTs4REFBZ0M7Ozs7Ozs4REFDaEQsOERBQUM4STtvREFBSzlJLFdBQVU7OERBQ2JhLFVBQVVOLFlBQVksR0FBRyxNQUFNLFNBQVNNLFVBQVVOLFlBQVk7Ozs7Ozs7Ozs7OztzREFHbkUsOERBQUNOOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQzhJO29EQUFLOUksV0FBVTs4REFBZ0M7Ozs7Ozs4REFDaEQsOERBQUM4STtvREFBSzlJLFdBQVU7OERBQ2JhLFVBQVV1RyxTQUFTLEdBQUcsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU16Qyw4REFBQzdJLCtEQUFTQTs7Ozs7c0NBR1YsOERBQUMwQjs7OENBQ0MsOERBQUNzSDtvQ0FBR3ZILFdBQVU7O3NEQUNaLDhEQUFDdEIsaU5BQVFBOzRDQUFDc0IsV0FBVTs7Ozs7O3dDQUFZOzs7Ozs7OzhDQUdsQyw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUM4STtvREFBSzlJLFdBQVU7OERBQWdDOzs7Ozs7OERBQ2hELDhEQUFDOEk7b0RBQUs5SSxXQUFVOzhEQUNidUUsV0FBVzFELFVBQVVrSSxTQUFTOzs7Ozs7Ozs7Ozs7c0RBR25DLDhEQUFDOUk7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDOEk7b0RBQUs5SSxXQUFVOzhEQUFnQzs7Ozs7OzhEQUNoRCw4REFBQzhJO29EQUFLOUksV0FBVTs4REFDYnVFLFdBQVcxRCxVQUFVbUksU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU12Qyw4REFBQ3pLLCtEQUFTQTs7Ozs7d0JBR1QsQ0FBQzRDLGlCQUFpQnlELHNCQUFzQmIsTUFBTSxHQUFHLG1CQUNoRCw4REFBQzlEOzs4Q0FDQyw4REFBQ0E7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDdUg7NENBQUd2SCxXQUFVOzs4REFDWiw4REFBQ1YsaU5BQVFBO29EQUFDVSxXQUFVOzs7Ozs7Z0RBQVk7Ozs7Ozs7c0RBR2xDLDhEQUFDMUIseURBQU1BOzRDQUNMcUksU0FBUTs0Q0FDUkMsTUFBSzs0Q0FDTEMsU0FBUyxJQUFNbkYsMEJBQTBCLENBQUNEOzRDQUMxQ3pCLFdBQVU7c0RBRVR5Qix1Q0FBeUIsOERBQUN0QyxpTkFBU0E7Z0RBQUNhLFdBQVU7Ozs7O3FFQUFlLDhEQUFDZCxpTkFBV0E7Z0RBQUNjLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUl4RnlCLHdDQUNDLDhEQUFDeEI7b0NBQUlELFdBQVU7O3NEQUViLDhEQUFDQzs0Q0FDQ0QsV0FBVyw0REFJVixPQUhDdUIsdUJBQXVCLE9BQ25CLG1EQUNBOzRDQUVOc0YsU0FBUyxJQUFNaEMsdUJBQXVCO3NEQUV0Qyw0RUFBQzVFO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7OztrRUFDZiw4REFBQ0M7OzBFQUNDLDhEQUFDNkc7Z0VBQUU5RyxXQUFVOzBFQUFzQjs7Ozs7OzBFQUNuQyw4REFBQzhHO2dFQUFFOUcsV0FBVTswRUFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQU1sRDRFLHNCQUFzQjZDLEdBQUcsQ0FBQyxDQUFDN0Qsc0JBQzFCLDhEQUFDM0Q7Z0RBRUNELFdBQVcsNERBSVYsT0FIQ3VCLHVCQUF1QnFDLE1BQU1oRSxFQUFFLEdBQzNCLG1EQUNBO2dEQUVOaUgsU0FBUyxJQUFNaEMsdUJBQXVCakIsTUFBTWhFLEVBQUU7MERBRTlDLDRFQUFDSztvREFBSUQsV0FBVTs7d0RBQ1osZUFBZXFJLElBQUksQ0FBQ3pFLE1BQU1vRCxHQUFHLGtCQUM1Qiw4REFBQ1k7NERBQ0M5SCxLQUFLOEQsTUFBTW9ELEdBQUc7NERBQ2RoSCxXQUFVOzREQUNWc0ksS0FBSzs0REFDTEMsV0FBVzs0REFDWEMsU0FBUTs0REFDUmIsY0FBYyxDQUFDaEYsSUFBTUEsRUFBRWtGLGFBQWEsQ0FBQ0UsSUFBSTs0REFDekNDLGNBQWMsQ0FBQ3JGO2dFQUNiQSxFQUFFa0YsYUFBYSxDQUFDSSxLQUFLO2dFQUNyQnRGLEVBQUVrRixhQUFhLENBQUNLLFdBQVcsR0FBRzs0REFDaEM7Ozs7O2lGQUdGLDhEQUFDOUg7NERBQ0NOLEtBQUs4RCxNQUFNb0QsR0FBRzs0REFDZGpILEtBQUs2RCxNQUFNK0UsT0FBTyxJQUFJOzREQUN0QjNJLFdBQVU7Ozs7OztzRUFHZCw4REFBQ0M7OzhFQUNDLDhEQUFDNkc7b0VBQUU5RyxXQUFVOzhFQUNWNEQsTUFBTStFLE9BQU8sSUFBSTs7Ozs7OzhFQUVwQiw4REFBQzdCO29FQUFFOUcsV0FBVTs4RUFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQ0FqQzVDNEQsTUFBTWhFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQTJDeEIsQ0FBQ3VCLGlCQUFpQnlELHNCQUFzQmIsTUFBTSxHQUFHLG1CQUFLLDhEQUFDeEYsK0RBQVNBOzs7Ozs7Ozs7Ozs7Ozs7O1lBT3RFNEQsZ0NBQ0MsOERBQUNsQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FFYiw4REFBQzZJO2dDQUNDaEMsU0FBU2hFO2dDQUNUN0MsV0FBVTswQ0FFViw0RUFBQ2pCLGlOQUFDQTtvQ0FBQ2lCLFdBQVU7Ozs7Ozs7Ozs7OzRCQUlkVyxtQkFBbUJvRCxNQUFNLEdBQUcsbUJBQzNCOztrREFDRSw4REFBQzhFO3dDQUNDaEMsU0FBUzlEO3dDQUNUL0MsV0FBVTtrREFFViw0RUFBQ2YsaU5BQVdBOzRDQUFDZSxXQUFVOzs7Ozs7Ozs7OztrREFFekIsOERBQUM2STt3Q0FDQ2hDLFNBQVM3RDt3Q0FDVGhELFdBQVU7a0RBRVYsNEVBQUNoQixpTkFBWUE7NENBQUNnQixXQUFVOzs7Ozs7Ozs7Ozs7OzBDQU05Qiw4REFBQ0M7Z0NBQUlELFdBQVU7O29DQUNYO3dDQUNBLE1BQU00RCxRQUFRakQsa0JBQWtCLENBQUMwQixnQkFBZ0I7d0NBQ2pELE1BQU0yRSxNQUFNcEQsa0JBQUFBLDRCQUFBQSxNQUFPb0QsR0FBRzt3Q0FDdEIsT0FBT0EsT0FBTyxlQUFlcUIsSUFBSSxDQUFDckIscUJBQ2hDLDhEQUFDWTs0Q0FDQzlILEtBQUtrSDs0Q0FDTGhILFdBQVU7NENBQ1ZpSixRQUFROzRDQUNSQyxRQUFROzRDQUNSWixLQUFLOzRDQUNMQyxXQUFXOzs7OztpRUFHYiw4REFBQ25JOzRDQUNDTixLQUFLa0g7NENBQ0xqSCxLQUFLNkQsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPK0UsT0FBTyxLQUFJOzRDQUN2QjNJLFdBQVU7Ozs7OztvQ0FHaEI7b0NBR0NXLEVBQUFBLG9DQUFBQSxrQkFBa0IsQ0FBQzBCLGdCQUFnQixjQUFuQzFCLHdEQUFBQSxrQ0FBcUNnSSxPQUFPLG1CQUMzQyw4REFBQzFJO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDOEc7NENBQUU5RyxXQUFVO3NEQUNWVyxrQkFBa0IsQ0FBQzBCLGdCQUFnQixDQUFDc0csT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT25EaEksbUJBQW1Cb0QsTUFBTSxHQUFHLG1CQUMzQiw4REFBQzlEO2dDQUFJRCxXQUFVOztvQ0FDWnFDLGtCQUFrQjtvQ0FBRTtvQ0FBSTFCLG1CQUFtQm9ELE1BQU07Ozs7Ozs7Ozs7Ozs7a0NBTXhELDhEQUFDOUQ7d0JBQ0NELFdBQVU7d0JBQ1Y2RyxTQUFTaEU7Ozs7Ozs7Ozs7OzswQkFNZiw4REFBQ3JFLGlFQUFnQkE7Z0JBQ2ZnQyxRQUFRdUI7Z0JBQ1JvSCxTQUFTLElBQU1uSCxrQkFBa0I7Z0JBQ2pDb0gsV0FBV3ZEO2dCQUNYd0QsZUFBZXhJLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV3NFLElBQUksS0FBSTs7Ozs7Ozs7Ozs7O0FBSTFDO0dBdHdCZ0I5RTs7UUFPR2pDLDJEQUFPQTs7O01BUFZpQyIsInNvdXJjZXMiOlsiRTpcXGJlc3N0aWVrdVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcY2hhdFxcY2hhcmFjdGVyLXByb2ZpbGUtc2lkZWJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENoYXJhY3RlciwgQ2hhcmFjdGVyQXNzZXQgfSBmcm9tICdAL3R5cGVzL2NoYXJhY3Rlcic7XG5pbXBvcnQgeyBjaGFyYWN0ZXJTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9jaGFyYWN0ZXInO1xuaW1wb3J0IHsgZmF2b3JpdGVTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9mYXZvcml0ZSc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9hdXRoLWNvbnRleHQnO1xuXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IFNlcGFyYXRvciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zZXBhcmF0b3InO1xuaW1wb3J0IHsgUGhvbmVOdW1iZXJNb2RhbCB9IGZyb20gJy4vcGhvbmUtbnVtYmVyLW1vZGFsJztcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJztcbmltcG9ydCB7XG4gIENhbGVuZGFyLFxuICBUYWcsXG4gIE1lc3NhZ2VDaXJjbGUsXG4gIFN0YXIsXG4gIEhlYXJ0LFxuICBJbmZvLFxuICBYLFxuICBDaGV2cm9uUmlnaHQsXG4gIENoZXZyb25MZWZ0LFxuICBDaGV2cm9uRG93bixcbiAgQ2hldnJvblVwLFxuICBJbWFnZSBhcyBJbWFnZUljb24sXG4gIFNldHRpbmdzLFxuICBFeHRlcm5hbExpbmssXG4gIFNoYXJlMixcbiAgQ29weSxcbiAgQ2hlY2tcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGZvcm1hdERpc3RhbmNlVG9Ob3cgfSBmcm9tICdkYXRlLWZucyc7XG5pbXBvcnQgeyBpZCB9IGZyb20gJ2RhdGUtZm5zL2xvY2FsZSc7XG5cbi8vIFNpbXBsZSBhdmF0YXIgY29tcG9uZW50IHRoYXQgYWx3YXlzIHVzZXMgc3RhdGljIGltYWdlXG5jb25zdCBTaW1wbGVBdmF0YXIgPSAoeyBzcmMsIGFsdCwgY2xhc3NOYW1lIH06IHtcbiAgc3JjOiBzdHJpbmcgfCBudWxsO1xuICBhbHQ6IHN0cmluZztcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufSkgPT4ge1xuICBpZiAoIXNyYykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtMnhsIGZsZXggc2l6ZS1mdWxsIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWZ1bGwgYmctbXV0ZWQgJHtjbGFzc05hbWUgfHwgJyd9YH0+XG4gICAgICAgIHthbHQ/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnQyd9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8aW1nXG4gICAgICBzcmM9e3NyY31cbiAgICAgIGFsdD17YWx0fVxuICAgICAgY2xhc3NOYW1lPXtgYXNwZWN0LXNxdWFyZSBzaXplLWZ1bGwgb2JqZWN0LWNvdmVyICR7Y2xhc3NOYW1lIHx8ICcnfWB9XG4gICAgLz5cbiAgKTtcbn07XG5cbmludGVyZmFjZSBDaGFyYWN0ZXJQcm9maWxlU2lkZWJhclByb3BzIHtcbiAgY2hhcmFjdGVySWQ6IHN0cmluZztcbiAgbWVzc2FnZUNvdW50OiBudW1iZXI7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25Ub2dnbGU6ICgpID0+IHZvaWQ7XG4gIG9uQmFja2dyb3VuZENoYW5nZT86IChiYWNrZ3JvdW5kQXNzZXRJZDogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENoYXJhY3RlclByb2ZpbGVTaWRlYmFyKHtcbiAgY2hhcmFjdGVySWQsXG4gIG1lc3NhZ2VDb3VudCxcbiAgaXNPcGVuLFxuICBvblRvZ2dsZSxcbiAgb25CYWNrZ3JvdW5kQ2hhbmdlXG59OiBDaGFyYWN0ZXJQcm9maWxlU2lkZWJhclByb3BzKSB7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbY2hhcmFjdGVyLCBzZXRDaGFyYWN0ZXJdID0gdXNlU3RhdGU8Q2hhcmFjdGVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFthc3NldHMsIHNldEFzc2V0c10gPSB1c2VTdGF0ZTxDaGFyYWN0ZXJBc3NldFtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbYXNzZXRzTG9hZGluZywgc2V0QXNzZXRzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2N1cnJlbnRQcm9maWxlSW1hZ2VJbmRleCwgc2V0Q3VycmVudFByb2ZpbGVJbWFnZUluZGV4XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbc2VsZWN0ZWRCYWNrZ3JvdW5kLCBzZXRTZWxlY3RlZEJhY2tncm91bmRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaG93QmFja2dyb3VuZFNldHRpbmdzLCBzZXRTaG93QmFja2dyb3VuZFNldHRpbmdzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzRmF2b3JpdGUsIHNldElzRmF2b3JpdGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNGYXZvcml0ZUxvYWRpbmcsIHNldElzRmF2b3JpdGVMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dQaG9uZU1vZGFsLCBzZXRTaG93UGhvbmVNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsaW5rQ29waWVkLCBzZXRMaW5rQ29waWVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dJbWFnZU1vZGFsLCBzZXRTaG93SW1hZ2VNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFttb2RhbEltYWdlSW5kZXgsIHNldE1vZGFsSW1hZ2VJbmRleF0gPSB1c2VTdGF0ZSgwKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRDaGFyYWN0ZXIoKTtcbiAgICBsb2FkQXNzZXRzKCk7XG4gICAgY2hlY2tGYXZvcml0ZVN0YXR1cygpO1xuICB9LCBbY2hhcmFjdGVySWRdKTtcblxuICAvLyBLZXlib2FyZCBuYXZpZ2F0aW9uIGZvciBpbWFnZSBtb2RhbFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghc2hvd0ltYWdlTW9kYWwpIHJldHVybjtcblxuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZTogS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgICAgc3dpdGNoIChlLmtleSkge1xuICAgICAgICBjYXNlICdFc2NhcGUnOlxuICAgICAgICAgIGhhbmRsZU1vZGFsQ2xvc2UoKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnQXJyb3dMZWZ0JzpcbiAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgaGFuZGxlTW9kYWxQcmV2aW91cygpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdBcnJvd1JpZ2h0JzpcbiAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgaGFuZGxlTW9kYWxOZXh0KCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICB9LCBbc2hvd0ltYWdlTW9kYWwsIG1vZGFsSW1hZ2VJbmRleF0pO1xuXG4gIGNvbnN0IGxvYWRDaGFyYWN0ZXIgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBjaGFyYWN0ZXJEYXRhID0gYXdhaXQgY2hhcmFjdGVyU2VydmljZS5nZXRDaGFyYWN0ZXJCeUlkKGNoYXJhY3RlcklkKTtcbiAgICAgIHNldENoYXJhY3RlcihjaGFyYWN0ZXJEYXRhKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgY2hhcmFjdGVyOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvYWRBc3NldHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEFzc2V0c0xvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBhc3NldHNEYXRhID0gYXdhaXQgY2hhcmFjdGVyU2VydmljZS5nZXRDaGFyYWN0ZXJBc3NldHMoY2hhcmFjdGVySWQpO1xuICAgICAgc2V0QXNzZXRzKGFzc2V0c0RhdGEpO1xuICAgICAgXG4gICAgICAvLyBBdXRvLXNlbGVjdCBmaXJzdCBiYWNrZ3JvdW5kIGlmIGNoYXJhY3RlciBoYXMgYmFja2dyb3VuZHMgYW5kIG5vIGJhY2tncm91bmQgaXMgY3VycmVudGx5IHNlbGVjdGVkXG4gICAgICBjb25zdCBiYWNrZ3JvdW5kQXNzZXRzID0gYXNzZXRzRGF0YS5maWx0ZXIoYXNzZXQgPT4gYXNzZXQucHVycG9zZSA9PT0gJ2JhY2tncm91bmQnICYmIGFzc2V0LmlzUHVibGlzaGVkKTtcbiAgICAgIGlmIChiYWNrZ3JvdW5kQXNzZXRzLmxlbmd0aCA+IDAgJiYgc2VsZWN0ZWRCYWNrZ3JvdW5kID09PSBudWxsKSB7XG4gICAgICAgIGNvbnN0IGRlZmF1bHRCYWNrZ3JvdW5kID0gYmFja2dyb3VuZEFzc2V0c1swXS5pZDtcbiAgICAgICAgc2V0U2VsZWN0ZWRCYWNrZ3JvdW5kKGRlZmF1bHRCYWNrZ3JvdW5kKTtcbiAgICAgICAgb25CYWNrZ3JvdW5kQ2hhbmdlPy4oZGVmYXVsdEJhY2tncm91bmQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBjaGFyYWN0ZXIgYXNzZXRzOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QXNzZXRzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNoZWNrRmF2b3JpdGVTdGF0dXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGZhdm9yaXRlc1Jlc3BvbnNlID0gYXdhaXQgZmF2b3JpdGVTZXJ2aWNlLmdldEZhdm9yaXRlcygpO1xuICAgICAgY29uc3QgaXNGYXZvcml0ZWQgPSBmYXZvcml0ZXNSZXNwb25zZS5kYXRhLnNvbWUoZmF2ID0+IGZhdi5pZCA9PT0gY2hhcmFjdGVySWQpO1xuICAgICAgc2V0SXNGYXZvcml0ZShpc0Zhdm9yaXRlZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjaGVjayBmYXZvcml0ZSBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgLy8gRG9uJ3Qgc2hvdyBlcnJvciB0b2FzdCBmb3IgdGhpcywganVzdCBrZWVwIGRlZmF1bHQgc3RhdGVcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIGZvcm1hdERpc3RhbmNlVG9Ob3cobmV3IERhdGUoZGF0ZVN0cmluZyksIHsgYWRkU3VmZml4OiB0cnVlLCBsb2NhbGU6IGlkIH0pO1xuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuICdVbmtub3duJztcbiAgICB9XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9ucyBmb3IgYXNzZXRzXG4gIGNvbnN0IGdldFByb2ZpbGVBc3NldHMgPSAoKSA9PiB7XG4gICAgcmV0dXJuIGFzc2V0cy5maWx0ZXIoYXNzZXQgPT4gYXNzZXQucHVycG9zZSA9PT0gJ3Byb2ZpbGUnICYmIGFzc2V0LmlzUHVibGlzaGVkKTtcbiAgfTtcblxuICBjb25zdCBnZXRCYWNrZ3JvdW5kQXNzZXRzID0gKCkgPT4ge1xuICAgIHJldHVybiBhc3NldHMuZmlsdGVyKGFzc2V0ID0+IGFzc2V0LnB1cnBvc2UgPT09ICdiYWNrZ3JvdW5kJyAmJiBhc3NldC5pc1B1Ymxpc2hlZCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQmFja2dyb3VuZENoYW5nZSA9IChiYWNrZ3JvdW5kSWQ6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBzZXRTZWxlY3RlZEJhY2tncm91bmQoYmFja2dyb3VuZElkKTtcbiAgICBvbkJhY2tncm91bmRDaGFuZ2U/LihiYWNrZ3JvdW5kSWQpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUZhdm9yaXRlVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY2hhcmFjdGVyKSByZXR1cm47XG5cbiAgICBzZXRJc0Zhdm9yaXRlTG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgaWYgKGlzRmF2b3JpdGUpIHtcbiAgICAgICAgYXdhaXQgZmF2b3JpdGVTZXJ2aWNlLnJlbW92ZUZyb21GYXZvcml0ZShjaGFyYWN0ZXIuaWQpO1xuICAgICAgICBzZXRJc0Zhdm9yaXRlKGZhbHNlKTtcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkRpaGFwdXMgZGFyaSBmYXZvcml0XCIsIHtcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYCR7Y2hhcmFjdGVyLm5hbWV9IHRlbGFoIGRpaGFwdXMgZGFyaSBkYWZ0YXIgZmF2b3JpdCBBbmRhLmAsXG4gICAgICAgICAgZHVyYXRpb246IDMwMDAsXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYXdhaXQgZmF2b3JpdGVTZXJ2aWNlLmFkZFRvRmF2b3JpdGUoY2hhcmFjdGVyLmlkKTtcbiAgICAgICAgc2V0SXNGYXZvcml0ZSh0cnVlKTtcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkRpdGFtYmFoa2FuIGtlIGZhdm9yaXRcIiwge1xuICAgICAgICAgIGRlc2NyaXB0aW9uOiBgJHtjaGFyYWN0ZXIubmFtZX0gdGVsYWggZGl0YW1iYWhrYW4ga2UgZGFmdGFyIGZhdm9yaXQgQW5kYS5gLFxuICAgICAgICAgIGR1cmF0aW9uOiAzMDAwLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3QuZXJyb3IoXCJHYWdhbCBtZW5ndWJhaCBmYXZvcml0XCIsIHtcbiAgICAgICAgZGVzY3JpcHRpb246IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJUZXJqYWRpIGtlc2FsYWhhbiBzYWF0IG1lbmd1YmFoIHN0YXR1cyBmYXZvcml0LlwiLFxuICAgICAgICBkdXJhdGlvbjogNDAwMCxcbiAgICAgIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0Zhdm9yaXRlTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVdoYXRzQXBwQ2xpY2sgPSAoKSA9PiB7XG4gICAgaWYgKCFjaGFyYWN0ZXI/LndoYXRzYXBwVXJsKSByZXR1cm47XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGhhcyBwaG9uZSBudW1iZXJcbiAgICBpZiAoIXVzZXI/LnBob25lTnVtYmVyKSB7XG4gICAgICBzZXRTaG93UGhvbmVNb2RhbCh0cnVlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBJZiB1c2VyIGhhcyBwaG9uZSBudW1iZXIsIHByb2NlZWQgdG8gV2hhdHNBcHBcbiAgICB3aW5kb3cub3BlbihjaGFyYWN0ZXIud2hhdHNhcHBVcmwsICdfYmxhbmsnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVQaG9uZU1vZGFsU3VjY2VzcyA9ICgpID0+IHtcbiAgICBpZiAoY2hhcmFjdGVyPy53aGF0c2FwcFVybCkge1xuICAgICAgd2luZG93Lm9wZW4oY2hhcmFjdGVyLndoYXRzYXBwVXJsLCAnX2JsYW5rJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNvcHlMaW5rID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY2hhcmFjdGVyKSByZXR1cm47XG5cbiAgICBjb25zdCBjaGFyYWN0ZXJVcmwgPSBgJHt3aW5kb3cubG9jYXRpb24ub3JpZ2lufS9kYXNoYm9hcmQ/Y2hhcmFjdGVyPSR7Y2hhcmFjdGVyLmlkfWA7XG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoY2hhcmFjdGVyVXJsKTtcbiAgICAgIHNldExpbmtDb3BpZWQodHJ1ZSk7XG4gICAgICB0b2FzdC5zdWNjZXNzKFwiTGluayBkaXNhbGluIVwiLCB7XG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkxpbmsga2FyYWt0ZXIgdGVsYWggZGlzYWxpbiBrZSBjbGlwYm9hcmQuIEJhZ2lrYW4ga2UgdGVtYW4tdGVtYW5tdSFcIixcbiAgICAgICAgZHVyYXRpb246IDMwMDAsXG4gICAgICB9KTtcblxuICAgICAgLy8gUmVzZXQgY29waWVkIHN0YXRlIGFmdGVyIDMgc2Vjb25kc1xuICAgICAgc2V0VGltZW91dCgoKSA9PiBzZXRMaW5rQ29waWVkKGZhbHNlKSwgMzAwMCk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNvcHkgbGluazonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcihcIkdhZ2FsIG1lbnlhbGluIGxpbmtcIiwge1xuICAgICAgICBkZXNjcmlwdGlvbjogXCJUZXJqYWRpIGtlc2FsYWhhbiBzYWF0IG1lbnlhbGluIGxpbmsga2FyYWt0ZXIuXCIsXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwLFxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNoYXJlVG9XaGF0c0FwcCA9ICgpID0+IHtcbiAgICBpZiAoIWNoYXJhY3RlcikgcmV0dXJuO1xuXG4gICAgY29uc3QgY2hhcmFjdGVyVXJsID0gYCR7d2luZG93LmxvY2F0aW9uLm9yaWdpbn0vZGFzaGJvYXJkP2NoYXJhY3Rlcj0ke2NoYXJhY3Rlci5pZH1gO1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBgSGFsbyEgQXlvIGNoYXQgZGVuZ2FuICR7Y2hhcmFjdGVyLm5hbWV9IGRpIEJlc3RpZWt1ISDwn6SWXFxuXFxuJHtjaGFyYWN0ZXIuZGVzY3JpcHRpb259XFxuXFxuS2xpayBsaW5rIGluaTogJHtjaGFyYWN0ZXJVcmx9YDtcbiAgICBjb25zdCB3aGF0c2FwcFVybCA9IGBodHRwczovL3dhLm1lLz90ZXh0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KG1lc3NhZ2UpfWA7XG5cbiAgICB3aW5kb3cub3Blbih3aGF0c2FwcFVybCwgJ19ibGFuaycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUltYWdlQ2xpY2sgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIHNldE1vZGFsSW1hZ2VJbmRleChpbmRleCk7XG4gICAgc2V0U2hvd0ltYWdlTW9kYWwodHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTW9kYWxDbG9zZSA9ICgpID0+IHtcbiAgICBzZXRTaG93SW1hZ2VNb2RhbChmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTW9kYWxQcmV2aW91cyA9ICgpID0+IHtcbiAgICBjb25zdCBwcm9maWxlQXNzZXRzID0gZ2V0UHJvZmlsZUFzc2V0cygpO1xuICAgIHNldE1vZGFsSW1hZ2VJbmRleChtb2RhbEltYWdlSW5kZXggPiAwID8gbW9kYWxJbWFnZUluZGV4IC0gMSA6IHByb2ZpbGVBc3NldHMubGVuZ3RoIC0gMSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTW9kYWxOZXh0ID0gKCkgPT4ge1xuICAgIGNvbnN0IHByb2ZpbGVBc3NldHMgPSBnZXRQcm9maWxlQXNzZXRzKCk7XG4gICAgc2V0TW9kYWxJbWFnZUluZGV4KG1vZGFsSW1hZ2VJbmRleCA8IHByb2ZpbGVBc3NldHMubGVuZ3RoIC0gMSA/IG1vZGFsSW1hZ2VJbmRleCArIDEgOiAwKTtcbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGJnLWJhY2tncm91bmQgYm9yZGVyLWwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZsZXggZmxleC1jb2wgaC1mdWxsIG92ZXJmbG93LWhpZGRlbiAke1xuICAgICAgICBpc09wZW4gPyAndy04MCcgOiAndy0xMidcbiAgICAgIH1gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMjAgcC00IGJvcmRlci1iIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNPcGVuID8gPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz4gOiA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+fVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtMzIgYmctbXV0ZWQgcm91bmRlZC1sZ1wiIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02IGJnLW11dGVkIHJvdW5kZWQgdy0zLzRcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1tdXRlZCByb3VuZGVkIHctZnVsbFwiIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0yLzNcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFjaGFyYWN0ZXIpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BiZy1iYWNrZ3JvdW5kIGJvcmRlci1sIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmbGV4IGZsZXgtY29sIGgtZnVsbCBvdmVyZmxvdy1oaWRkZW4gJHtcbiAgICAgICAgaXNPcGVuID8gJ3ctODAnIDogJ3ctMTInXG4gICAgICB9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBoLTIwIHAtNCBib3JkZXItYiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzT3BlbiA/IDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+IDogPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge2lzT3BlbiAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICA8SW5mbyBjbGFzc05hbWU9XCJ3LTggaC04IG14LWF1dG8gbWItMlwiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj5LYXJha3RlciB0aWRhayBkaXRlbXVrYW48L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgYmctYmFja2dyb3VuZCBib3JkZXItbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZmxleCBmbGV4LWNvbCBoLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuICR7XG4gICAgICBpc09wZW4gPyAndy04MCcgOiAndy0xMidcbiAgICB9YH0+XG4gICAgICB7LyogVG9nZ2xlIEJ1dHRvbiAtIEZpeGVkICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMjAgcC00IGJvcmRlci1iIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICA+XG4gICAgICAgICAge2lzT3BlbiA/IDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+IDogPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFByb2ZpbGUgQ29udGVudCAtIFNjcm9sbGFibGUgKi99XG4gICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIG92ZXJmbG93LXgtaGlkZGVuIHAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogQ2hhcmFjdGVyIEF2YXRhciAmIE5hbWUgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLTI0IG14LWF1dG8gbWItNCByZWxhdGl2ZSBmbGV4IHNocmluay0wIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICA8U2ltcGxlQXZhdGFyXG4gICAgICAgICAgICAgICAgICBzcmM9eygoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEdldCBmaXJzdCBwcm9maWxlIGltYWdlIGFzc2V0IChub3QgdmlkZW8pXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByb2ZpbGVBc3NldHMgPSBnZXRQcm9maWxlQXNzZXRzKCkuZmlsdGVyKGFzc2V0ID0+IGFzc2V0LnR5cGUgPT09ICdpbWFnZScpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcHJvZmlsZUFzc2V0cy5sZW5ndGggPiAwID8gcHJvZmlsZUFzc2V0c1swXS51cmwgOiBjaGFyYWN0ZXIuaW1hZ2U7XG4gICAgICAgICAgICAgICAgICB9KSgpfVxuICAgICAgICAgICAgICAgICAgYWx0PXtjaGFyYWN0ZXIubmFtZX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItMVwiPntjaGFyYWN0ZXIubmFtZX08L2gyPlxuXG4gICAgICAgICAgICAgIHtjaGFyYWN0ZXIudGl0bGUgJiYgKFxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIHtjaGFyYWN0ZXIudGl0bGV9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTIgbWItM1wiPlxuICAgICAgICAgICAgICAgIHtjaGFyYWN0ZXIuc3RvcnlNb2RlICYmIChcbiAgICAgICAgICAgICAgICAgIDxCYWRnZSBjbGFzc05hbWU9XCJiZy1iZXN0aWVrdS1wcmltYXJ5IHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgTW9kZSBDZXJpdGFcbiAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJjYXBpdGFsaXplXCI+XG4gICAgICAgICAgICAgICAgICB7Y2hhcmFjdGVyLnN0YXR1c31cbiAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogV2hhdHNBcHAgQnV0dG9uIC0gU2hvdyBwcm9taW5lbnRseSBpZiBhdmFpbGFibGUgKi99XG4gICAgICAgICAgICAgIHtjaGFyYWN0ZXIud2hhdHNhcHBVcmwgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJkZWZhdWx0XCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlV2hhdHNBcHBDbGlja31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIENoYXQgZGkgV2hhdHNBcHBcbiAgICAgICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBGYXZvcml0ZSBCdXR0b24gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD17aXNGYXZvcml0ZSA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRmF2b3JpdGVUb2dnbGV9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNGYXZvcml0ZUxvYWRpbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAke1xuICAgICAgICAgICAgICAgICAgICBpc0Zhdm9yaXRlXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctcmVkLTUwMCBob3ZlcjpiZy1yZWQtNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnaG92ZXI6YmctcmVkLTUwIGhvdmVyOnRleHQtcmVkLTYwMCBob3Zlcjpib3JkZXItcmVkLTMwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxIZWFydFxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00ICR7aXNGYXZvcml0ZSA/ICdmaWxsLWN1cnJlbnQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7aXNGYXZvcml0ZUxvYWRpbmdcbiAgICAgICAgICAgICAgICAgICAgPyAnTG9hZGluZy4uLidcbiAgICAgICAgICAgICAgICAgICAgOiBpc0Zhdm9yaXRlXG4gICAgICAgICAgICAgICAgICAgICAgPyAnRmF2b3JpdCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdUYW1iYWgga2UgRmF2b3JpdCdcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFNoYXJlIENoYXJhY3RlciBCdXR0b25zIC0gTW92ZWQgdG8gdG9wIGZvciBiZXR0ZXIgdmlzaWJpbGl0eSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGdhcC0yIG1iLTNcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29weUxpbmt9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBmbGV4LTFcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtsaW5rQ29waWVkID8gKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBMaW5rIERpc2FsaW4hXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8Q29weSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBTYWxpbiBMaW5rXG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2hhcmVUb1doYXRzQXBwfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgYmctZ3JlZW4tNTAgaG92ZXI6YmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNzAwIGJvcmRlci1ncmVlbi0yMDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTaGFyZTIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBXaGF0c0FwcFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8U2VwYXJhdG9yIC8+XG5cbiAgICAgICAgICAgIHsvKiBEZXNjcmlwdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8SW5mbyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICBUZW50YW5nXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgIHtjaGFyYWN0ZXIuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8U2VwYXJhdG9yIC8+XG5cbiAgICAgICAgICAgIHsvKiBUYWdzICovfVxuICAgICAgICAgICAge2NoYXJhY3Rlci50YWdzICYmIGNoYXJhY3Rlci50YWdzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTMgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxUYWcgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBUYWdcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtjaGFyYWN0ZXIudGFncy5tYXAoKHRhZykgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2Uga2V5PXt0YWd9IHZhcmlhbnQ9XCJzZWNvbmRhcnlcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3RhZ31cbiAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIDxTZXBhcmF0b3IgLz5cblxuICAgICAgICAgICAgey8qIFByb2ZpbGUgR2FsbGVyeSAqL31cbiAgICAgICAgICAgIHshYXNzZXRzTG9hZGluZyAmJiBnZXRQcm9maWxlQXNzZXRzKCkubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgbWItMyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIEdhbGVyaSBQcm9maWxcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlXCJcbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZpZGVvID0gZS5jdXJyZW50VGFyZ2V0LnF1ZXJ5U2VsZWN0b3IoJ3ZpZGVvJyk7XG4gICAgICAgICAgICAgICAgICAgICAgaWYgKHZpZGVvKSB2aWRlby5wbGF5KCk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2aWRlbyA9IGUuY3VycmVudFRhcmdldC5xdWVyeVNlbGVjdG9yKCd2aWRlbycpO1xuICAgICAgICAgICAgICAgICAgICAgIGlmICh2aWRlbykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmlkZW8ucGF1c2UoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZpZGVvLmN1cnJlbnRUaW1lID0gMDtcbiAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIG9uVG91Y2hTdGFydD17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2aWRlbyA9IGUuY3VycmVudFRhcmdldC5xdWVyeVNlbGVjdG9yKCd2aWRlbycpO1xuICAgICAgICAgICAgICAgICAgICAgIGlmICh2aWRlbykgdmlkZW8ucGxheSgpO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBvblRvdWNoRW5kPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZpZGVvID0gZS5jdXJyZW50VGFyZ2V0LnF1ZXJ5U2VsZWN0b3IoJ3ZpZGVvJyk7XG4gICAgICAgICAgICAgICAgICAgICAgaWYgKHZpZGVvKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2aWRlby5wYXVzZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdmlkZW8uY3VycmVudFRpbWUgPSAwO1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgYXNzZXQgPSBnZXRQcm9maWxlQXNzZXRzKClbY3VycmVudFByb2ZpbGVJbWFnZUluZGV4XTtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cmwgPSBhc3NldD8udXJsO1xuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB1cmwgJiYgL1xcLm1wNChcXD98JCkvaS50ZXN0KHVybCkgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8dmlkZW9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXt1cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTQ4IG9iamVjdC1jb3ZlciByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIGhvdmVyOm9wYWNpdHktOTAgdHJhbnNpdGlvbi1vcGFjaXR5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbXV0ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxheXNJbmxpbmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcHJlbG9hZD1cIm1ldGFkYXRhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zdGVyPXt1cmwgKyBcIiN0PTAuMVwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkxvYWRlZERhdGE9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LmN1cnJlbnRUaW1lID0gMDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlSW1hZ2VDbGljayhjdXJyZW50UHJvZmlsZUltYWdlSW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3VybH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXthc3NldD8uY2FwdGlvbiB8fCAnUHJvZmlsZSBpbWFnZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTQ4IG9iamVjdC1jb3ZlciByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIGhvdmVyOm9wYWNpdHktOTAgdHJhbnNpdGlvbi1vcGFjaXR5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlSW1hZ2VDbGljayhjdXJyZW50UHJvZmlsZUltYWdlSW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICB9KSgpfVxuICAgICAgICAgICAgICAgICAgICB7Z2V0UHJvZmlsZUFzc2V0cygpLmxlbmd0aCA+IDEgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQteC0wIGJvdHRvbS0yIGZsZXgganVzdGlmeS1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInNlY29uZGFyeVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ibGFjay81MCBob3ZlcjpiZy1ibGFjay83MCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFByb2ZpbGVJbWFnZUluZGV4KFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRQcm9maWxlSW1hZ2VJbmRleCA+IDAgPyBjdXJyZW50UHJvZmlsZUltYWdlSW5kZXggLSAxIDogZ2V0UHJvZmlsZUFzc2V0cygpLmxlbmd0aCAtIDFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25MZWZ0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggYmctYmxhY2svNTAgaG92ZXI6YmctYmxhY2svNzAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQcm9maWxlSW1hZ2VJbmRleChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UHJvZmlsZUltYWdlSW5kZXggPCBnZXRQcm9maWxlQXNzZXRzKCkubGVuZ3RoIC0gMSA/IGN1cnJlbnRQcm9maWxlSW1hZ2VJbmRleCArIDEgOiAwXG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAge2dldFByb2ZpbGVBc3NldHMoKVtjdXJyZW50UHJvZmlsZUltYWdlSW5kZXhdPy5jYXB0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Z2V0UHJvZmlsZUFzc2V0cygpW2N1cnJlbnRQcm9maWxlSW1hZ2VJbmRleF0uY2FwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIHtnZXRQcm9maWxlQXNzZXRzKCkubGVuZ3RoID4gMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRQcm9maWxlQXNzZXRzKCkubWFwKChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID09PSBjdXJyZW50UHJvZmlsZUltYWdlSW5kZXggPyAnYmctYmVzdGlla3UtcHJpbWFyeScgOiAnYmctbXV0ZWQnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50UHJvZmlsZUltYWdlSW5kZXgoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHshYXNzZXRzTG9hZGluZyAmJiBnZXRQcm9maWxlQXNzZXRzKCkubGVuZ3RoID4gMCAmJiA8U2VwYXJhdG9yIC8+fVxuXG4gICAgICAgICAgICB7LyogQ2hhdCBTdGF0cyAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTMgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICBTdGF0aXN0aWsgQ2hhdFxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlBlc2FuIEFuZGE8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e21lc3NhZ2VDb3VudH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+VG90YWwgUGVzYW48L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmVzdGlla3UtcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgICAgICB7Y2hhcmFjdGVyLm1lc3NhZ2VDb3VudCA+IDk5OSA/ICc5OTkrJyA6IGNoYXJhY3Rlci5tZXNzYWdlQ291bnR9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+SmVuaXMgS2FyYWt0ZXI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIHtjaGFyYWN0ZXIuc3RvcnlNb2RlID8gJ1N0b3J5JyA6ICdDaGF0J31cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPFNlcGFyYXRvciAvPlxuXG4gICAgICAgICAgICB7LyogQ2hhcmFjdGVyIEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBtYi0zIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgIEluZm8gS2FyYWt0ZXJcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5EaWJ1YXQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGNoYXJhY3Rlci5jcmVhdGVkQXQpfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkRpcGVyYmFydWk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGNoYXJhY3Rlci51cGRhdGVkQXQpfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8U2VwYXJhdG9yIC8+XG5cbiAgICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIFNldHRpbmdzICovfVxuICAgICAgICAgICAgeyFhc3NldHNMb2FkaW5nICYmIGdldEJhY2tncm91bmRBc3NldHMoKS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIExhdGFyIEJlbGFrYW5nIENoYXRcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0JhY2tncm91bmRTZXR0aW5ncyghc2hvd0JhY2tncm91bmRTZXR0aW5ncyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTZcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7c2hvd0JhY2tncm91bmRTZXR0aW5ncyA/IDxDaGV2cm9uVXAgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+IDogPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAge3Nob3dCYWNrZ3JvdW5kU2V0dGluZ3MgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgey8qIERlZmF1bHQgYmFja2dyb3VuZCBvcHRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyBib3JkZXItMiBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRCYWNrZ3JvdW5kID09PSBudWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1iZXN0aWVrdS1wcmltYXJ5IGJnLWJlc3RpZWt1LXByaW1hcnkvMTAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1tdXRlZCBob3Zlcjpib3JkZXItbXV0ZWQtZm9yZWdyb3VuZC81MCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVCYWNrZ3JvdW5kQ2hhbmdlKG51bGwpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtOCBiZy1ncmFkaWVudC10by1iIGZyb20tYmFja2dyb3VuZCB0by1tdXRlZC8yMCByb3VuZGVkIGJvcmRlclwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkRlZmF1bHQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TGF0YXIgYmVsYWthbmcgcG9sb3M8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEJhY2tncm91bmQgYXNzZXQgb3B0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAge2dldEJhY2tncm91bmRBc3NldHMoKS5tYXAoKGFzc2V0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXthc3NldC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMyByb3VuZGVkLWxnIGJvcmRlci0yIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQmFja2dyb3VuZCA9PT0gYXNzZXQuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmVzdGlla3UtcHJpbWFyeSBiZy1iZXN0aWVrdS1wcmltYXJ5LzEwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1tdXRlZCBob3Zlcjpib3JkZXItbXV0ZWQtZm9yZWdyb3VuZC81MCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQmFja2dyb3VuZENoYW5nZShhc3NldC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7L1xcLm1wNChcXD98JCkvaS50ZXN0KGFzc2V0LnVybCkgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHZpZGVvXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2Fzc2V0LnVybH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTIgaC04IG9iamVjdC1jb3ZlciByb3VuZGVkIGJvcmRlciBwb2ludGVyLWV2ZW50cy1ub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG11dGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGF5c0lubGluZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJlbG9hZD1cIm1ldGFkYXRhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IGUuY3VycmVudFRhcmdldC5wbGF5KCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5wYXVzZSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuY3VycmVudFRpbWUgPSAwO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17YXNzZXQudXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXthc3NldC5jYXB0aW9uIHx8ICdCYWNrZ3JvdW5kJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTIgaC04IG9iamVjdC1jb3ZlciByb3VuZGVkIGJvcmRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXNzZXQuY2FwdGlvbiB8fCAnTGF0YXIgQmVsYWthbmcgS3VzdG9tJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5MYXRhciBiZWxha2FuZyBrYXJha3RlcjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHshYXNzZXRzTG9hZGluZyAmJiBnZXRCYWNrZ3JvdW5kQXNzZXRzKCkubGVuZ3RoID4gMCAmJiA8U2VwYXJhdG9yIC8+fVxuXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIEltYWdlIE1vZGFsICovfVxuICAgICAge3Nob3dJbWFnZU1vZGFsICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmxhY2svODAgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWF4LXctNHhsIG1heC1oLVs5MHZoXSB3LWZ1bGwgbXgtNFwiPlxuICAgICAgICAgICAgey8qIENsb3NlIGJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTW9kYWxDbG9zZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNCB6LTEwIHAtMiBiZy1ibGFjay81MCBob3ZlcjpiZy1ibGFjay83MCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIGJ1dHRvbnMgKi99XG4gICAgICAgICAgICB7Z2V0UHJvZmlsZUFzc2V0cygpLmxlbmd0aCA+IDEgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU1vZGFsUHJldmlvdXN9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTQgdG9wLTEvMiAtdHJhbnNsYXRlLXktMS8yIHotMTAgcC0yIGJnLWJsYWNrLzUwIGhvdmVyOmJnLWJsYWNrLzcwIHRleHQtd2hpdGUgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTW9kYWxOZXh0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtNCB0b3AtMS8yIC10cmFuc2xhdGUteS0xLzIgei0xMCBwLTIgYmctYmxhY2svNTAgaG92ZXI6YmctYmxhY2svNzAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEltYWdlIGNvbnRlbnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWJsYWNrIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgIHsoKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGFzc2V0ID0gZ2V0UHJvZmlsZUFzc2V0cygpW21vZGFsSW1hZ2VJbmRleF07XG4gICAgICAgICAgICAgICAgY29uc3QgdXJsID0gYXNzZXQ/LnVybDtcbiAgICAgICAgICAgICAgICByZXR1cm4gdXJsICYmIC9cXC5tcDQoXFw/fCQpL2kudGVzdCh1cmwpID8gKFxuICAgICAgICAgICAgICAgICAgPHZpZGVvXG4gICAgICAgICAgICAgICAgICAgIHNyYz17dXJsfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LWgtWzgwdmhdIG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgICAgYXV0b1BsYXlcbiAgICAgICAgICAgICAgICAgICAgbXV0ZWRcbiAgICAgICAgICAgICAgICAgICAgcGxheXNJbmxpbmVcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgc3JjPXt1cmx9XG4gICAgICAgICAgICAgICAgICAgIGFsdD17YXNzZXQ/LmNhcHRpb24gfHwgJ1Byb2ZpbGUgaW1hZ2UnfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LWgtWzgwdmhdIG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgfSkoKX1cblxuICAgICAgICAgICAgICB7LyogQ2FwdGlvbiAqL31cbiAgICAgICAgICAgICAge2dldFByb2ZpbGVBc3NldHMoKVttb2RhbEltYWdlSW5kZXhdPy5jYXB0aW9uICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibGFjay84MCB0by10cmFuc3BhcmVudCBwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAge2dldFByb2ZpbGVBc3NldHMoKVttb2RhbEltYWdlSW5kZXhdLmNhcHRpb259XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEltYWdlIGNvdW50ZXIgKi99XG4gICAgICAgICAgICB7Z2V0UHJvZmlsZUFzc2V0cygpLmxlbmd0aCA+IDEgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS00IGxlZnQtMS8yIC10cmFuc2xhdGUteC0xLzIgYmctYmxhY2svNTAgdGV4dC13aGl0ZSBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7bW9kYWxJbWFnZUluZGV4ICsgMX0gLyB7Z2V0UHJvZmlsZUFzc2V0cygpLmxlbmd0aH1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEJhY2tncm91bmQgb3ZlcmxheSAtIGNsaWNrIHRvIGNsb3NlICovfVxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgLXotMTBcIlxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTW9kYWxDbG9zZX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBQaG9uZSBOdW1iZXIgTW9kYWwgKi99XG4gICAgICA8UGhvbmVOdW1iZXJNb2RhbFxuICAgICAgICBpc09wZW49e3Nob3dQaG9uZU1vZGFsfVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93UGhvbmVNb2RhbChmYWxzZSl9XG4gICAgICAgIG9uU3VjY2Vzcz17aGFuZGxlUGhvbmVNb2RhbFN1Y2Nlc3N9XG4gICAgICAgIGNoYXJhY3Rlck5hbWU9e2NoYXJhY3Rlcj8ubmFtZSB8fCAnQ2hhcmFjdGVyJ31cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImNoYXJhY3RlclNlcnZpY2UiLCJmYXZvcml0ZVNlcnZpY2UiLCJ1c2VBdXRoIiwiQmFkZ2UiLCJCdXR0b24iLCJTZXBhcmF0b3IiLCJQaG9uZU51bWJlck1vZGFsIiwidG9hc3QiLCJDYWxlbmRhciIsIlRhZyIsIk1lc3NhZ2VDaXJjbGUiLCJIZWFydCIsIkluZm8iLCJYIiwiQ2hldnJvblJpZ2h0IiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uRG93biIsIkNoZXZyb25VcCIsIkltYWdlIiwiSW1hZ2VJY29uIiwiU2V0dGluZ3MiLCJFeHRlcm5hbExpbmsiLCJTaGFyZTIiLCJDb3B5IiwiQ2hlY2siLCJmb3JtYXREaXN0YW5jZVRvTm93IiwiaWQiLCJTaW1wbGVBdmF0YXIiLCJzcmMiLCJhbHQiLCJjbGFzc05hbWUiLCJkaXYiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsImltZyIsIkNoYXJhY3RlclByb2ZpbGVTaWRlYmFyIiwiY2hhcmFjdGVySWQiLCJtZXNzYWdlQ291bnQiLCJpc09wZW4iLCJvblRvZ2dsZSIsIm9uQmFja2dyb3VuZENoYW5nZSIsImdldFByb2ZpbGVBc3NldHMiLCJ1c2VyIiwiY2hhcmFjdGVyIiwic2V0Q2hhcmFjdGVyIiwiYXNzZXRzIiwic2V0QXNzZXRzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJhc3NldHNMb2FkaW5nIiwic2V0QXNzZXRzTG9hZGluZyIsImN1cnJlbnRQcm9maWxlSW1hZ2VJbmRleCIsInNldEN1cnJlbnRQcm9maWxlSW1hZ2VJbmRleCIsInNlbGVjdGVkQmFja2dyb3VuZCIsInNldFNlbGVjdGVkQmFja2dyb3VuZCIsInNob3dCYWNrZ3JvdW5kU2V0dGluZ3MiLCJzZXRTaG93QmFja2dyb3VuZFNldHRpbmdzIiwiaXNGYXZvcml0ZSIsInNldElzRmF2b3JpdGUiLCJpc0Zhdm9yaXRlTG9hZGluZyIsInNldElzRmF2b3JpdGVMb2FkaW5nIiwic2hvd1Bob25lTW9kYWwiLCJzZXRTaG93UGhvbmVNb2RhbCIsImxpbmtDb3BpZWQiLCJzZXRMaW5rQ29waWVkIiwic2hvd0ltYWdlTW9kYWwiLCJzZXRTaG93SW1hZ2VNb2RhbCIsIm1vZGFsSW1hZ2VJbmRleCIsInNldE1vZGFsSW1hZ2VJbmRleCIsImxvYWRDaGFyYWN0ZXIiLCJsb2FkQXNzZXRzIiwiY2hlY2tGYXZvcml0ZVN0YXR1cyIsImhhbmRsZUtleURvd24iLCJlIiwia2V5IiwiaGFuZGxlTW9kYWxDbG9zZSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlTW9kYWxQcmV2aW91cyIsImhhbmRsZU1vZGFsTmV4dCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJjaGFyYWN0ZXJEYXRhIiwiZ2V0Q2hhcmFjdGVyQnlJZCIsImVycm9yIiwiY29uc29sZSIsImFzc2V0c0RhdGEiLCJnZXRDaGFyYWN0ZXJBc3NldHMiLCJiYWNrZ3JvdW5kQXNzZXRzIiwiZmlsdGVyIiwiYXNzZXQiLCJwdXJwb3NlIiwiaXNQdWJsaXNoZWQiLCJsZW5ndGgiLCJkZWZhdWx0QmFja2dyb3VuZCIsImZhdm9yaXRlc1Jlc3BvbnNlIiwiZ2V0RmF2b3JpdGVzIiwiaXNGYXZvcml0ZWQiLCJkYXRhIiwic29tZSIsImZhdiIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiRGF0ZSIsImFkZFN1ZmZpeCIsImxvY2FsZSIsImdldEJhY2tncm91bmRBc3NldHMiLCJoYW5kbGVCYWNrZ3JvdW5kQ2hhbmdlIiwiYmFja2dyb3VuZElkIiwiaGFuZGxlRmF2b3JpdGVUb2dnbGUiLCJyZW1vdmVGcm9tRmF2b3JpdGUiLCJzdWNjZXNzIiwiZGVzY3JpcHRpb24iLCJuYW1lIiwiZHVyYXRpb24iLCJhZGRUb0Zhdm9yaXRlIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlV2hhdHNBcHBDbGljayIsIndoYXRzYXBwVXJsIiwicGhvbmVOdW1iZXIiLCJ3aW5kb3ciLCJvcGVuIiwiaGFuZGxlUGhvbmVNb2RhbFN1Y2Nlc3MiLCJoYW5kbGVDb3B5TGluayIsImNoYXJhY3RlclVybCIsImxvY2F0aW9uIiwib3JpZ2luIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0Iiwic2V0VGltZW91dCIsImhhbmRsZVNoYXJlVG9XaGF0c0FwcCIsImVuY29kZVVSSUNvbXBvbmVudCIsImhhbmRsZUltYWdlQ2xpY2siLCJpbmRleCIsInByb2ZpbGVBc3NldHMiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJwIiwidHlwZSIsInVybCIsImltYWdlIiwiaDIiLCJ0aXRsZSIsInN0b3J5TW9kZSIsInN0YXR1cyIsImRpc2FibGVkIiwiaDMiLCJ0YWdzIiwibWFwIiwidGFnIiwib25Nb3VzZUVudGVyIiwidmlkZW8iLCJjdXJyZW50VGFyZ2V0IiwicXVlcnlTZWxlY3RvciIsInBsYXkiLCJvbk1vdXNlTGVhdmUiLCJwYXVzZSIsImN1cnJlbnRUaW1lIiwib25Ub3VjaFN0YXJ0Iiwib25Ub3VjaEVuZCIsInRlc3QiLCJtdXRlZCIsInBsYXlzSW5saW5lIiwicHJlbG9hZCIsInBvc3RlciIsIm9uTG9hZGVkRGF0YSIsImNhcHRpb24iLCJfIiwiYnV0dG9uIiwic3BhbiIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsImNvbnRyb2xzIiwiYXV0b1BsYXkiLCJvbkNsb3NlIiwib25TdWNjZXNzIiwiY2hhcmFjdGVyTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\n"));

/***/ })

});