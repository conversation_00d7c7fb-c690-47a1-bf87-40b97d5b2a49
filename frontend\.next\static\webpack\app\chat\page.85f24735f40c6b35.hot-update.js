"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./reset-chat-modal */ \"(app-pages-browser)/./src/components/chat/reset-chat-modal.tsx\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, profileImageUrl, characterName } = param;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c2 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { streamingMessage, profileImageUrl, characterName } = param;\n    _s1();\n    const [thinkingText, setThinkingText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('Berpikir');\n    // Cycle through thinking messages when not streaming actual content\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamingMessage.useEffect\": ()=>{\n            if (!streamingMessage) {\n                const messages = [\n                    'Berpikir',\n                    'Merenungkan',\n                    'Mempertimbangkan',\n                    'Memikirkan jawaban'\n                ];\n                let index = 0;\n                const interval = setInterval({\n                    \"StreamingMessage.useEffect.interval\": ()=>{\n                        index = (index + 1) % messages.length;\n                        setThinkingText(messages[index]);\n                    }\n                }[\"StreamingMessage.useEffect.interval\"], 2000);\n                return ({\n                    \"StreamingMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMessage.useEffect\"];\n            }\n        }\n    }[\"StreamingMessage.useEffect\"], [\n        streamingMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: thinkingText\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n}, \"W7FciA9Z3UpxXy1pJlVC9GvQ8tw=\"));\n_c3 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount, profileImageUrl } = param;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                src: profileImageUrl,\n                                alt: character.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Mode Cerita\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages, profileImageUrl } = param;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                        src: profileImageUrl,\n                        alt: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s2((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s2();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    // Detect mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInput.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInput.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInput.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInput.useEffect\"];\n        }\n    }[\"ChatInput.useEffect\"], []);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    const handleEmojiSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleEmojiSelect]\": (emoji)=>{\n            const newValue = newMessage + emoji;\n            setNewMessage(newValue);\n            messageRef.current = newValue;\n            // Focus back to input after emoji selection\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatInput.useCallback[handleEmojiSelect]\"], [\n        newMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 \".concat(isMobile ? 'p-3 pb-6' : 'p-4'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"\".concat(isMobile ? 'pr-20' : 'pr-24', \" rounded-2xl border-2 focus:border-bestieku-primary transition-colors \").concat(isMobile ? 'py-3 text-base' : 'py-3'),\n                                style: isMobile ? {\n                                    fontSize: '16px'\n                                } : {}\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_9__.EmojiPicker, {\n                                        onEmojiSelect: handleEmojiSelect,\n                                        disabled: disabled\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 \".concat(isMobile ? 'px-4 py-3' : 'px-6 py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 383,\n        columnNumber: 5\n    }, undefined);\n}, \"0TgdyP+W7Ya7Fi9I41+kS8az6FU=\"));\n_c6 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s3((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c7 = _s3((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5;\n    _s3();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileImageUrl, setProfileImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isImmersiveMode, setIsImmersiveMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show reset confirmation modal\n    const handleResetClick = ()=>{\n        setShowResetModal(true);\n    };\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Close modal\n            setShowResetModal(false);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                        // Get profile image from assets (prefer static image over video)\n                        const profileAssets = assets.filter({\n                            \"ChatInterface.useEffect.loadCharacterData.profileAssets\": (asset)=>asset.purpose === 'profile' && asset.type === 'image' && asset.isPublished\n                        }[\"ChatInterface.useEffect.loadCharacterData.profileAssets\"]);\n                        if (profileAssets.length > 0) {\n                            setProfileImageUrl(profileAssets[0].url);\n                        } else {\n                            // Fallback to character.image if no profile assets\n                            setProfileImageUrl(characterData.image);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Reset all streaming states when chat changes\n            setIsStreaming(false);\n            setStreamingMessage('');\n            setSending(false);\n            // Cleanup any existing connections and timeouts\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n                streamConnectionRef.current = null;\n            }\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n                streamingTimeoutRef.current = null;\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n                streamTimeoutRef.current = null;\n            }\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const currentChatId = chat.id; // Capture current chat ID\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Show AI thinking for 3-9 seconds before sending to backend\n                setIsStreaming(true);\n                setStreamingMessage('');\n                const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms\n                console.log(\"AI thinking for \".concat(thinkingDelay, \"ms before processing...\"));\n                await new Promise({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (resolve)=>setTimeout(resolve, thinkingDelay)\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Check if chat hasn't changed during the thinking delay\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed during thinking delay, aborting message send');\n                    return;\n                }\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Check again if chat hasn't changed after API call\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed after message send, aborting streaming');\n                    return;\n                }\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming(currentChatId);\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Only show error and remove message if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                    alert('Failed to send message. Please try again.');\n                }\n            } finally{\n                // Only reset sending state if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setSending(false);\n                }\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async (expectedChatId)=>{\n        const targetChatId = expectedChatId || chat.id;\n        console.log('Starting stream for chat:', targetChatId);\n        // Check if chat hasn't changed before starting stream\n        if (expectedChatId && chat.id !== expectedChatId) {\n            console.log('Chat changed before streaming started, aborting');\n            return;\n        }\n        // isStreaming already set to true in handleSendMessage\n        // setStreamingMessage(''); // Keep current thinking state\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            // Check if chat hasn't changed before updating\n            if (expectedChatId && chat.id !== expectedChatId) {\n                return;\n            }\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    // Check again if chat hasn't changed before the delayed update\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        setStreamingMessage(message);\n                        lastUpdateTime = Date.now();\n                    }\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            // Check if chat hasn't changed during streaming\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed during streaming, ignoring SSE event');\n                return;\n            }\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message only if chat hasn't changed\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        var _data_data;\n                        const finalMessage = {\n                            id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                            role: 'assistant',\n                            content: currentStreamingMessage,\n                            contentType: 'text',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                finalMessage\n                            ]);\n                        setStreamingMessage('');\n                        setIsStreaming(false);\n                    }\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                setStreamingMessage('');\n            }\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error, streaming was actually active, and chat hasn't changed\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming && (!expectedChatId || chat.id === expectedChatId)) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Check one more time if chat hasn't changed before creating connection\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed before creating stream connection, aborting');\n                return;\n            }\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(targetChatId, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active, connection exists, and chat hasn't changed\n                if (isStreaming && streamConnectionRef.current && (!expectedChatId || chat.id === expectedChatId)) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                alert('Failed to establish connection for AI response. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 900,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 909,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 899,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full \".concat(isMobile ? 'h-16 p-3' : 'h-20 p-4'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 944,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 929,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleResetClick,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 964,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 927,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    profileImageUrl: profileImageUrl,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                profileImageUrl: profileImageUrl,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1009,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1030,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 978,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1034,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__.ResetChatModal, {\n                isOpen: showResetModal,\n                onClose: ()=>setShowResetModal(false),\n                onConfirm: handleDeleteMessages,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || ((_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name),\n                messageCount: messages.length,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1042,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 925,\n        columnNumber: 5\n    }, undefined);\n}, \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\")), \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\");\n_c8 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"StreamingMessage\");\n$RefreshReg$(_c4, \"WelcomeMessage\");\n$RefreshReg$(_c5, \"OpeningScene\");\n$RefreshReg$(_c6, \"ChatInput\");\n$RefreshReg$(_c7, \"ChatInterface$memo\");\n$RefreshReg$(_c8, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});