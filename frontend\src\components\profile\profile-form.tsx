'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PhoneInput } from '@/components/ui/phone-input';
import { useAuth } from '@/contexts/auth-context';
import { UpdateProfileRequest } from '@/types/auth';
import { Camera, Loader2, Save, User } from 'lucide-react';
import { toast } from 'sonner';

interface ProfileFormProps {
  onSuccess?: () => void;
}

export function ProfileForm({ onSuccess }: ProfileFormProps) {
  const { user, updateProfile, uploadProfileImage } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  
  const [formData, setFormData] = useState<UpdateProfileRequest>({
    name: user?.name || '',
    phoneNumber: user?.phoneNumber || '',
    dateOfBirth: user?.dateOfBirth ? user.dateOfBirth.split('T')[0] : '',
    gender: user?.gender || '',
    about: user?.about || '',
  });

  const handleInputChange = (field: keyof UpdateProfileRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("File tidak valid", {
        description: "Silakan pilih file gambar yang valid (JPG, PNG, dll).",
        duration: 4000,
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File terlalu besar", {
        description: "Ukuran gambar harus kurang dari 5MB.",
        duration: 4000,
      });
      return;
    }

    setIsUploadingImage(true);

    try {
      await uploadProfileImage(file);
      toast.success("Foto profil berhasil diperbarui!", {
        description: "Foto profil Anda telah diperbarui dengan sukses.",
        duration: 4000,
      });
    } catch (err) {
      toast.error("Gagal mengunggah foto", {
        description: err instanceof Error ? err.message : "Terjadi kesalahan saat mengunggah gambar.",
        duration: 4000,
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await updateProfile(formData);
      toast.success("Profil berhasil diperbarui!", {
        description: "Informasi profil Anda telah disimpan dengan sukses.",
        duration: 4000,
      });
      onSuccess?.();
    } catch (err) {
      toast.error("Gagal memperbarui profil", {
        description: err instanceof Error ? err.message : "Terjadi kesalahan saat menyimpan profil.",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="space-y-8">
        {/* Profile Image Section */}
        <div className="text-center">
          <div className="relative inline-block">
            <Avatar className="w-32 h-32 mx-auto ring-4 ring-bestieku-primary/20">
              <AvatarImage src={user?.image} alt={user?.name} />
              <AvatarFallback className="bg-gradient-bestieku text-3xl font-semibold">
                {user?.name?.charAt(0)?.toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            
            <Button
              type="button"
              size="icon"
              variant="outline"
              className="absolute bottom-2 right-2 rounded-full bg-background shadow-lg hover:bg-bestieku-primary border-2"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploadingImage}
            >
              {isUploadingImage ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Camera className="w-4 h-4" />
              )}
            </Button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
          
          <p className="text-sm text-muted-foreground mt-3">
            Klik ikon kamera untuk mengubah foto profil Anda
          </p>
        </div>

        {/* Profile Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-6">
            {/* Name Field */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-2">
                Nama Lengkap *
              </label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Masukkan nama lengkap Anda"
                required
                minLength={3}
                className="h-12"
              />
            </div>

            {/* Phone Number Field */}
            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium mb-2">
                Nomor Telepon
              </label>
              <PhoneInput
                value={formData.phoneNumber || ''}
                onChange={(value) => handleInputChange('phoneNumber', value)}
                placeholder="Masukkan nomor telepon"
                disabled={isLoading}
                className="h-12"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Pilih kode negara dan masukkan nomor telepon tanpa kode negara
              </p>
            </div>

            {/* Date of Birth Field */}
            <div>
              <label htmlFor="dateOfBirth" className="block text-sm font-medium mb-2">
                Tanggal Lahir
              </label>
              <Input
                id="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                className="h-12"
              />
            </div>

            {/* Gender Field */}
            <div>
              <label htmlFor="gender" className="block text-sm font-medium mb-2">
                Jenis Kelamin
              </label>
              <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                <SelectTrigger className="h-12 focus:ring-bestieku-primary focus:border-bestieku-primary">
                  <SelectValue placeholder="Pilih jenis kelamin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male" className="hover:bg-bestieku-primary/10 hover:text-bestieku-primary focus:bg-bestieku-primary/10 focus:text-bestieku-primary">
                    Laki-laki
                  </SelectItem>
                  <SelectItem value="female" className="hover:bg-bestieku-primary/10 hover:text-bestieku-primary focus:bg-bestieku-primary/10 focus:text-bestieku-primary">
                    Perempuan
                  </SelectItem>
                  <SelectItem value="other" className="hover:bg-bestieku-primary/10 hover:text-bestieku-primary focus:bg-bestieku-primary/10 focus:text-bestieku-primary">
                    Lainnya
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* About Field */}
            <div>
              <label htmlFor="about" className="block text-sm font-medium mb-2">
                Tentang
              </label>
              <textarea
                id="about"
                value={formData.about}
                onChange={(e) => handleInputChange('about', e.target.value)}
                placeholder="Ceritakan tentang diri Anda"
                className="flex min-h-[120px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none"
                rows={4}
              />
            </div>
          </div>



          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full h-12 bg-gradient-bestieku hover:bg-gradient-bestieku-reverse rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Memperbarui Profil...
              </>
            ) : (
              <>
                <Save className="mr-2 h-5 w-5" />
                Simpan Perubahan
              </>
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
