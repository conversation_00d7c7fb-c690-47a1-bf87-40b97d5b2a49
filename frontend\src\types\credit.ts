export interface CreditBalance {
  userId: string;
  balance: number;
  updatedAt: string;
}

export interface CreditPackage {
  quantity: number;
  price: number;
}

export interface BuyCreditRequest {
  quantity: number;
}

export interface OrderItem {
  id: string;
  name: string;
  description: string;
  price: number;
  quantity: number;
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  total: number;
  description: string;
  status: string;
  invoiceId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerDetails {
  name: string;
  email: string;
  phone: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  price: number;
}

export interface InvoiceTotal {
  type: string;
  description: string;
  amount: number;
}

export interface Invoice {
  id: string;
  userId: string;
  customerDetails: CustomerDetails;
  orderId: string;
  items: InvoiceItem[];
  totals: InvoiceTotal[];
  paidDate: string | null;
  metadata: Record<string, any>;
  status: string;
  invoiceCode: string;
  invoiceLink: string;
  paymentLink: string;
  paymentToken: string;
  createdAt: string;
  updatedAt: string;
}

export interface BuyCreditResponse {
  order: Order;
  invoice: Invoice;
}
