"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/immersive-mode.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/immersive-mode.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImmersiveMode: () => (/* binding */ ImmersiveMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ImmersiveMode auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImmersiveMode(param) {\n    let { isOpen, onClose, character, messages, streamingMessage, isStreaming, onSendMessage, disabled, characterAssets, profileImageUrl } = param;\n    var _backgroundAssets_;\n    _s();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticles, setShowParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [characterMood, setCharacterMood] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('neutral');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Get profile assets for character display\n    const profileAssets = characterAssets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    // Get background assets for ambient backgrounds\n    const backgroundAssets = characterAssets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    // Cycle through character images every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (profileAssets.length <= 1) return;\n            const interval = setInterval({\n                \"ImmersiveMode.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"ImmersiveMode.useEffect.interval\": (prev)=>(prev + 1) % profileAssets.length\n                    }[\"ImmersiveMode.useEffect.interval\"]);\n                }\n            }[\"ImmersiveMode.useEffect.interval\"], 10000);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>clearInterval(interval)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        profileAssets.length\n    ]);\n    // Focus input when opening\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isOpen) {\n                setTimeout({\n                    \"ImmersiveMode.useEffect\": ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                    }\n                }[\"ImmersiveMode.useEffect\"], 100);\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen\n    ]);\n    // Update character mood based on chat state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isStreaming) {\n                setCharacterMood('thinking');\n            } else if (messages.length > 0) {\n                const lastMessage = messages[messages.length - 1];\n                if (lastMessage.role === 'user') {\n                    setCharacterMood('happy');\n                } else {\n                    setCharacterMood('neutral');\n                }\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isStreaming,\n        messages\n    ]);\n    // Handle keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (!isOpen) return;\n            const handleKeyDown = {\n                \"ImmersiveMode.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape') {\n                        onClose();\n                    } else if (e.key === 'F11') {\n                        e.preventDefault();\n                        // Toggle fullscreen if supported\n                        if (document.fullscreenElement) {\n                            document.exitFullscreen();\n                        } else {\n                            document.documentElement.requestFullscreen();\n                        }\n                    } else if (e.ctrlKey && e.key === 'p') {\n                        e.preventDefault();\n                        setShowParticles(!showParticles);\n                    } else if (e.ctrlKey && e.key === 'm') {\n                        e.preventDefault();\n                        setIsAudioEnabled(!isAudioEnabled);\n                    }\n                }\n            }[\"ImmersiveMode.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen,\n        onClose,\n        showParticles,\n        isAudioEnabled\n    ]);\n    const handleSendMessage = ()=>{\n        const message = newMessage.trim();\n        if (!message || disabled) return;\n        onSendMessage(message);\n        setNewMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleEmojiSelect = (emoji)=>{\n        var _inputRef_current;\n        setNewMessage((prev)=>prev + emoji);\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    if (!isOpen) return null;\n    const currentProfileAsset = profileAssets[currentImageIndex];\n    const currentProfileImage = (currentProfileAsset === null || currentProfileAsset === void 0 ? void 0 : currentProfileAsset.url) || profileImageUrl || (character === null || character === void 0 ? void 0 : character.image);\n    const currentBackground = (_backgroundAssets_ = backgroundAssets[0]) === null || _backgroundAssets_ === void 0 ? void 0 : _backgroundAssets_.url;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20\",\n                style: {\n                    backgroundImage: currentBackground ? \"linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)), url(\".concat(currentBackground, \")\") : undefined,\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            showParticles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: characterMood === 'thinking' ? 30 : 20\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute rounded-full animate-pulse \".concat(characterMood === 'thinking' ? 'w-1 h-1 bg-blue-400/30' : characterMood === 'happy' ? 'w-2 h-2 bg-yellow-400/20' : 'w-1 h-1 bg-white/20'),\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                                animationDuration: \"\".concat(characterMood === 'thinking' ? 1 + Math.random() * 2 : 2 + Math.random() * 3, \"s\")\n                            }\n                        }, i, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)),\n                    characterMood === 'thinking' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                0,\n                                1,\n                                2\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-400/50 rounded-full animate-bounce\",\n                                    style: {\n                                        animationDelay: \"\".concat(i * 0.2, \"s\"),\n                                        animationDuration: '1s'\n                                    }\n                                }, i, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 right-4 flex justify-between items-center z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-white text-xl font-bold\",\n                                children: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 text-sm\",\n                                children: \"Theater Mode\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                className: \"text-white hover:bg-white/10\",\n                                title: isAudioEnabled ? \"Disable Audio\" : \"Enable Audio\",\n                                children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 65\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setShowParticles(!showParticles),\n                                className: \"text-white hover:bg-white/10\",\n                                title: showParticles ? \"Hide Effects\" : \"Show Effects\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"text-white hover:bg-white/10\",\n                                title: \"Exit Immersive Mode\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full pt-16 pb-4 flex-col md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center justify-center p-4 md:p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-80 h-80 rounded-full overflow-hidden border-4 shadow-2xl transition-all duration-1000 \".concat(characterMood === 'thinking' ? 'border-blue-400/50 shadow-blue-400/20' : characterMood === 'happy' ? 'border-yellow-400/50 shadow-yellow-400/20' : 'border-white/20 shadow-white/10'),\n                                    children: [\n                                        currentProfileImage && /\\.mp4(\\?|$)/i.test(currentProfileImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: currentProfileImage,\n                                            className: \"w-full h-full object-cover\",\n                                            autoPlay: true,\n                                            muted: true,\n                                            loop: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentProfileImage,\n                                            alt: character === null || character === void 0 ? void 0 : character.name,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-transparent via-transparent transition-all duration-1000 \".concat(characterMood === 'thinking' ? 'to-blue-400/10 animate-pulse' : characterMood === 'happy' ? 'to-yellow-400/10 animate-bounce' : 'to-white/5 animate-pulse')\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        (characterMood === 'thinking' || characterMood === 'happy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 rounded-full \".concat(characterMood === 'thinking' ? 'shadow-[0_0_50px_rgba(59,130,246,0.3)]' : 'shadow-[0_0_50px_rgba(251,191,36,0.3)]', \" animate-pulse\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm transition-all duration-500 \".concat(isStreaming ? 'bg-blue-500/60 shadow-lg shadow-blue-500/20' : 'bg-black/50'),\n                                        children: isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        0,\n                                                        1,\n                                                        2\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 bg-white rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"\".concat(i * 0.2, \"s\"),\n                                                                animationDuration: '1s'\n                                                            }\n                                                        }, i, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1\",\n                                                    children: \"Thinking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ready to chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 bg-black/40 backdrop-blur-md border-l border-white/10 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    messages.slice(-10).map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[80%] rounded-2xl px-4 py-3 \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white' : 'bg-white/10 backdrop-blur-sm text-white border border-white/20'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: message.content\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, message.id, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this)),\n                                    isStreaming && streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[80%] rounded-2xl px-4 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: streamingMessage\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block w-2 h-4 bg-white/60 animate-pulse ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    ref: inputRef,\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Message \".concat((character === null || character === void 0 ? void 0 : character.name) || 'character', \"...\"),\n                                                    disabled: disabled,\n                                                    className: \"bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-white/60 rounded-2xl pr-12\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_4__.EmojiPicker, {\n                                                        onEmojiSelect: handleEmojiSelect,\n                                                        disabled: disabled\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleSendMessage,\n                                            disabled: !newMessage.trim() || disabled,\n                                            className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(ImmersiveMode, \"NmcayRwAXc8xtHdXruQ832Az/7k=\");\n_c = ImmersiveMode;\nvar _c;\n$RefreshReg$(_c, \"ImmersiveMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/immersive-mode.tsx\n"));

/***/ })

});