'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Character } from '@/types/character';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Users } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

// Helper function to detect mobile device
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
};

interface CharacterCardProps {
  character: Character;
  onStartChat?: (characterId: string) => void;
}

export function CharacterCard({ character, onStartChat }: CharacterCardProps) {
  const { isAuthenticated } = useAuth();
  const cardRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  const handleCardClick = () => {
    if (isAuthenticated && onStartChat) {
      onStartChat(character.id);
    }
  };

  // Detect mobile device on mount
  useEffect(() => {
    setIsMobile(isMobileDevice());
  }, []);

  // Intersection Observer untuk auto-play video saat card terlihat (HANYA MOBILE)
  useEffect(() => {
    const card = cardRef.current;
    const video = videoRef.current;

    if (!card || !video || !isMobile) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Card terlihat di viewport, play video
            video.play().catch(() => {
              // Ignore autoplay errors
            });
          } else {
            // Card tidak terlihat, pause video dan reset ke frame pertama
            video.pause();
            video.currentTime = 0;
          }
        });
      },
      {
        threshold: 0.5, // Play saat 50% card terlihat
        rootMargin: '0px 0px -10% 0px' // Sedikit margin untuk performa
      }
    );

    observer.observe(card);

    return () => {
      observer.disconnect();
    };
  }, [isMobile]);

  // Handle hover events untuk desktop
  const handleMouseEnter = () => {
    if (!isMobile && videoRef.current) {
      videoRef.current.play().catch(() => {
        // Ignore autoplay errors
      });
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile && videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
  };



  return (
    <div
      ref={cardRef}
      className={`bg-card border rounded-xl p-4 transition-all duration-200 group ${
        isAuthenticated
          ? 'hover:shadow-lg hover:scale-[1.02] cursor-pointer hover:border-bestieku-primary/50'
          : 'hover:shadow-md cursor-default opacity-90'
      }`}
      onClick={handleCardClick}
    >
      {/* Character Image */}
      <div className="relative mb-3">
        <div
          className="bg-muted/50 aspect-square rounded-lg overflow-hidden"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {character.image ? (
            /\.mp4(\?|$)/i.test(character.image) ? (
              <video
                ref={videoRef}
                src={character.image}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 pointer-events-none"
                muted
                playsInline
                preload="metadata"
                poster={character.image + "#t=0.1"}
                onLoadedData={(e) => {
                  // Ensure first frame is visible
                  e.currentTarget.currentTime = 0;
                }}
              />
            ) : (
              <img
                src={character.image}
                alt={character.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              />
            )
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <MessageCircle className="w-12 h-12" />
            </div>
          )}
        </div>
        
        {/* Story Mode Badge */}
        {character.storyMode && (
          <Badge
            variant="secondary"
            className="absolute top-2 right-2 bg-white text-black border border-gray-200 hover:bg-white hover:text-black pointer-events-none"
          >
            Mode Cerita
          </Badge>
        )}

        {/* Popularity Badge */}
        {character.messageCount > 0 && (
          <Badge
            variant="secondary"
            className="absolute top-2 left-2 bg-black/70 text-white text-xs flex items-center gap-1"
          >
            <Users className="w-3 h-3" />
            {character.messageCount > 999 ? '999+' : character.messageCount}
          </Badge>
        )}
      </div>

      {/* Character Info */}
      <div className="space-y-2">
        <h3 className="font-semibold text-lg leading-tight">{character.name}</h3>
        
        <p className="text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem]">
          {character.description}
        </p>

        {/* Tags */}
        {character.tags && character.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {character.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {character.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{character.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Status Indicator */}
        <div className="pt-2 flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <MessageCircle className="w-3 h-3" />
            <span>{isAuthenticated ? 'Klik untuk memulai percakapan' : 'Login/Signup untuk memulai percakapan'}</span>
          </div>
          {isAuthenticated && (
            <div className="flex items-center gap-1 text-bestieku-primary opacity-0 group-hover:opacity-100 transition-opacity">
              <span className="text-xs font-medium">Mulai →</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
