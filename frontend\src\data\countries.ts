export interface Country {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
}

export const countries: Country[] = [
  {
    code: 'ID',
    name: 'Indonesia',
    dialCode: '62',
    flag: '🇮🇩'
  },
  {
    code: 'MY',
    name: 'Malaysia',
    dialCode: '60',
    flag: '🇲🇾'
  },
  {
    code: 'SG',
    name: 'Singapore',
    dialCode: '65',
    flag: '🇸🇬'
  },
  {
    code: 'TH',
    name: 'Thailand',
    dialCode: '66',
    flag: '🇹🇭'
  },
  {
    code: 'PH',
    name: 'Philippines',
    dialCode: '63',
    flag: '🇵🇭'
  },
  {
    code: 'VN',
    name: 'Vietnam',
    dialCode: '84',
    flag: '🇻🇳'
  },
  {
    code: 'US',
    name: 'United States',
    dialCode: '1',
    flag: '🇺🇸'
  },
  {
    code: 'GB',
    name: 'United Kingdom',
    dialCode: '44',
    flag: '🇬🇧'
  },
  {
    code: 'AU',
    name: 'Australia',
    dialCode: '61',
    flag: '🇦🇺'
  },
  {
    code: 'JP',
    name: 'Japan',
    dialCode: '81',
    flag: '🇯🇵'
  },
  {
    code: 'KR',
    name: 'South Korea',
    dialCode: '82',
    flag: '🇰🇷'
  },
  {
    code: 'CN',
    name: 'China',
    dialCode: '86',
    flag: '🇨🇳'
  },
  {
    code: 'IN',
    name: 'India',
    dialCode: '91',
    flag: '🇮🇳'
  }
];

// Helper functions
export const getCountryByDialCode = (dialCode: string): Country | undefined => {
  return countries.find(country => country.dialCode === dialCode);
};

export const getCountryByCode = (code: string): Country | undefined => {
  return countries.find(country => country.code === code);
};

export const parsePhoneNumber = (phoneNumber: string): { countryCode: string; localNumber: string } | null => {
  if (!phoneNumber) return null;
  
  // Try to match with known country codes
  for (const country of countries) {
    if (phoneNumber.startsWith(country.dialCode)) {
      return {
        countryCode: country.dialCode,
        localNumber: phoneNumber.substring(country.dialCode.length)
      };
    }
  }
  
  // Default to Indonesia if starts with 0
  if (phoneNumber.startsWith('0')) {
    return {
      countryCode: '62',
      localNumber: phoneNumber.substring(1)
    };
  }
  
  return null;
};

export const formatPhoneNumber = (countryCode: string, localNumber: string): string => {
  // Remove any leading zeros from local number
  const cleanLocalNumber = localNumber.replace(/^0+/, '');
  return `${countryCode}${cleanLocalNumber}`;
};
