export interface FavoriteCharacter {
  id: string;
  name: string;
  title: string | null;
  description: string;
  storyMode: boolean;
  image: string;
  backgroundImage: string;
  openingScene: string | null;
  tags: string[];
  messageCount: number;
  whatsappUrl: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface FavoritesResponse {
  data: FavoriteCharacter[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface FavoriteActionResponse {
  success: boolean;
  message: string;
}

export interface GetFavoritesParams {
  page?: number;
  limit?: number;
}
