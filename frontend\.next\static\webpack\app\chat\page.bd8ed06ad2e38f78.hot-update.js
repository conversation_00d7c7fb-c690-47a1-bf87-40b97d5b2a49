"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx":
/*!***********************************************************!*\
  !*** ./src/components/chat/character-profile-sidebar.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterProfileSidebar: () => (/* binding */ CharacterProfileSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _services_favorite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/favorite */ \"(app-pages-browser)/./src/services/favorite.ts\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _phone_number_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./phone-number-modal */ \"(app-pages-browser)/./src/components/chat/phone-number-modal.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* __next_internal_client_entry_do_not_use__ CharacterProfileSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-2xl flex size-full items-center justify-center rounded-full bg-muted \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nfunction CharacterProfileSidebar(param) {\n    let { characterId, messageCount, isOpen, onToggle, onBackgroundChange } = param;\n    var _getProfileAssets_currentProfileImageIndex, _getProfileAssets_modalImageIndex;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [assetsLoading, setAssetsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentProfileImageIndex, setCurrentProfileImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedBackground, setSelectedBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBackgroundSettings, setShowBackgroundSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavoriteLoading, setIsFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPhoneModal, setShowPhoneModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkCopied, setLinkCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalImageIndex, setModalImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            loadCharacter();\n            loadAssets();\n            checkFavoriteStatus();\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        characterId\n    ]);\n    // Keyboard navigation for image modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            if (!showImageModal) return;\n            const handleKeyDown = {\n                \"CharacterProfileSidebar.useEffect.handleKeyDown\": (e)=>{\n                    switch(e.key){\n                        case 'Escape':\n                            handleModalClose();\n                            break;\n                        case 'ArrowLeft':\n                            e.preventDefault();\n                            handleModalPrevious();\n                            break;\n                        case 'ArrowRight':\n                            e.preventDefault();\n                            handleModalNext();\n                            break;\n                    }\n                }\n            }[\"CharacterProfileSidebar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"CharacterProfileSidebar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"CharacterProfileSidebar.useEffect\"];\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        showImageModal,\n        modalImageIndex\n    ]);\n    const loadCharacter = async ()=>{\n        try {\n            setLoading(true);\n            const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterById(characterId);\n            setCharacter(characterData);\n        } catch (error) {\n            console.error('Failed to load character:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAssets = async ()=>{\n        try {\n            setAssetsLoading(true);\n            const assetsData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterAssets(characterId);\n            setAssets(assetsData);\n            // Auto-select first background if character has backgrounds and no background is currently selected\n            const backgroundAssets = assetsData.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n            if (backgroundAssets.length > 0 && selectedBackground === null) {\n                const defaultBackground = backgroundAssets[0].id;\n                setSelectedBackground(defaultBackground);\n                onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(defaultBackground);\n            }\n        } catch (error) {\n            console.error('Failed to load character assets:', error);\n        } finally{\n            setAssetsLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            const favoritesResponse = await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.getFavorites();\n            const isFavorited = favoritesResponse.data.some((fav)=>fav.id === characterId);\n            setIsFavorite(isFavorited);\n        } catch (error) {\n            console.error('Failed to check favorite status:', error);\n        // Don't show error toast for this, just keep default state\n        }\n    };\n    const formatDate = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true,\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.id\n            });\n        } catch (e) {\n            return 'Unknown';\n        }\n    };\n    // Helper functions for assets\n    const getProfileAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    };\n    const getBackgroundAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    };\n    const handleBackgroundChange = (backgroundId)=>{\n        setSelectedBackground(backgroundId);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(backgroundId);\n    };\n    const handleFavoriteToggle = async ()=>{\n        if (!character) return;\n        setIsFavoriteLoading(true);\n        try {\n            if (isFavorite) {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.removeFromFavorite(character.id);\n                setIsFavorite(false);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Dihapus dari favorit\", {\n                    description: \"\".concat(character.name, \" telah dihapus dari daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            } else {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.addToFavorite(character.id);\n                setIsFavorite(true);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Ditambahkan ke favorit\", {\n                    description: \"\".concat(character.name, \" telah ditambahkan ke daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal mengubah favorit\", {\n                description: error instanceof Error ? error.message : \"Terjadi kesalahan saat mengubah status favorit.\",\n                duration: 4000\n            });\n        } finally{\n            setIsFavoriteLoading(false);\n        }\n    };\n    const handleWhatsAppClick = ()=>{\n        if (!(character === null || character === void 0 ? void 0 : character.whatsappUrl)) return;\n        // Check if user has phone number\n        if (!(user === null || user === void 0 ? void 0 : user.phoneNumber)) {\n            setShowPhoneModal(true);\n            return;\n        }\n        // If user has phone number, proceed to WhatsApp\n        window.open(character.whatsappUrl, '_blank');\n    };\n    const handlePhoneModalSuccess = ()=>{\n        if (character === null || character === void 0 ? void 0 : character.whatsappUrl) {\n            window.open(character.whatsappUrl, '_blank');\n        }\n    };\n    const handleCopyLink = async ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        try {\n            await navigator.clipboard.writeText(characterUrl);\n            setLinkCopied(true);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Link disalin!\", {\n                description: \"Link karakter telah disalin ke clipboard. Bagikan ke teman-temanmu!\",\n                duration: 3000\n            });\n            // Reset copied state after 3 seconds\n            setTimeout(()=>setLinkCopied(false), 3000);\n        } catch (error) {\n            console.error('Failed to copy link:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal menyalin link\", {\n                description: \"Terjadi kesalahan saat menyalin link karakter.\",\n                duration: 3000\n            });\n        }\n    };\n    const handleShareToWhatsApp = ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        const message = \"Halo! Ayo chat dengan \".concat(character.name, \" di Bestieku! \\uD83E\\uDD16\\n\\n\").concat(character.description, \"\\n\\nKlik link ini: \").concat(characterUrl);\n        const whatsappUrl = \"https://wa.me/?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleImageClick = (index)=>{\n        setModalImageIndex(index);\n        setShowImageModal(true);\n        setImageLoading(true);\n    };\n    const handleModalClose = ()=>{\n        setShowImageModal(false);\n    };\n    const handleModalPrevious = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : profileAssets.length - 1);\n        setImageLoading(true);\n    };\n    const handleModalNext = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex < profileAssets.length - 1 ? modalImageIndex + 1 : 0);\n        setImageLoading(true);\n    };\n    // Touch handlers for swipe navigation\n    const handleTouchStart = (e)=>{\n        setTouchEnd(null);\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && getProfileAssets().length > 1) {\n            handleModalNext();\n        }\n        if (isRightSwipe && getProfileAssets().length > 1) {\n            handleModalPrevious();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-32 bg-muted rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    if (!character) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Karakter tidak ditemukan\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    onClick: onToggle,\n                    className: \"w-full\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 60\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 relative flex shrink-0 overflow-hidden rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                        src: (()=>{\n                                            // Get first profile image asset (not video)\n                                            const profileAssets = getProfileAssets().filter((asset)=>asset.type === 'image');\n                                            return profileAssets.length > 0 ? profileAssets[0].url : character.image;\n                                        })(),\n                                        alt: character.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-1\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this),\n                                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mb-3 font-medium\",\n                                    children: character.title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2 mb-3\",\n                                    children: [\n                                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-bestieku-primary text-white\",\n                                            children: \"Mode Cerita\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: character.status\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this),\n                                character.whatsappUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        onClick: handleWhatsAppClick,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chat di WhatsApp\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: isFavorite ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleFavoriteToggle,\n                                        disabled: isFavoriteLoading,\n                                        className: \"flex items-center gap-2 \".concat(isFavorite ? 'bg-red-500 hover:bg-red-600 text-white' : 'hover:bg-red-50 hover:text-red-600 hover:border-red-300'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isFavorite ? 'fill-current' : '')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            isFavoriteLoading ? 'Loading...' : isFavorite ? 'Favorit' : 'Tambah ke Favorit'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleCopyLink,\n                                            className: \"flex items-center gap-2 flex-1\",\n                                            children: linkCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Link Disalin!\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Salin Link\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShareToWhatsApp,\n                                            className: \"flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"WhatsApp\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Tentang\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                    children: character.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 13\n                        }, this),\n                        character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Tag\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: character.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Galeri Profil\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            onTouchStart: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onTouchEnd: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            children: [\n                                                (()=>{\n                                                    const asset = getProfileAssets()[currentProfileImageIndex];\n                                                    const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                                    return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        src: url,\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        muted: true,\n                                                        playsInline: true,\n                                                        preload: \"metadata\",\n                                                        poster: url + \"#t=0.1\",\n                                                        onLoadedData: (e)=>{\n                                                            e.currentTarget.currentTime = 0;\n                                                        },\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: url,\n                                                        alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })(),\n                                                getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-x-0 bottom-2 flex justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex > 0 ? currentProfileImageIndex - 1 : getProfileAssets().length - 1),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex < getProfileAssets().length - 1 ? currentProfileImageIndex + 1 : 0),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this),\n                                        ((_getProfileAssets_currentProfileImageIndex = getProfileAssets()[currentProfileImageIndex]) === null || _getProfileAssets_currentProfileImageIndex === void 0 ? void 0 : _getProfileAssets_currentProfileImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground text-center\",\n                                            children: getProfileAssets()[currentProfileImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 21\n                                        }, this),\n                                        getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-1\",\n                                            children: getProfileAssets().map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentProfileImageIndex ? 'bg-bestieku-primary' : 'bg-muted'),\n                                                    onClick: ()=>setCurrentProfileImageIndex(index)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 65\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Statistik Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Pesan Anda\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Pesan\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-bestieku-primary\",\n                                                    children: character.messageCount > 999 ? '999+' : character.messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Jenis Karakter\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: character.storyMode ? 'Story' : 'Chat'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Info Karakter\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Dibuat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Diperbarui\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Latar Belakang Chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: ()=>setShowBackgroundSettings(!showBackgroundSettings),\n                                            className: \"w-6 h-6\",\n                                            children: showBackgroundSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 47\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 83\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 17\n                                }, this),\n                                showBackgroundSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === null ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                            onClick: ()=>handleBackgroundChange(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-8 bg-gradient-to-b from-background to-muted/20 rounded border\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Latar belakang polos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 21\n                                        }, this),\n                                        getBackgroundAssets().map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === asset.id ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                                onClick: ()=>handleBackgroundChange(asset.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /\\.mp4(\\?|$)/i.test(asset.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: asset.url,\n                                                            className: \"w-12 h-8 object-cover rounded border pointer-events-none\",\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            preload: \"metadata\",\n                                                            onMouseEnter: (e)=>e.currentTarget.play(),\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.pause();\n                                                                e.currentTarget.currentTime = 0;\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: asset.url,\n                                                            alt: asset.caption || 'Background',\n                                                            className: \"w-12 h-8 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: asset.caption || 'Latar Belakang Kustom'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Latar belakang karakter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, asset.id, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 23\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 68\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this),\n            showImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl max-h-[90vh] w-full mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModalClose,\n                                className: \"absolute top-4 right-4 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalPrevious,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalNext,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-black rounded-lg overflow-hidden\",\n                                onTouchStart: handleTouchStart,\n                                onTouchMove: handleTouchMove,\n                                onTouchEnd: handleTouchEnd,\n                                children: [\n                                    (()=>{\n                                        const asset = getProfileAssets()[modalImageIndex];\n                                        const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                        return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: url,\n                                            className: \"w-full max-h-[80vh] object-contain\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            muted: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: url,\n                                            alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                            className: \"w-full max-h-[80vh] object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 19\n                                        }, this);\n                                    })(),\n                                    ((_getProfileAssets_modalImageIndex = getProfileAssets()[modalImageIndex]) === null || _getProfileAssets_modalImageIndex === void 0 ? void 0 : _getProfileAssets_modalImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-center\",\n                                            children: getProfileAssets()[modalImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm\",\n                                children: [\n                                    modalImageIndex + 1,\n                                    \" / \",\n                                    getProfileAssets().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        onClick: handleModalClose\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 786,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phone_number_modal__WEBPACK_IMPORTED_MODULE_8__.PhoneNumberModal, {\n                isOpen: showPhoneModal,\n                onClose: ()=>setShowPhoneModal(false),\n                onSuccess: handlePhoneModalSuccess,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 869,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterProfileSidebar, \"3mCKT5IcF9Xt3P2BS7RMtQHfEtw=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c1 = CharacterProfileSidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"CharacterProfileSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\n"));

/***/ })

});