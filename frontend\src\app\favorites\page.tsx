'use client';

import React, { useState, useEffect } from 'react';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/app-sidebar';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { AuthModal } from '@/components/auth/auth-modal';
import { Heart, MessageCircle, User, Loader2 } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { favoriteService } from '@/services/favorite';
import { FavoriteCharacter } from '@/types/favorite';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { characterService } from '@/services/character';

export default function FavoritesPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [favorites, setFavorites] = useState<FavoriteCharacter[]>([]);
  const [favoritesLoading, setFavoritesLoading] = useState(true);
  const [removingFavorite, setRemovingFavorite] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated) {
      loadFavorites();
    }
  }, [isAuthenticated]);

  const loadFavorites = async () => {
    try {
      setFavoritesLoading(true);
      const response = await favoriteService.getFavorites();
      setFavorites(response.data);
    } catch (error) {
      toast.error("Gagal memuat favorit", {
        description: "Terjadi kesalahan saat memuat daftar favorit Anda.",
        duration: 4000,
      });
    } finally {
      setFavoritesLoading(false);
    }
  };

  const handleRemoveFavorite = async (characterId: string, characterName: string) => {
    try {
      setRemovingFavorite(characterId);
      await favoriteService.removeFromFavorite(characterId);
      
      // Remove from local state
      setFavorites(prev => prev.filter(fav => fav.id !== characterId));
      
      toast.success("Dihapus dari favorit", {
        description: `${characterName} telah dihapus dari daftar favorit Anda.`,
        duration: 3000,
      });
    } catch (error) {
      toast.error("Gagal menghapus favorit", {
        description: "Terjadi kesalahan saat menghapus dari favorit.",
        duration: 4000,
      });
    } finally {
      setRemovingFavorite(null);
    }
  };

  const handleChatWithCharacter = async (characterId: string) => {
    if (!isAuthenticated) {
      toast.error("Silakan masuk terlebih dahulu", {
        description: "Anda perlu masuk untuk memulai chat.",
        duration: 4000,
      });
      return;
    }

    try {
      const chatSession = await characterService.initiateChat(characterId);
      console.log('Chat initiated:', chatSession);
      // Navigate to chat page with the new chat
      router.push(`/chat?id=${chatSession.id}`);
    } catch (error) {
      console.error('Failed to initiate chat:', error);
      toast.error("Gagal memulai chat", {
        description: "Terjadi kesalahan saat memulai chat dengan character.",
        duration: 4000,
      });
    }
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading...</p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!isAuthenticated) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/dashboard">
                      Bestieku
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Favorit</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>

          <div className="flex flex-1 flex-col gap-6 p-4 pt-0">
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center max-w-md mx-auto p-8">
                <MessageCircle className="w-16 h-16 mx-auto mb-6 text-muted-foreground/50" />
                <h2 className="text-xl font-semibold mb-2">Selamat Datang di Halaman Favorit Akun Anda</h2>
                <p className="text-muted-foreground mb-6">
                  Untuk melihat daftar character favorit Anda, silakan masuk atau daftar terlebih dahulu.
                </p>
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="inline-flex items-center px-6 py-3 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-lg transition-colors font-medium"
                >
                  Masuk / Daftar
                </button>
              </div>
            </div>
          </div>

          <AuthModal 
            isOpen={isAuthModalOpen} 
            onClose={() => setIsAuthModalOpen(false)} 
          />
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Favorit</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-6 p-4 pt-0">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold mb-2">Favorit</h1>
            <p className="text-muted-foreground">
              Daftar character yang telah Anda tambahkan ke favorit
            </p>
          </div>

          {/* Content */}
          <div>

            {favoritesLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <Card key={i} className="animate-pulse flex flex-col h-full">
                    <CardContent className="p-6 flex flex-col h-full">
                      {/* Header skeleton */}
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-16 h-16 bg-muted rounded-full flex-shrink-0" />
                        <div className="flex-1">
                          <div className="h-5 bg-muted rounded w-3/4 mb-2" />
                          <div className="h-5"> {/* Fixed height container */}
                            <div className="h-4 bg-muted rounded w-1/2" />
                          </div>
                        </div>
                      </div>

                      {/* Description skeleton - Fixed height */}
                      <div className="mb-4 h-16">
                        <div className="h-4 bg-muted rounded w-full mb-2" />
                        <div className="h-4 bg-muted rounded w-2/3 mb-2" />
                        <div className="h-4 bg-muted rounded w-1/2" />
                      </div>

                      {/* Badges skeleton - Fixed height */}
                      <div className="flex items-center gap-2 mb-4 h-6">
                        <div className="h-5 bg-muted rounded w-20" />
                        <div className="h-5 bg-muted rounded w-16" />
                      </div>

                      {/* Buttons skeleton - Always at bottom */}
                      <div className="flex gap-2 mt-auto">
                        <div className="h-8 bg-muted rounded flex-1" />
                        <div className="h-8 w-8 bg-muted rounded" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : favorites.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Belum Ada Favorit</h3>
                <p className="text-muted-foreground mb-6">
                  Anda belum menambahkan character apapun ke favorit.
                </p>
                <Button
                  onClick={() => router.push('/dashboard')}
                  variant="outline"
                >
                  Jelajahi Character
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {favorites.map((character) => (
                  <Card key={character.id} className="hover:shadow-lg transition-shadow flex flex-col h-full">
                    <CardContent className="p-6 flex flex-col h-full">
                      {/* Header - Fixed height */}
                      <div className="flex items-center gap-4 mb-4">
                        <Avatar className="w-16 h-16 flex-shrink-0">
                          <AvatarImage src={character.image} alt={character.name} />
                          <AvatarFallback>
                            {character.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-lg truncate">{character.name}</h3>
                          <div className="h-5"> {/* Fixed height container for title */}
                            {character.title && (
                              <p className="text-sm text-muted-foreground truncate">{character.title}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Description - Fixed height */}
                      <div className="mb-4 h-16 overflow-hidden">
                        <p className="text-sm text-muted-foreground line-clamp-3">
                          {character.description}
                        </p>
                      </div>

                      {/* Badges - Fixed height */}
                      <div className="flex items-center gap-2 mb-4 h-6">
                        {character.storyMode && (
                          <Badge className="bg-bestieku-primary text-white text-xs">
                            Story Mode
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          <MessageCircle className="w-3 h-3 mr-1" />
                          {character.messageCount} pesan
                        </Badge>
                      </div>

                      {/* Buttons - Always at bottom */}
                      <div className="flex gap-2 mt-auto">
                        <Button
                          size="sm"
                          onClick={() => handleChatWithCharacter(character.id)}
                          className="flex-1 bg-gradient-bestieku hover:bg-gradient-bestieku-reverse"
                        >
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Chat
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveFavorite(character.id, character.name)}
                          disabled={removingFavorite === character.id}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          {removingFavorite === character.id ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <Heart className="w-4 h-4 fill-current" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
