"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/immersive-mode.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/immersive-mode.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImmersiveMode: () => (/* binding */ ImmersiveMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ImmersiveMode auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImmersiveMode(param) {\n    let { isOpen, onClose, character, messages, streamingMessage, isStreaming, onSendMessage, disabled, characterAssets, profileImageUrl } = param;\n    var _backgroundAssets_;\n    _s();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticles, setShowParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [characterMood, setCharacterMood] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('neutral');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Get profile assets for character display\n    const profileAssets = characterAssets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    // Get background assets for ambient backgrounds\n    const backgroundAssets = characterAssets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    // Cycle through character images every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (profileAssets.length <= 1) return;\n            const interval = setInterval({\n                \"ImmersiveMode.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"ImmersiveMode.useEffect.interval\": (prev)=>(prev + 1) % profileAssets.length\n                    }[\"ImmersiveMode.useEffect.interval\"]);\n                }\n            }[\"ImmersiveMode.useEffect.interval\"], 10000);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>clearInterval(interval)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        profileAssets.length\n    ]);\n    // Focus input when opening\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isOpen) {\n                setTimeout({\n                    \"ImmersiveMode.useEffect\": ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                    }\n                }[\"ImmersiveMode.useEffect\"], 100);\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen\n    ]);\n    // Update character mood based on chat state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isStreaming) {\n                setCharacterMood('thinking');\n            } else if (messages.length > 0) {\n                const lastMessage = messages[messages.length - 1];\n                if (lastMessage.role === 'user') {\n                    setCharacterMood('happy');\n                } else {\n                    setCharacterMood('neutral');\n                }\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isStreaming,\n        messages\n    ]);\n    // Handle keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (!isOpen) return;\n            const handleKeyDown = {\n                \"ImmersiveMode.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape') {\n                        onClose();\n                    }\n                }\n            }[\"ImmersiveMode.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    const handleSendMessage = ()=>{\n        const message = newMessage.trim();\n        if (!message || disabled) return;\n        onSendMessage(message);\n        setNewMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleEmojiSelect = (emoji)=>{\n        var _inputRef_current;\n        setNewMessage((prev)=>prev + emoji);\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    if (!isOpen) return null;\n    const currentProfileAsset = profileAssets[currentImageIndex];\n    const currentProfileImage = (currentProfileAsset === null || currentProfileAsset === void 0 ? void 0 : currentProfileAsset.url) || profileImageUrl || (character === null || character === void 0 ? void 0 : character.image);\n    const currentBackground = (_backgroundAssets_ = backgroundAssets[0]) === null || _backgroundAssets_ === void 0 ? void 0 : _backgroundAssets_.url;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20\",\n                style: {\n                    backgroundImage: currentBackground ? \"linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)), url(\".concat(currentBackground, \")\") : undefined,\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            showParticles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: characterMood === 'thinking' ? 30 : 20\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute rounded-full animate-pulse \".concat(characterMood === 'thinking' ? 'w-1 h-1 bg-blue-400/30' : characterMood === 'happy' ? 'w-2 h-2 bg-yellow-400/20' : 'w-1 h-1 bg-white/20'),\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                                animationDuration: \"\".concat(characterMood === 'thinking' ? 1 + Math.random() * 2 : 2 + Math.random() * 3, \"s\")\n                            }\n                        }, i, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)),\n                    characterMood === 'thinking' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                0,\n                                1,\n                                2\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-400/50 rounded-full animate-bounce\",\n                                    style: {\n                                        animationDelay: \"\".concat(i * 0.2, \"s\"),\n                                        animationDuration: '1s'\n                                    }\n                                }, i, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 right-4 flex justify-between items-center z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-white text-xl font-bold\",\n                                children: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 text-sm\",\n                                children: \"Theater Mode\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                className: \"text-white hover:bg-white/10\",\n                                title: isAudioEnabled ? \"Disable Audio\" : \"Enable Audio\",\n                                children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 65\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setShowParticles(!showParticles),\n                                className: \"text-white hover:bg-white/10\",\n                                title: showParticles ? \"Hide Effects\" : \"Show Effects\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"text-white hover:bg-white/10\",\n                                title: \"Exit Immersive Mode\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full pt-16 pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center justify-center p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-80 h-80 rounded-full overflow-hidden border-4 shadow-2xl transition-all duration-1000 \".concat(characterMood === 'thinking' ? 'border-blue-400/50 shadow-blue-400/20' : characterMood === 'happy' ? 'border-yellow-400/50 shadow-yellow-400/20' : 'border-white/20 shadow-white/10'),\n                                    children: [\n                                        currentProfileImage && /\\.mp4(\\?|$)/i.test(currentProfileImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: currentProfileImage,\n                                            className: \"w-full h-full object-cover\",\n                                            autoPlay: true,\n                                            muted: true,\n                                            loop: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentProfileImage,\n                                            alt: character === null || character === void 0 ? void 0 : character.name,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm\",\n                                        children: isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Typing...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 bg-black/40 backdrop-blur-md border-l border-white/10 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    messages.slice(-10).map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[80%] rounded-2xl px-4 py-3 \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white' : 'bg-white/10 backdrop-blur-sm text-white border border-white/20'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: message.content\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, message.id, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)),\n                                    isStreaming && streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[80%] rounded-2xl px-4 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: streamingMessage\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block w-2 h-4 bg-white/60 animate-pulse ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    ref: inputRef,\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Message \".concat((character === null || character === void 0 ? void 0 : character.name) || 'character', \"...\"),\n                                                    disabled: disabled,\n                                                    className: \"bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-white/60 rounded-2xl pr-12\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_4__.EmojiPicker, {\n                                                        onEmojiSelect: handleEmojiSelect,\n                                                        disabled: disabled\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleSendMessage,\n                                            disabled: !newMessage.trim() || disabled,\n                                            className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(ImmersiveMode, \"NmcayRwAXc8xtHdXruQ832Az/7k=\");\n_c = ImmersiveMode;\nvar _c;\n$RefreshReg$(_c, \"ImmersiveMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/immersive-mode.tsx\n"));

/***/ })

});