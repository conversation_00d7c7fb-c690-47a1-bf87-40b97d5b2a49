'use client';

import React, { useState, useEffect } from 'react';
import { Character, CharacterAsset } from '@/types/character';
import { characterService } from '@/services/character';
import { favoriteService } from '@/services/favorite';
import { useAuth } from '@/contexts/auth-context';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { PhoneNumberModal } from './phone-number-modal';
import { toast } from 'sonner';
import {
  Calendar,
  Tag,
  MessageCircle,
  Star,
  Heart,
  Info,
  X,
  ChevronRight,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  Image as ImageIcon,
  Settings,
  ExternalLink,
  Share2,
  Copy,
  Check
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';

// Simple avatar component that always uses static image
const SimpleAvatar = ({ src, alt, className }: {
  src: string | null;
  alt: string;
  className?: string;
}) => {
  if (!src) {
    return (
      <div className={`text-2xl flex size-full items-center justify-center rounded-full bg-muted ${className || ''}`}>
        {alt?.charAt(0)?.toUpperCase() || 'C'}
      </div>
    );
  }

  return (
    <img
      src={src}
      alt={alt}
      className={`aspect-square size-full object-cover ${className || ''}`}
    />
  );
};

interface CharacterProfileSidebarProps {
  characterId: string;
  messageCount: number;
  isOpen: boolean;
  onToggle: () => void;
  onBackgroundChange?: (backgroundAssetId: string | null) => void;
}

export function CharacterProfileSidebar({
  characterId,
  messageCount,
  isOpen,
  onToggle,
  onBackgroundChange
}: CharacterProfileSidebarProps) {
  const { user } = useAuth();
  const [character, setCharacter] = useState<Character | null>(null);
  const [assets, setAssets] = useState<CharacterAsset[]>([]);
  const [loading, setLoading] = useState(true);
  const [assetsLoading, setAssetsLoading] = useState(true);
  const [currentProfileImageIndex, setCurrentProfileImageIndex] = useState(0);
  const [selectedBackground, setSelectedBackground] = useState<string | null>(null);
  const [showBackgroundSettings, setShowBackgroundSettings] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFavoriteLoading, setIsFavoriteLoading] = useState(false);
  const [showPhoneModal, setShowPhoneModal] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  useEffect(() => {
    loadCharacter();
    loadAssets();
    checkFavoriteStatus();
  }, [characterId]);

  const loadCharacter = async () => {
    try {
      setLoading(true);
      const characterData = await characterService.getCharacterById(characterId);
      setCharacter(characterData);
    } catch (error) {
      console.error('Failed to load character:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAssets = async () => {
    try {
      setAssetsLoading(true);
      const assetsData = await characterService.getCharacterAssets(characterId);
      setAssets(assetsData);
      
      // Auto-select first background if character has backgrounds and no background is currently selected
      const backgroundAssets = assetsData.filter(asset => asset.purpose === 'background' && asset.isPublished);
      if (backgroundAssets.length > 0 && selectedBackground === null) {
        const defaultBackground = backgroundAssets[0].id;
        setSelectedBackground(defaultBackground);
        onBackgroundChange?.(defaultBackground);
      }
    } catch (error) {
      console.error('Failed to load character assets:', error);
    } finally {
      setAssetsLoading(false);
    }
  };

  const checkFavoriteStatus = async () => {
    try {
      const favoritesResponse = await favoriteService.getFavorites();
      const isFavorited = favoritesResponse.data.some(fav => fav.id === characterId);
      setIsFavorite(isFavorited);
    } catch (error) {
      console.error('Failed to check favorite status:', error);
      // Don't show error toast for this, just keep default state
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: id });
    } catch {
      return 'Unknown';
    }
  };

  // Helper functions for assets
  const getProfileAssets = () => {
    return assets.filter(asset => asset.purpose === 'profile' && asset.isPublished);
  };

  const getBackgroundAssets = () => {
    return assets.filter(asset => asset.purpose === 'background' && asset.isPublished);
  };

  const handleBackgroundChange = (backgroundId: string | null) => {
    setSelectedBackground(backgroundId);
    onBackgroundChange?.(backgroundId);
  };

  const handleFavoriteToggle = async () => {
    if (!character) return;

    setIsFavoriteLoading(true);
    try {
      if (isFavorite) {
        await favoriteService.removeFromFavorite(character.id);
        setIsFavorite(false);
        toast.success("Dihapus dari favorit", {
          description: `${character.name} telah dihapus dari daftar favorit Anda.`,
          duration: 3000,
        });
      } else {
        await favoriteService.addToFavorite(character.id);
        setIsFavorite(true);
        toast.success("Ditambahkan ke favorit", {
          description: `${character.name} telah ditambahkan ke daftar favorit Anda.`,
          duration: 3000,
        });
      }
    } catch (error) {
      toast.error("Gagal mengubah favorit", {
        description: error instanceof Error ? error.message : "Terjadi kesalahan saat mengubah status favorit.",
        duration: 4000,
      });
    } finally {
      setIsFavoriteLoading(false);
    }
  };

  const handleWhatsAppClick = () => {
    if (!character?.whatsappUrl) return;

    // Check if user has phone number
    if (!user?.phoneNumber) {
      setShowPhoneModal(true);
      return;
    }

    // If user has phone number, proceed to WhatsApp
    window.open(character.whatsappUrl, '_blank');
  };

  const handlePhoneModalSuccess = () => {
    if (character?.whatsappUrl) {
      window.open(character.whatsappUrl, '_blank');
    }
  };

  const handleCopyLink = async () => {
    if (!character) return;

    const characterUrl = `${window.location.origin}/dashboard?character=${character.id}`;

    try {
      await navigator.clipboard.writeText(characterUrl);
      setLinkCopied(true);
      toast.success("Link disalin!", {
        description: "Link karakter telah disalin ke clipboard. Bagikan ke teman-temanmu!",
        duration: 3000,
      });

      // Reset copied state after 3 seconds
      setTimeout(() => setLinkCopied(false), 3000);

    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error("Gagal menyalin link", {
        description: "Terjadi kesalahan saat menyalin link karakter.",
        duration: 3000,
      });
    }
  };

  const handleShareToWhatsApp = () => {
    if (!character) return;

    const characterUrl = `${window.location.origin}/dashboard?character=${character.id}`;
    const message = `Halo! Ayo chat dengan ${character.name} di Bestieku! 🤖\n\n${character.description}\n\nKlik link ini: ${characterUrl}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;

    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <div className={`bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden ${
        isOpen ? 'w-80' : 'w-12'
      }`}>
        <div className="flex-shrink-0 h-20 p-4 border-b flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="w-full"
          >
            {isOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>

        {isOpen && (
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-4 animate-pulse">
              <div className="w-full h-32 bg-muted rounded-lg" />
              <div className="h-6 bg-muted rounded w-3/4" />
              <div className="h-4 bg-muted rounded w-full" />
              <div className="h-4 bg-muted rounded w-2/3" />
            </div>
          </div>
        )}
      </div>
    );
  }

  if (!character) {
    return (
      <div className={`bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden ${
        isOpen ? 'w-80' : 'w-12'
      }`}>
        <div className="flex-shrink-0 h-20 p-4 border-b flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="w-full"
          >
            {isOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>

        {isOpen && (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center text-muted-foreground">
              <Info className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">Karakter tidak ditemukan</p>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden ${
      isOpen ? 'w-80' : 'w-12'
    }`}>
      {/* Toggle Button - Fixed */}
      <div className="flex-shrink-0 h-20 p-4 border-b flex items-center justify-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="w-full"
        >
          {isOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>
      </div>

      {/* Profile Content - Scrollable */}
      {isOpen && (
        <div className="flex-1 overflow-y-auto overflow-x-hidden p-4">
          <div className="space-y-6">
            {/* Character Avatar & Name */}
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 relative flex shrink-0 overflow-hidden rounded-full">
                <SimpleAvatar
                  src={(() => {
                    // Get first profile image asset (not video)
                    const profileAssets = getProfileAssets().filter(asset => asset.type === 'image');
                    return profileAssets.length > 0 ? profileAssets[0].url : character.image;
                  })()}
                  alt={character.name}
                />
              </div>

              <h2 className="text-xl font-bold mb-1">{character.name}</h2>

              {character.title && (
                <p className="text-sm text-muted-foreground mb-3 font-medium">
                  {character.title}
                </p>
              )}

              <div className="flex items-center justify-center gap-2 mb-3">
                {character.storyMode && (
                  <Badge className="bg-bestieku-primary text-white">
                    Mode Cerita
                  </Badge>
                )}
                <Badge variant="outline" className="capitalize">
                  {character.status}
                </Badge>
              </div>

              {/* WhatsApp Button - Show prominently if available */}
              {character.whatsappUrl && (
                <div className="flex justify-center mb-3">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleWhatsAppClick}
                    className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
                  >
                    <MessageCircle className="w-4 h-4" />
                    Chat di WhatsApp
                    <ExternalLink className="w-3 h-3" />
                  </Button>
                </div>
              )}

              {/* Favorite Button */}
              <div className="flex justify-center mb-3">
                <Button
                  variant={isFavorite ? "default" : "outline"}
                  size="sm"
                  onClick={handleFavoriteToggle}
                  disabled={isFavoriteLoading}
                  className={`flex items-center gap-2 ${
                    isFavorite
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'hover:bg-red-50 hover:text-red-600 hover:border-red-300'
                  }`}
                >
                  <Heart
                    className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`}
                  />
                  {isFavoriteLoading
                    ? 'Loading...'
                    : isFavorite
                      ? 'Favorit'
                      : 'Tambah ke Favorit'
                  }
                </Button>
              </div>

              {/* Share Character Buttons - Moved to top for better visibility */}
              <div className="flex justify-center gap-2 mb-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                  className="flex items-center gap-2 flex-1"
                >
                  {linkCopied ? (
                    <>
                      <Check className="w-4 h-4" />
                      Link Disalin!
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4" />
                      Salin Link
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShareToWhatsApp}
                  className="flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                >
                  <Share2 className="w-4 h-4" />
                  WhatsApp
                </Button>
              </div>
            </div>

            <Separator />

            {/* Description */}
            <div>
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <Info className="w-4 h-4" />
                Tentang
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {character.description}
              </p>
            </div>

            <Separator />

            {/* Tags */}
            {character.tags && character.tags.length > 0 && (
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  Tag
                </h3>
                <div className="flex flex-wrap gap-2">
                  {character.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            {/* Profile Gallery */}
            {!assetsLoading && getProfileAssets().length > 0 && (
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <ImageIcon className="w-4 h-4" />
                  Galeri Profil
                </h3>
                <div className="space-y-3">
                  <div
                    className="relative"
                    onMouseEnter={(e) => {
                      const video = e.currentTarget.querySelector('video');
                      if (video) video.play();
                    }}
                    onMouseLeave={(e) => {
                      const video = e.currentTarget.querySelector('video');
                      if (video) {
                        video.pause();
                        video.currentTime = 0;
                      }
                    }}
                    onTouchStart={(e) => {
                      const video = e.currentTarget.querySelector('video');
                      if (video) video.play();
                    }}
                    onTouchEnd={(e) => {
                      const video = e.currentTarget.querySelector('video');
                      if (video) {
                        video.pause();
                        video.currentTime = 0;
                      }
                    }}
                  >
                    {(() => {
                      const asset = getProfileAssets()[currentProfileImageIndex];
                      const url = asset?.url;
                      return url && /\.mp4(\?|$)/i.test(url) ? (
                        <video
                          src={url}
                          className="w-full h-48 object-cover rounded-lg pointer-events-none"
                          muted
                          playsInline
                          preload="metadata"
                          poster={url + "#t=0.1"}
                          onLoadedData={(e) => {
                            e.currentTarget.currentTime = 0;
                          }}
                        />
                      ) : (
                        <img
                          src={url}
                          alt={asset?.caption || 'Profile image'}
                          className="w-full h-48 object-cover rounded-lg"
                        />
                      );
                    })()}
                    {getProfileAssets().length > 1 && (
                      <div className="absolute inset-x-0 bottom-2 flex justify-center gap-2">
                        <Button
                          variant="secondary"
                          size="icon"
                          className="w-8 h-8 bg-black/50 hover:bg-black/70 text-white"
                          onClick={() => setCurrentProfileImageIndex(
                            currentProfileImageIndex > 0 ? currentProfileImageIndex - 1 : getProfileAssets().length - 1
                          )}
                        >
                          <ChevronLeft className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="secondary"
                          size="icon"
                          className="w-8 h-8 bg-black/50 hover:bg-black/70 text-white"
                          onClick={() => setCurrentProfileImageIndex(
                            currentProfileImageIndex < getProfileAssets().length - 1 ? currentProfileImageIndex + 1 : 0
                          )}
                        >
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                  {getProfileAssets()[currentProfileImageIndex]?.caption && (
                    <p className="text-sm text-muted-foreground text-center">
                      {getProfileAssets()[currentProfileImageIndex].caption}
                    </p>
                  )}
                  {getProfileAssets().length > 1 && (
                    <div className="flex justify-center gap-1">
                      {getProfileAssets().map((_, index) => (
                        <button
                          key={index}
                          className={`w-2 h-2 rounded-full transition-colors ${
                            index === currentProfileImageIndex ? 'bg-bestieku-primary' : 'bg-muted'
                          }`}
                          onClick={() => setCurrentProfileImageIndex(index)}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {!assetsLoading && getProfileAssets().length > 0 && <Separator />}

            {/* Chat Stats */}
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <MessageCircle className="w-4 h-4" />
                Statistik Chat
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Pesan Anda</span>
                  <span className="text-sm font-medium">{messageCount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Pesan</span>
                  <span className="text-sm font-medium text-bestieku-primary">
                    {character.messageCount > 999 ? '999+' : character.messageCount}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Jenis Karakter</span>
                  <span className="text-sm font-medium">
                    {character.storyMode ? 'Story' : 'Chat'}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Character Info */}
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Info Karakter
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Dibuat</span>
                  <span className="text-sm font-medium">
                    {formatDate(character.createdAt)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Diperbarui</span>
                  <span className="text-sm font-medium">
                    {formatDate(character.updatedAt)}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Background Settings */}
            {!assetsLoading && getBackgroundAssets().length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Latar Belakang Chat
                  </h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowBackgroundSettings(!showBackgroundSettings)}
                    className="w-6 h-6"
                  >
                    {showBackgroundSettings ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </Button>
                </div>

                {showBackgroundSettings && (
                  <div className="space-y-3">
                    {/* Default background option */}
                    <div
                      className={`p-3 rounded-lg border-2 cursor-pointer transition-colors ${
                        selectedBackground === null
                          ? 'border-bestieku-primary bg-bestieku-primary/10'
                          : 'border-muted hover:border-muted-foreground/50'
                      }`}
                      onClick={() => handleBackgroundChange(null)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-8 bg-gradient-to-b from-background to-muted/20 rounded border"></div>
                        <div>
                          <p className="text-sm font-medium">Default</p>
                          <p className="text-xs text-muted-foreground">Latar belakang polos</p>
                        </div>
                      </div>
                    </div>

                    {/* Background asset options */}
                    {getBackgroundAssets().map((asset) => (
                      <div
                        key={asset.id}
                        className={`p-3 rounded-lg border-2 cursor-pointer transition-colors ${
                          selectedBackground === asset.id
                            ? 'border-bestieku-primary bg-bestieku-primary/10'
                            : 'border-muted hover:border-muted-foreground/50'
                        }`}
                        onClick={() => handleBackgroundChange(asset.id)}
                      >
                        <div className="flex items-center gap-3">
                          {/\.mp4(\?|$)/i.test(asset.url) ? (
                            <video
                              src={asset.url}
                              className="w-12 h-8 object-cover rounded border pointer-events-none"
                              muted
                              playsInline
                              preload="metadata"
                              onMouseEnter={(e) => e.currentTarget.play()}
                              onMouseLeave={(e) => {
                                e.currentTarget.pause();
                                e.currentTarget.currentTime = 0;
                              }}
                            />
                          ) : (
                            <img
                              src={asset.url}
                              alt={asset.caption || 'Background'}
                              className="w-12 h-8 object-cover rounded border"
                            />
                          )}
                          <div>
                            <p className="text-sm font-medium">
                              {asset.caption || 'Latar Belakang Kustom'}
                            </p>
                            <p className="text-xs text-muted-foreground">Latar belakang karakter</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {!assetsLoading && getBackgroundAssets().length > 0 && <Separator />}

          </div>
        </div>
      )}

      {/* Phone Number Modal */}
      <PhoneNumberModal
        isOpen={showPhoneModal}
        onClose={() => setShowPhoneModal(false)}
        onSuccess={handlePhoneModalSuccess}
        characterName={character?.name || 'Character'}
      />
    </div>
  );
}
