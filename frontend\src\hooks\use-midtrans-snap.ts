import { useEffect, useRef } from 'react';

interface MidtransSnapOptions {
  onSuccess?: (result: any) => void;
  onPending?: (result: any) => void;
  onError?: (result: any) => void;
  onClose?: () => void;
}

declare global {
  interface Window {
    snap: {
      pay: (token: string, options?: MidtransSnapOptions) => void;
      embed: (token: string, options: MidtransSnapOptions & { embedId: string }) => void;
    };
  }
}

export function useMidtransSnap() {
  const isSnapLoaded = useRef(false);
  const isScriptLoaded = useRef(false);

  useEffect(() => {
    if (!isScriptLoaded.current) {
      const script = document.createElement('script');
      script.src = 'https://app.sandbox.midtrans.com/snap/snap.js'; // Sandbox untuk staging
      script.setAttribute('data-client-key', process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY || '');
      
      script.onload = () => {
        isSnapLoaded.current = true;
        isScriptLoaded.current = true;
      };
      
      script.onerror = () => {
        console.error('Failed to load Midtrans SNAP script');
      };

      document.head.appendChild(script);

      return () => {
        // Cleanup script when component unmounts
        const existingScript = document.querySelector('script[src*="snap.js"]');
        if (existingScript) {
          document.head.removeChild(existingScript);
          isScriptLoaded.current = false;
          isSnapLoaded.current = false;
        }
      };
    }
  }, []);

  const payWithSnap = (token: string, options: MidtransSnapOptions) => {
    if (!isSnapLoaded.current || !window.snap) {
      console.error('Midtrans SNAP not loaded yet');
      return;
    }

    window.snap.pay(token, options);
  };

  const embedSnap = (token: string, embedId: string, options: MidtransSnapOptions) => {
    if (!isSnapLoaded.current || !window.snap) {
      console.error('Midtrans SNAP not loaded yet');
      return;
    }

    window.snap.embed(token, {
      ...options,
      embedId,
    });
  };

  return {
    isSnapLoaded: isSnapLoaded.current,
    payWithSnap,
    embedSnap,
  };
}
