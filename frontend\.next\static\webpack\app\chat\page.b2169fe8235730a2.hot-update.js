"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./reset-chat-modal */ \"(app-pages-browser)/./src/components/chat/reset-chat-modal.tsx\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, profileImageUrl, characterName } = param;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c2 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { streamingMessage, profileImageUrl, characterName } = param;\n    _s1();\n    const [thinkingText, setThinkingText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('Berpikir');\n    // Cycle through thinking messages when not streaming actual content\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamingMessage.useEffect\": ()=>{\n            if (!streamingMessage) {\n                const messages = [\n                    'Berpikir',\n                    'Merenungkan',\n                    'Mempertimbangkan',\n                    'Memikirkan jawaban'\n                ];\n                let index = 0;\n                const interval = setInterval({\n                    \"StreamingMessage.useEffect.interval\": ()=>{\n                        index = (index + 1) % messages.length;\n                        setThinkingText(messages[index]);\n                    }\n                }[\"StreamingMessage.useEffect.interval\"], 2000);\n                return ({\n                    \"StreamingMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMessage.useEffect\"];\n            }\n        }\n    }[\"StreamingMessage.useEffect\"], [\n        streamingMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: thinkingText\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n}, \"W7FciA9Z3UpxXy1pJlVC9GvQ8tw=\"));\n_c3 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount, profileImageUrl } = param;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                src: profileImageUrl,\n                                alt: character.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Mode Cerita\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages, profileImageUrl } = param;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                        src: profileImageUrl,\n                        alt: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s2((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s2();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    // Detect mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInput.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInput.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInput.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInput.useEffect\"];\n        }\n    }[\"ChatInput.useEffect\"], []);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    const handleEmojiSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleEmojiSelect]\": (emoji)=>{\n            const newValue = newMessage + emoji;\n            setNewMessage(newValue);\n            messageRef.current = newValue;\n            // Focus back to input after emoji selection\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatInput.useCallback[handleEmojiSelect]\"], [\n        newMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 \".concat(isMobile ? 'p-3 pb-6' : 'p-4'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"\".concat(isMobile ? 'pr-20' : 'pr-24', \" rounded-2xl border-2 focus:border-bestieku-primary transition-colors \").concat(isMobile ? 'py-3 text-base' : 'py-3'),\n                                style: isMobile ? {\n                                    fontSize: '16px'\n                                } : {}\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_9__.EmojiPicker, {\n                                        onEmojiSelect: handleEmojiSelect,\n                                        disabled: disabled\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 \".concat(isMobile ? 'px-4 py-3' : 'px-6 py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 383,\n        columnNumber: 5\n    }, undefined);\n}, \"0TgdyP+W7Ya7Fi9I41+kS8az6FU=\"));\n_c6 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s3((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c7 = _s3((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5;\n    _s3();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileImageUrl, setProfileImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show reset confirmation modal\n    const handleResetClick = ()=>{\n        setShowResetModal(true);\n    };\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Close modal\n            setShowResetModal(false);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                        // Get profile image from assets (prefer static image over video)\n                        const profileAssets = assets.filter({\n                            \"ChatInterface.useEffect.loadCharacterData.profileAssets\": (asset)=>asset.purpose === 'profile' && asset.type === 'image' && asset.isPublished\n                        }[\"ChatInterface.useEffect.loadCharacterData.profileAssets\"]);\n                        if (profileAssets.length > 0) {\n                            setProfileImageUrl(profileAssets[0].url);\n                        } else {\n                            // Fallback to character.image if no profile assets\n                            setProfileImageUrl(characterData.image);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Reset all streaming states when chat changes\n            setIsStreaming(false);\n            setStreamingMessage('');\n            setSending(false);\n            // Cleanup any existing connections and timeouts\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n                streamConnectionRef.current = null;\n            }\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n                streamingTimeoutRef.current = null;\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n                streamTimeoutRef.current = null;\n            }\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const currentChatId = chat.id; // Capture current chat ID\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Show AI thinking for 3-9 seconds before sending to backend\n                setIsStreaming(true);\n                setStreamingMessage('');\n                const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms\n                console.log(\"AI thinking for \".concat(thinkingDelay, \"ms before processing...\"));\n                await new Promise({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (resolve)=>setTimeout(resolve, thinkingDelay)\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Check if chat hasn't changed during the thinking delay\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed during thinking delay, aborting message send');\n                    return;\n                }\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Check again if chat hasn't changed after API call\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed after message send, aborting streaming');\n                    return;\n                }\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming(currentChatId);\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Only show error and remove message if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                    alert('Failed to send message. Please try again.');\n                }\n            } finally{\n                // Only reset sending state if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setSending(false);\n                }\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async (expectedChatId)=>{\n        const targetChatId = expectedChatId || chat.id;\n        console.log('Starting stream for chat:', targetChatId);\n        // Check if chat hasn't changed before starting stream\n        if (expectedChatId && chat.id !== expectedChatId) {\n            console.log('Chat changed before streaming started, aborting');\n            return;\n        }\n        // isStreaming already set to true in handleSendMessage\n        // setStreamingMessage(''); // Keep current thinking state\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error and streaming was actually active\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active and connection exists\n                if (isStreaming && streamConnectionRef.current) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 873,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 870,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 879,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 869,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full \".concat(isMobile ? 'h-16 p-3' : 'h-20 p-4'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 899,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 922,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleResetClick,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 919,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 897,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 961,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 970,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    profileImageUrl: profileImageUrl,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                profileImageUrl: profileImageUrl,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 948,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1004,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__.ResetChatModal, {\n                isOpen: showResetModal,\n                onClose: ()=>setShowResetModal(false),\n                onConfirm: handleDeleteMessages,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || ((_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name),\n                messageCount: messages.length,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1012,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 895,\n        columnNumber: 5\n    }, undefined);\n}, \"TWWU90YYrjWW8OsXbApf3QOv7v8=\")), \"TWWU90YYrjWW8OsXbApf3QOv7v8=\");\n_c8 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"StreamingMessage\");\n$RefreshReg$(_c4, \"WelcomeMessage\");\n$RefreshReg$(_c5, \"OpeningScene\");\n$RefreshReg$(_c6, \"ChatInput\");\n$RefreshReg$(_c7, \"ChatInterface$memo\");\n$RefreshReg$(_c8, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});