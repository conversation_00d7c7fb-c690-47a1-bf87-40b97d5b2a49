import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { countries, Country, parsePhoneNumber, formatPhoneNumber } from '@/data/countries';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function PhoneInput({ 
  value, 
  onChange, 
  placeholder = "Nomor telepon", 
  disabled = false,
  className = ""
}: PhoneInputProps) {
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]); // Default to Indonesia
  const [localNumber, setLocalNumber] = useState('');

  // Parse existing phone number when component mounts or value changes
  useEffect(() => {
    if (value) {
      const parsed = parsePhoneNumber(value);
      if (parsed) {
        const country = countries.find(c => c.dialCode === parsed.countryCode);
        if (country) {
          setSelectedCountry(country);
          setLocalNumber(parsed.localNumber);
        }
      }
    }
  }, [value]);

  const handleCountryChange = (countryCode: string) => {
    const country = countries.find(c => c.code === countryCode);
    if (country) {
      setSelectedCountry(country);
      // Update the full phone number
      if (localNumber) {
        const fullNumber = formatPhoneNumber(country.dialCode, localNumber);
        onChange(fullNumber);
      }
    }
  };

  const handleLocalNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Only allow numbers
    const cleanValue = inputValue.replace(/[^\d]/g, '');
    
    setLocalNumber(cleanValue);
    
    // Update the full phone number
    if (cleanValue) {
      const fullNumber = formatPhoneNumber(selectedCountry.dialCode, cleanValue);
      onChange(fullNumber);
    } else {
      onChange('');
    }
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      {/* Country Code Selector */}
      <Select
        value={selectedCountry.code}
        onValueChange={handleCountryChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-32 flex-shrink-0">
          <SelectValue>
            <div className="flex items-center gap-2">
              <span className="text-lg">{selectedCountry.flag}</span>
              <span className="text-sm">+{selectedCountry.dialCode}</span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {countries.map((country) => (
            <SelectItem key={country.code} value={country.code}>
              <div className="flex items-center gap-2">
                <span className="text-lg">{country.flag}</span>
                <span className="text-sm font-medium">{country.name}</span>
                <span className="text-xs text-muted-foreground">+{country.dialCode}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Phone Number Input */}
      <Input
        type="tel"
        value={localNumber}
        onChange={handleLocalNumberChange}
        placeholder={placeholder}
        disabled={disabled}
        className="flex-1"
      />
    </div>
  );
}
