import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const accessToken = (await cookieStore.get('accessToken'))?.value;
    const tokenExpiredAt = (await cookieStore.get('tokenExpiredAt'))?.value;
    const allCookies = await cookieStore.getAll();

    console.log('Transfer tokens request:', {
      hasAccessToken: !!accessToken,
      hasTokenExpiredAt: !!tokenExpiredAt,
      accessTokenLength: accessToken?.length,
      allCookies: allCookies.map(c => c.name)
    });

    if (!accessToken || !tokenExpiredAt) {
      console.error('Tokens not found in cookies:', {
        accessToken: !!accessToken,
        tokenExpiredAt: !!tokenExpiredAt
      });
      return NextResponse.json(
        { error: 'Tokens not found in cookies' },
        { status: 400 }
      );
    }

    // Create response with tokens
    const response = NextResponse.json({
      accessToken,
      expiredAt: tokenExpiredAt,
    });

    // Clear the cookies after transferring
    response.cookies.delete('accessToken');
    response.cookies.delete('tokenExpiredAt');

    return response;
  } catch (error) {
    console.error('Token transfer error:', error);
    return NextResponse.json(
      { error: 'Failed to transfer tokens' },
      { status: 500 }
    );
  }
}
