import { NextRequest, NextResponse } from 'next/server';

function getCorrectOrigin(request: NextRequest): string {
  // Try to get the correct origin from various sources
  const forwardedHost = request.headers.get('x-forwarded-host');
  const forwardedProto = request.headers.get('x-forwarded-proto') || 'https';
  const host = request.headers.get('host');

  if (forwardedHost) {
    return `${forwardedProto}://${forwardedHost}`;
  }

  if (host && !host.includes('localhost')) {
    return `https://${host}`;
  }

  // Fallback to nextUrl.origin
  return request.nextUrl.origin;
}

export async function GET(request: NextRequest) {
  try {
    const correctOrigin = getCorrectOrigin(request);

    console.log('=== CALLBACK DEBUG INFO ===');
    console.log('request.url:', request.url);
    console.log('request.nextUrl.origin:', request.nextUrl.origin);
    console.log('request.nextUrl.href:', request.nextUrl.href);
    console.log('request.headers.host:', request.headers.get('host'));
    console.log('request.headers.x-forwarded-host:', request.headers.get('x-forwarded-host'));
    console.log('request.headers.x-forwarded-proto:', request.headers.get('x-forwarded-proto'));
    console.log('correctOrigin:', correctOrigin);
    console.log('process.env.NODE_ENV:', process.env.NODE_ENV);
    console.log('===========================');

    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Handle OAuth error
    if (error) {
      console.error('OAuth error:', error);
      return NextResponse.redirect(
        new URL(`/auth/error?error=${encodeURIComponent(error)}`, correctOrigin)
      );
    }

    // Validate required parameters
    if (!code) {
      return NextResponse.redirect(
        new URL('/auth/error?error=missing_code', correctOrigin)
      );
    }

    // Exchange authorization code for access token with Google
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${correctOrigin}/api/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      console.error('Token exchange failed:', errorData);
      return NextResponse.redirect(
        new URL('/auth/error?error=token_exchange_failed', correctOrigin)
      );
    }

    const tokenData = await tokenResponse.json();
    const { id_token } = tokenData;

    if (!id_token) {
      return NextResponse.redirect(
        new URL('/auth/error?error=missing_id_token', correctOrigin)
      );
    }

    // Send ID token to our backend for authentication
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api-staging.bestieku.ai';

    console.log('Sending request to backend:', {
      url: `${API_BASE_URL}/auth/google`,
      hasIdToken: !!id_token,
      idTokenLength: id_token?.length
    });

    const authResponse = await fetch(`${API_BASE_URL}/auth/google`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        idToken: id_token,
      }),
    });

    if (!authResponse.ok) {
      const errorData = await authResponse.json();
      console.error('Backend auth failed:', {
        status: authResponse.status,
        statusText: authResponse.statusText,
        errorData,
        url: `${API_BASE_URL}/auth/google`
      });
      return NextResponse.redirect(
        new URL(`/auth/error?error=${encodeURIComponent(errorData.message || 'auth_failed')}`, correctOrigin)
      );
    }

    const authData = await authResponse.json();
    const { accessToken, expiredAt, issuedAt } = authData;

    // Redirect to success page with tokens in URL params (will be handled by client)
    const successUrl = new URL('/auth/success', correctOrigin);
    successUrl.searchParams.set('token', encodeURIComponent(accessToken));
    successUrl.searchParams.set('expires', expiredAt.toString());

    console.log('Redirecting to success page with tokens:', {
      correctOrigin,
      successUrl: successUrl.toString()
    });

    return NextResponse.redirect(successUrl);
  } catch (error) {
    console.error('Callback handler error:', error);
    const correctOrigin = getCorrectOrigin(request);
    return NextResponse.redirect(
      new URL('/auth/error?error=internal_error', correctOrigin)
    );
  }
}
