"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

interface ThemeProviderProps {
  children: React.ReactNode
  attribute?: string | string[]
  defaultTheme?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
  [key: string]: any // Allow any additional props
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // @ts-ignore - Ignore type checking for next-themes props compatibility
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
