"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-2.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Volume2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 9a5 5 0 0 1 0 6\",\n            key: \"1q6k2b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.364 18.364a9 9 0 0 0 0-12.728\",\n            key: \"ijwkga\"\n        }\n    ]\n];\nconst Volume2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-2\", __iconNode);\n //# sourceMappingURL=volume-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdm9sdW1lLTIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUFBO0tBRVQ7SUFDQTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBdUI7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ3BEO1FBQUMsQ0FBUTtRQUFBLENBQUU7WUFBQSxFQUFHLG9DQUFxQztZQUFBLElBQUs7UUFBVTtLQUFBO0NBQ3BFO0FBYU0sY0FBVSxrRUFBaUIsYUFBWSxDQUFVIiwic291cmNlcyI6WyJFOlxcc3JjXFxpY29uc1xcdm9sdW1lLTIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdNMTEgNC43MDJhLjcwNS43MDUgMCAwIDAtMS4yMDMtLjQ5OEw2LjQxMyA3LjU4N0ExLjQgMS40IDAgMCAxIDUuNDE2IDhIM2ExIDEgMCAwIDAtMSAxdjZhMSAxIDAgMCAwIDEgMWgyLjQxNmExLjQgMS40IDAgMCAxIC45OTcuNDEzbDMuMzgzIDMuMzg0QS43MDUuNzA1IDAgMCAwIDExIDE5LjI5OHonLFxuICAgICAga2V5OiAndXFqOXV3JyxcbiAgICB9LFxuICBdLFxuICBbJ3BhdGgnLCB7IGQ6ICdNMTYgOWE1IDUgMCAwIDEgMCA2Jywga2V5OiAnMXE2azJiJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTE5LjM2NCAxOC4zNjRhOSA5IDAgMCAwIDAtMTIuNzI4Jywga2V5OiAnaWp3a2dhJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBWb2x1bWUyXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVEVnTkM0M01ESmhMamN3TlM0M01EVWdNQ0F3SURBdE1TNHlNRE10TGpRNU9FdzJMalF4TXlBM0xqVTROMEV4TGpRZ01TNDBJREFnTUNBeElEVXVOREUySURoSU0yRXhJREVnTUNBd0lEQXRNU0F4ZGpaaE1TQXhJREFnTUNBd0lERWdNV2d5TGpReE5tRXhMalFnTVM0MElEQWdNQ0F4SUM0NU9UY3VOREV6YkRNdU16Z3pJRE11TXpnMFFTNDNNRFV1TnpBMUlEQWdNQ0F3SURFeElERTVMakk1T0hvaUlDOCtDaUFnUEhCaGRHZ2daRDBpVFRFMklEbGhOU0ExSURBZ01DQXhJREFnTmlJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTVRrdU16WTBJREU0TGpNMk5HRTVJRGtnTUNBd0lEQWdNQzB4TWk0M01qZ2lJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy92b2x1bWUtMlxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFZvbHVtZTIgPSBjcmVhdGVMdWNpZGVJY29uKCd2b2x1bWUtMicsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBWb2x1bWUyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-x.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ VolumeX)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"22\",\n            x2: \"16\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"1ewh16\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"22\",\n            y1: \"9\",\n            y2: \"15\",\n            key: \"5ykzw1\"\n        }\n    ]\n];\nconst VolumeX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"volume-x\", __iconNode);\n //# sourceMappingURL=volume-x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./reset-chat-modal */ \"(app-pages-browser)/./src/components/chat/reset-chat-modal.tsx\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* harmony import */ var _immersive_mode__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./immersive-mode */ \"(app-pages-browser)/./src/components/chat/immersive-mode.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 61,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_11__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, profileImageUrl, characterName } = param;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_13__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_14__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c2 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { streamingMessage, profileImageUrl, characterName } = param;\n    _s1();\n    const [thinkingText, setThinkingText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('Berpikir');\n    // Cycle through thinking messages when not streaming actual content\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamingMessage.useEffect\": ()=>{\n            if (!streamingMessage) {\n                const messages = [\n                    'Berpikir',\n                    'Merenungkan',\n                    'Mempertimbangkan',\n                    'Memikirkan jawaban'\n                ];\n                let index = 0;\n                const interval = setInterval({\n                    \"StreamingMessage.useEffect.interval\": ()=>{\n                        index = (index + 1) % messages.length;\n                        setThinkingText(messages[index]);\n                    }\n                }[\"StreamingMessage.useEffect.interval\"], 2000);\n                return ({\n                    \"StreamingMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMessage.useEffect\"];\n            }\n        }\n    }[\"StreamingMessage.useEffect\"], [\n        streamingMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: thinkingText\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n}, \"W7FciA9Z3UpxXy1pJlVC9GvQ8tw=\"));\n_c3 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount, profileImageUrl } = param;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                src: profileImageUrl,\n                                alt: character.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Mode Cerita\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages, profileImageUrl } = param;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                        src: profileImageUrl,\n                        alt: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s2((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s2();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    // Detect mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInput.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInput.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInput.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInput.useEffect\"];\n        }\n    }[\"ChatInput.useEffect\"], []);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    const handleEmojiSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleEmojiSelect]\": (emoji)=>{\n            const newValue = newMessage + emoji;\n            setNewMessage(newValue);\n            messageRef.current = newValue;\n            // Focus back to input after emoji selection\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatInput.useCallback[handleEmojiSelect]\"], [\n        newMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 \".concat(isMobile ? 'p-3 pb-6' : 'p-4'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"\".concat(isMobile ? 'pr-20' : 'pr-24', \" rounded-2xl border-2 focus:border-bestieku-primary transition-colors \").concat(isMobile ? 'py-3 text-base' : 'py-3'),\n                                style: isMobile ? {\n                                    fontSize: '16px'\n                                } : {}\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_9__.EmojiPicker, {\n                                        onEmojiSelect: handleEmojiSelect,\n                                        disabled: disabled\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 \".concat(isMobile ? 'px-4 py-3' : 'px-6 py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, undefined),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 384,\n        columnNumber: 5\n    }, undefined);\n}, \"0TgdyP+W7Ya7Fi9I41+kS8az6FU=\"));\n_c6 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s3((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c7 = _s3((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5;\n    _s3();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileImageUrl, setProfileImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isImmersiveMode, setIsImmersiveMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show reset confirmation modal\n    const handleResetClick = ()=>{\n        setShowResetModal(true);\n    };\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Close modal\n            setShowResetModal(false);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                        // Get profile image from assets (prefer static image over video)\n                        const profileAssets = assets.filter({\n                            \"ChatInterface.useEffect.loadCharacterData.profileAssets\": (asset)=>asset.purpose === 'profile' && asset.type === 'image' && asset.isPublished\n                        }[\"ChatInterface.useEffect.loadCharacterData.profileAssets\"]);\n                        if (profileAssets.length > 0) {\n                            setProfileImageUrl(profileAssets[0].url);\n                        } else {\n                            // Fallback to character.image if no profile assets\n                            setProfileImageUrl(characterData.image);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Reset all streaming states when chat changes\n            setIsStreaming(false);\n            setStreamingMessage('');\n            setSending(false);\n            // Cleanup any existing connections and timeouts\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n                streamConnectionRef.current = null;\n            }\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n                streamingTimeoutRef.current = null;\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n                streamTimeoutRef.current = null;\n            }\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const currentChatId = chat.id; // Capture current chat ID\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Show AI thinking for 3-9 seconds before sending to backend\n                setIsStreaming(true);\n                setStreamingMessage('');\n                const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms\n                console.log(\"AI thinking for \".concat(thinkingDelay, \"ms before processing...\"));\n                await new Promise({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (resolve)=>setTimeout(resolve, thinkingDelay)\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Check if chat hasn't changed during the thinking delay\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed during thinking delay, aborting message send');\n                    return;\n                }\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Check again if chat hasn't changed after API call\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed after message send, aborting streaming');\n                    return;\n                }\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming(currentChatId);\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Only show error and remove message if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                    alert('Failed to send message. Please try again.');\n                }\n            } finally{\n                // Only reset sending state if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setSending(false);\n                }\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async (expectedChatId)=>{\n        const targetChatId = expectedChatId || chat.id;\n        console.log('Starting stream for chat:', targetChatId);\n        // Check if chat hasn't changed before starting stream\n        if (expectedChatId && chat.id !== expectedChatId) {\n            console.log('Chat changed before streaming started, aborting');\n            return;\n        }\n        // isStreaming already set to true in handleSendMessage\n        // setStreamingMessage(''); // Keep current thinking state\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            // Check if chat hasn't changed before updating\n            if (expectedChatId && chat.id !== expectedChatId) {\n                return;\n            }\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    // Check again if chat hasn't changed before the delayed update\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        setStreamingMessage(message);\n                        lastUpdateTime = Date.now();\n                    }\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            // Check if chat hasn't changed during streaming\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed during streaming, ignoring SSE event');\n                return;\n            }\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message only if chat hasn't changed\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        var _data_data;\n                        const finalMessage = {\n                            id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                            role: 'assistant',\n                            content: currentStreamingMessage,\n                            contentType: 'text',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                finalMessage\n                            ]);\n                        setStreamingMessage('');\n                        setIsStreaming(false);\n                    }\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                setStreamingMessage('');\n            }\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error, streaming was actually active, and chat hasn't changed\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming && (!expectedChatId || chat.id === expectedChatId)) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Check one more time if chat hasn't changed before creating connection\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed before creating stream connection, aborting');\n                return;\n            }\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(targetChatId, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active, connection exists, and chat hasn't changed\n                if (isStreaming && streamConnectionRef.current && (!expectedChatId || chat.id === expectedChatId)) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                alert('Failed to establish connection for AI response. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 904,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 901,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 910,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 900,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full \".concat(isMobile ? 'h-16 p-3' : 'h-20 p-4'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 945,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setIsImmersiveMode(!isImmersiveMode),\n                                className: \"text-bestieku-primary hover:text-bestieku-primary-dark hover:bg-bestieku-primary/10 flex items-center gap-2\",\n                                title: isImmersiveMode ? \"Exit Immersive Mode\" : \"Enter Immersive Mode\",\n                                children: [\n                                    isImmersiveMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 959,\n                                        columnNumber: 32\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 959,\n                                        columnNumber: 68\n                                    }, undefined),\n                                    !isMobile && (isImmersiveMode ? \"Exit\" : \"Immersive\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 11\n                            }, undefined),\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleResetClick,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 928,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    profileImageUrl: profileImageUrl,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 1024,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                profileImageUrl: profileImageUrl,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 1034,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1022,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1043,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 991,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1047,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_immersive_mode__WEBPACK_IMPORTED_MODULE_10__.ImmersiveMode, {\n                isOpen: isImmersiveMode,\n                onClose: ()=>setIsImmersiveMode(false),\n                character: character,\n                messages: messages,\n                streamingMessage: streamingMessage,\n                isStreaming: isStreaming,\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterAssets: characterAssets,\n                profileImageUrl: profileImageUrl\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1055,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__.ResetChatModal, {\n                isOpen: showResetModal,\n                onClose: ()=>setShowResetModal(false),\n                onConfirm: handleDeleteMessages,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || ((_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name),\n                messageCount: messages.length,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1069,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 926,\n        columnNumber: 5\n    }, undefined);\n}, \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\")), \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\");\n_c8 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"StreamingMessage\");\n$RefreshReg$(_c4, \"WelcomeMessage\");\n$RefreshReg$(_c5, \"OpeningScene\");\n$RefreshReg$(_c6, \"ChatInput\");\n$RefreshReg$(_c7, \"ChatInterface$memo\");\n$RefreshReg$(_c8, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/chat/immersive-mode.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/immersive-mode.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImmersiveMode: () => (/* binding */ ImmersiveMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ImmersiveMode auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImmersiveMode(param) {\n    let { isOpen, onClose, character, messages, streamingMessage, isStreaming, onSendMessage, disabled, characterAssets, profileImageUrl } = param;\n    var _backgroundAssets_;\n    _s();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticles, setShowParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Get profile assets for character display\n    const profileAssets = characterAssets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    // Get background assets for ambient backgrounds\n    const backgroundAssets = characterAssets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    // Cycle through character images every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (profileAssets.length <= 1) return;\n            const interval = setInterval({\n                \"ImmersiveMode.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"ImmersiveMode.useEffect.interval\": (prev)=>(prev + 1) % profileAssets.length\n                    }[\"ImmersiveMode.useEffect.interval\"]);\n                }\n            }[\"ImmersiveMode.useEffect.interval\"], 10000);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>clearInterval(interval)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        profileAssets.length\n    ]);\n    // Focus input when opening\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isOpen) {\n                setTimeout({\n                    \"ImmersiveMode.useEffect\": ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                    }\n                }[\"ImmersiveMode.useEffect\"], 100);\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen\n    ]);\n    // Handle keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (!isOpen) return;\n            const handleKeyDown = {\n                \"ImmersiveMode.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape') {\n                        onClose();\n                    }\n                }\n            }[\"ImmersiveMode.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    const handleSendMessage = ()=>{\n        const message = newMessage.trim();\n        if (!message || disabled) return;\n        onSendMessage(message);\n        setNewMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleEmojiSelect = (emoji)=>{\n        var _inputRef_current;\n        setNewMessage((prev)=>prev + emoji);\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    if (!isOpen) return null;\n    const currentProfileAsset = profileAssets[currentImageIndex];\n    const currentProfileImage = (currentProfileAsset === null || currentProfileAsset === void 0 ? void 0 : currentProfileAsset.url) || profileImageUrl || (character === null || character === void 0 ? void 0 : character.image);\n    const currentBackground = (_backgroundAssets_ = backgroundAssets[0]) === null || _backgroundAssets_ === void 0 ? void 0 : _backgroundAssets_.url;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20\",\n                style: {\n                    backgroundImage: currentBackground ? \"linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)), url(\".concat(currentBackground, \")\") : undefined,\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            showParticles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: Array.from({\n                    length: 20\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-1 h-1 bg-white/20 rounded-full animate-pulse\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                            animationDuration: \"\".concat(2 + Math.random() * 3, \"s\")\n                        }\n                    }, i, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 right-4 flex justify-between items-center z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-white text-xl font-bold\",\n                                children: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 text-sm\",\n                                children: \"Theater Mode\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                className: \"text-white hover:bg-white/10\",\n                                title: isAudioEnabled ? \"Disable Audio\" : \"Enable Audio\",\n                                children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 65\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setShowParticles(!showParticles),\n                                className: \"text-white hover:bg-white/10\",\n                                title: showParticles ? \"Hide Effects\" : \"Show Effects\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"text-white hover:bg-white/10\",\n                                title: \"Exit Immersive Mode\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full pt-16 pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center justify-center p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-80 h-80 rounded-full overflow-hidden border-4 border-white/20 shadow-2xl\",\n                                    children: [\n                                        currentProfileImage && /\\.mp4(\\?|$)/i.test(currentProfileImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: currentProfileImage,\n                                            className: \"w-full h-full object-cover\",\n                                            autoPlay: true,\n                                            muted: true,\n                                            loop: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentProfileImage,\n                                            alt: character === null || character === void 0 ? void 0 : character.name,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm\",\n                                        children: isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Typing...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Online\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 bg-black/40 backdrop-blur-md border-l border-white/10 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    messages.slice(-10).map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[80%] rounded-2xl px-4 py-3 \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white' : 'bg-white/10 backdrop-blur-sm text-white border border-white/20'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: message.content\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, message.id, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)),\n                                    isStreaming && streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[80%] rounded-2xl px-4 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: streamingMessage\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block w-2 h-4 bg-white/60 animate-pulse ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    ref: inputRef,\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Message \".concat((character === null || character === void 0 ? void 0 : character.name) || 'character', \"...\"),\n                                                    disabled: disabled,\n                                                    className: \"bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-white/60 rounded-2xl pr-12\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_4__.EmojiPicker, {\n                                                        onEmojiSelect: handleEmojiSelect,\n                                                        disabled: disabled\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleSendMessage,\n                                            disabled: !newMessage.trim() || disabled,\n                                            className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(ImmersiveMode, \"GGJiErsWqUgrWeVe4DpJYZaB75U=\");\n_c = ImmersiveMode;\nvar _c;\n$RefreshReg$(_c, \"ImmersiveMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/immersive-mode.tsx\n"));

/***/ })

});