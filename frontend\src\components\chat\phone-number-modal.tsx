'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { PhoneInput } from '@/components/ui/phone-input';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';
import { Phone, MessageCircle } from 'lucide-react';

interface PhoneNumberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  characterName: string;
}

export function PhoneNumberModal({ isOpen, onClose, onSuccess, characterName }: PhoneNumberModalProps) {
  const { user, updateProfile } = useAuth();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phoneNumber.trim()) {
      toast.error("Nomor telepon wajib diisi", {
        description: "Silakan masukkan nomor telepon Anda untuk melanjutkan ke WhatsApp.",
        duration: 4000,
      });
      return;
    }

    setIsLoading(true);

    try {
      await updateProfile({
        name: user?.name || '',
        phoneNumber: phoneNumber,
        dateOfBirth: user?.dateOfBirth,
        gender: user?.gender,
        about: user?.about,
      });

      toast.success("Nomor telepon berhasil disimpan!", {
        description: "Sekarang Anda akan diarahkan ke WhatsApp.",
        duration: 3000,
      });

      onSuccess();
      onClose();
    } catch (error) {
      toast.error("Gagal menyimpan nomor telepon", {
        description: error instanceof Error ? error.message : "Terjadi kesalahan saat menyimpan nomor telepon.",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5 text-green-600" />
            Nomor Telepon Diperlukan
          </DialogTitle>
          <DialogDescription>
            Untuk chat dengan <span className="font-medium">{characterName}</span> di WhatsApp, 
            AI perlu mengenali Anda melalui nomor telepon yang terdaftar.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="phoneNumber" className="block text-sm font-medium mb-2">
              Nomor Telepon *
            </label>
            <PhoneInput
              value={phoneNumber}
              onChange={setPhoneNumber}
              placeholder="Masukkan nomor telepon"
              disabled={isLoading}
              className="h-12"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Nomor ini akan disimpan di profil Anda dan digunakan untuk identifikasi di WhatsApp
            </p>
          </div>

          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !phoneNumber.trim()}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Lanjut ke WhatsApp
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
