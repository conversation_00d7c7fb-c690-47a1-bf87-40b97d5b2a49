"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,Maximize2,Minimize2,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./reset-chat-modal */ \"(app-pages-browser)/./src/components/chat/reset-chat-modal.tsx\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 61,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, profileImageUrl, characterName } = param;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c2 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { streamingMessage, profileImageUrl, characterName } = param;\n    _s1();\n    const [thinkingText, setThinkingText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('Berpikir');\n    // Cycle through thinking messages when not streaming actual content\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamingMessage.useEffect\": ()=>{\n            if (!streamingMessage) {\n                const messages = [\n                    'Berpikir',\n                    'Merenungkan',\n                    'Mempertimbangkan',\n                    'Memikirkan jawaban'\n                ];\n                let index = 0;\n                const interval = setInterval({\n                    \"StreamingMessage.useEffect.interval\": ()=>{\n                        index = (index + 1) % messages.length;\n                        setThinkingText(messages[index]);\n                    }\n                }[\"StreamingMessage.useEffect.interval\"], 2000);\n                return ({\n                    \"StreamingMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMessage.useEffect\"];\n            }\n        }\n    }[\"StreamingMessage.useEffect\"], [\n        streamingMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: thinkingText\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n}, \"W7FciA9Z3UpxXy1pJlVC9GvQ8tw=\"));\n_c3 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount, profileImageUrl } = param;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                src: profileImageUrl,\n                                alt: character.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Mode Cerita\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages, profileImageUrl } = param;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                        src: profileImageUrl,\n                        alt: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s2((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s2();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    // Detect mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInput.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInput.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInput.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInput.useEffect\"];\n        }\n    }[\"ChatInput.useEffect\"], []);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    const handleEmojiSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleEmojiSelect]\": (emoji)=>{\n            const newValue = newMessage + emoji;\n            setNewMessage(newValue);\n            messageRef.current = newValue;\n            // Focus back to input after emoji selection\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatInput.useCallback[handleEmojiSelect]\"], [\n        newMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 \".concat(isMobile ? 'p-3 pb-6' : 'p-4'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"\".concat(isMobile ? 'pr-20' : 'pr-24', \" rounded-2xl border-2 focus:border-bestieku-primary transition-colors \").concat(isMobile ? 'py-3 text-base' : 'py-3'),\n                                style: isMobile ? {\n                                    fontSize: '16px'\n                                } : {}\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_9__.EmojiPicker, {\n                                        onEmojiSelect: handleEmojiSelect,\n                                        disabled: disabled\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 \".concat(isMobile ? 'px-4 py-3' : 'px-6 py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, undefined),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 384,\n        columnNumber: 5\n    }, undefined);\n}, \"0TgdyP+W7Ya7Fi9I41+kS8az6FU=\"));\n_c6 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s3((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c7 = _s3((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5;\n    _s3();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileImageUrl, setProfileImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isImmersiveMode, setIsImmersiveMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show reset confirmation modal\n    const handleResetClick = ()=>{\n        setShowResetModal(true);\n    };\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Close modal\n            setShowResetModal(false);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                        // Get profile image from assets (prefer static image over video)\n                        const profileAssets = assets.filter({\n                            \"ChatInterface.useEffect.loadCharacterData.profileAssets\": (asset)=>asset.purpose === 'profile' && asset.type === 'image' && asset.isPublished\n                        }[\"ChatInterface.useEffect.loadCharacterData.profileAssets\"]);\n                        if (profileAssets.length > 0) {\n                            setProfileImageUrl(profileAssets[0].url);\n                        } else {\n                            // Fallback to character.image if no profile assets\n                            setProfileImageUrl(characterData.image);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Reset all streaming states when chat changes\n            setIsStreaming(false);\n            setStreamingMessage('');\n            setSending(false);\n            // Cleanup any existing connections and timeouts\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n                streamConnectionRef.current = null;\n            }\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n                streamingTimeoutRef.current = null;\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n                streamTimeoutRef.current = null;\n            }\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const currentChatId = chat.id; // Capture current chat ID\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Show AI thinking for 3-9 seconds before sending to backend\n                setIsStreaming(true);\n                setStreamingMessage('');\n                const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms\n                console.log(\"AI thinking for \".concat(thinkingDelay, \"ms before processing...\"));\n                await new Promise({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (resolve)=>setTimeout(resolve, thinkingDelay)\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Check if chat hasn't changed during the thinking delay\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed during thinking delay, aborting message send');\n                    return;\n                }\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Check again if chat hasn't changed after API call\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed after message send, aborting streaming');\n                    return;\n                }\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming(currentChatId);\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Only show error and remove message if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                    alert('Failed to send message. Please try again.');\n                }\n            } finally{\n                // Only reset sending state if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setSending(false);\n                }\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async (expectedChatId)=>{\n        const targetChatId = expectedChatId || chat.id;\n        console.log('Starting stream for chat:', targetChatId);\n        // Check if chat hasn't changed before starting stream\n        if (expectedChatId && chat.id !== expectedChatId) {\n            console.log('Chat changed before streaming started, aborting');\n            return;\n        }\n        // isStreaming already set to true in handleSendMessage\n        // setStreamingMessage(''); // Keep current thinking state\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            // Check if chat hasn't changed before updating\n            if (expectedChatId && chat.id !== expectedChatId) {\n                return;\n            }\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    // Check again if chat hasn't changed before the delayed update\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        setStreamingMessage(message);\n                        lastUpdateTime = Date.now();\n                    }\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            // Check if chat hasn't changed during streaming\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed during streaming, ignoring SSE event');\n                return;\n            }\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message only if chat hasn't changed\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        var _data_data;\n                        const finalMessage = {\n                            id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                            role: 'assistant',\n                            content: currentStreamingMessage,\n                            contentType: 'text',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                finalMessage\n                            ]);\n                        setStreamingMessage('');\n                        setIsStreaming(false);\n                    }\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                setStreamingMessage('');\n            }\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error, streaming was actually active, and chat hasn't changed\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming && (!expectedChatId || chat.id === expectedChatId)) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Check one more time if chat hasn't changed before creating connection\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed before creating stream connection, aborting');\n                return;\n            }\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(targetChatId, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active, connection exists, and chat hasn't changed\n                if (isStreaming && streamConnectionRef.current && (!expectedChatId || chat.id === expectedChatId)) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                alert('Failed to establish connection for AI response. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 904,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 901,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 910,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 900,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full \".concat(isMobile ? 'h-16 p-3' : 'h-20 p-4'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 945,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setIsImmersiveMode(!isImmersiveMode),\n                                className: \"text-bestieku-primary hover:text-bestieku-primary-dark hover:bg-bestieku-primary/10 flex items-center gap-2\",\n                                title: isImmersiveMode ? \"Exit Immersive Mode\" : \"Enter Immersive Mode\",\n                                children: [\n                                    isImmersiveMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 959,\n                                        columnNumber: 32\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 959,\n                                        columnNumber: 68\n                                    }, undefined),\n                                    !isMobile && (isImmersiveMode ? \"Exit\" : \"Immersive\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 11\n                            }, undefined),\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleResetClick,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_Maximize2_Minimize2_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 928,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    profileImageUrl: profileImageUrl,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 1024,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                profileImageUrl: profileImageUrl,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 1034,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1022,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1043,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 991,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1047,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__.ResetChatModal, {\n                isOpen: showResetModal,\n                onClose: ()=>setShowResetModal(false),\n                onConfirm: handleDeleteMessages,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || ((_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name),\n                messageCount: messages.length,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1055,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 926,\n        columnNumber: 5\n    }, undefined);\n}, \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\")), \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\");\n_c8 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"StreamingMessage\");\n$RefreshReg$(_c4, \"WelcomeMessage\");\n$RefreshReg$(_c5, \"OpeningScene\");\n$RefreshReg$(_c6, \"ChatInput\");\n$RefreshReg$(_c7, \"ChatInterface$memo\");\n$RefreshReg$(_c8, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvY2hhdC1pbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU4RTtBQUdoQztBQUNVO0FBQ1I7QUFDRjtBQUVBO0FBQ3dDO0FBQ3ZDO0FBQ1Y7QUFDTTtBQUNSO0FBQ0o7QUFDcUI7QUFDUDtBQUc3Qyx3REFBd0Q7QUFDeEQsTUFBTXdCLGVBQWU7UUFBQyxFQUFFQyxHQUFHLEVBQUVDLEdBQUcsRUFBRUMsU0FBUyxFQUkxQztJQUNDLElBQUksQ0FBQ0YsS0FBSztZQUdIQztRQUZMLHFCQUNFLDhEQUFDRTtZQUFJRCxXQUFXLGdIQUFnSSxPQUFoQkEsYUFBYTtzQkFDMUlELENBQUFBLGdCQUFBQSwyQkFBQUEsY0FBQUEsSUFBS0csTUFBTSxDQUFDLGdCQUFaSCxrQ0FBQUEsWUFBZ0JJLFdBQVcsT0FBTTs7Ozs7O0lBR3hDO0lBRUEscUJBQ0UsOERBQUNDO1FBQ0NOLEtBQUtBO1FBQ0xDLEtBQUtBO1FBQ0xDLFdBQVcsd0NBQXdELE9BQWhCQSxhQUFhOzs7Ozs7QUFHdEU7S0FwQk1IO0FBbUNOLE1BQU1RLCtCQUFpQjNCLDJDQUFJQSxDQUFDO1FBQUMsRUFBRTRCLE9BQU8sRUFBRUMsSUFBSSxFQUF1QjtJQUNqRSxJQUFJQSxTQUFTLFFBQVE7UUFDbkIsaUVBQWlFO1FBQ2pFLHFCQUFPLDhEQUFDQztZQUFFUixXQUFVO3NCQUErQ007Ozs7OztJQUNyRTtJQUVBLHNDQUFzQztJQUN0QyxxQkFDRSw4REFBQ0w7UUFBSUQsV0FBVTtrQkFDYiw0RUFBQ1IscURBQWFBO1lBQ1ppQixlQUFlO2dCQUFDaEIsbURBQVNBO2FBQUM7WUFDMUJpQixZQUFZO2dCQUNWLDhCQUE4QjtnQkFDOUJGLEdBQUc7d0JBQUMsRUFBRUcsUUFBUSxFQUFFO3lDQUNkLDhEQUFDSDt3QkFBRVIsV0FBVTtrQ0FBa0NXOzs7Ozs7O2dCQUVqRCw4QkFBOEI7Z0JBQzlCQyxJQUFJO3dCQUFDLEVBQUVELFFBQVEsRUFBRTt5Q0FDZiw4REFBQ0M7d0JBQUdaLFdBQVU7a0NBQXVCVzs7Ozs7OztnQkFFdkMsMEJBQTBCO2dCQUMxQkUsUUFBUTt3QkFBQyxFQUFFRixRQUFRLEVBQUU7eUNBQ25CLDhEQUFDRTt3QkFBT2IsV0FBVTtrQ0FBOEJXOzs7Ozs7O2dCQUVsRCx3QkFBd0I7Z0JBQ3hCRyxNQUFNO3dCQUFDLEVBQUVILFFBQVEsRUFBRVgsU0FBUyxFQUFFO29CQUM1QixNQUFNZSxXQUFXLENBQUNmO29CQUNsQixJQUFJZSxVQUFVO3dCQUNaLHFCQUNFLDhEQUFDRDs0QkFBS2QsV0FBVTtzQ0FDYlc7Ozs7OztvQkFHUDtvQkFDQSxxQkFDRSw4REFBQ0c7d0JBQUtkLFdBQVU7a0NBQ2JXOzs7Ozs7Z0JBR1A7Z0JBQ0Esa0JBQWtCO2dCQUNsQkssSUFBSTt3QkFBQyxFQUFFTCxRQUFRLEVBQUU7eUNBQ2YsOERBQUNLO3dCQUFHaEIsV0FBVTtrQ0FBd0NXOzs7Ozs7O2dCQUV4RE0sSUFBSTt3QkFBQyxFQUFFTixRQUFRLEVBQUU7eUNBQ2YsOERBQUNNO3dCQUFHakIsV0FBVTtrQ0FBMkNXOzs7Ozs7O2dCQUUzRE8sSUFBSTt3QkFBQyxFQUFFUCxRQUFRLEVBQUU7eUNBQ2YsOERBQUNPO3dCQUFHbEIsV0FBVTtrQ0FBV1c7Ozs7Ozs7Z0JBRTNCLHdCQUF3QjtnQkFDeEJRLFlBQVk7d0JBQUMsRUFBRVIsUUFBUSxFQUFFO3lDQUN2Qiw4REFBQ1E7d0JBQVduQixXQUFVO2tDQUNuQlc7Ozs7Ozs7WUFHUDtzQkFFQ0w7Ozs7Ozs7Ozs7O0FBSVQ7TUE5RE1EO0FBc0VOLE1BQU1lLDRCQUFjMUMsMkNBQUlBLElBQUM7UUFBQyxFQUFFMkMsT0FBTyxFQUFFQyxlQUFlLEVBQUVDLGFBQWEsRUFBb0I7O0lBQ3JGLE1BQU1DLGFBQWEvQyxrREFBV0E7K0NBQUMsQ0FBQ2dEO1lBQzlCLElBQUk7Z0JBQ0YsT0FBT25DLHlHQUFtQkEsQ0FBQyxJQUFJb0MsS0FBS0QsYUFBYTtvQkFBRUUsV0FBVztvQkFBTUMsUUFBUXJDLGdEQUFFQTtnQkFBQztZQUNqRixFQUFFLFVBQU07Z0JBQ04sT0FBTztZQUNUO1FBQ0Y7OENBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDVTtRQUNDRCxXQUFXLHdCQUFrRixPQUExRHFCLFFBQVFkLElBQUksS0FBSyxTQUFTLGdCQUFnQjs7WUFHNUVjLFFBQVFkLElBQUksS0FBSyw2QkFDaEIsOERBQUNOO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDSDtvQkFDQ0MsS0FBS3dCO29CQUNMdkIsS0FBS3dCLGlCQUFpQjs7Ozs7Ozs7Ozs7MEJBSzVCLDhEQUFDdEI7Z0JBQ0NELFdBQVcseUNBR1IsT0FIaURxQixRQUFRZCxJQUFJLEtBQUssU0FDL0QsaURBQ0E7O2tDQUdOLDhEQUFDRjt3QkFBZUMsU0FBU2UsUUFBUWYsT0FBTzt3QkFBRUMsTUFBTWMsUUFBUWQsSUFBSTs7Ozs7O2tDQUM1RCw4REFBQ047d0JBQUlELFdBQVcsMENBQ2IsT0FEdURxQixRQUFRZCxJQUFJLEtBQUssU0FBUyxrQkFBa0I7OzBDQUVwRyw4REFBQ0M7Z0NBQUVSLFdBQVU7MENBQ1Z3QixXQUFXSCxRQUFRUSxTQUFTOzs7Ozs7NEJBRTlCUixRQUFRZCxJQUFJLEtBQUssd0JBQ2hCLDhEQUFDTjtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPN0I7TUE3Q01vQjtBQXFETixNQUFNVSxpQ0FBbUJwRCwyQ0FBSUEsS0FBQztRQUFDLEVBQUVxRCxnQkFBZ0IsRUFBRVQsZUFBZSxFQUFFQyxhQUFhLEVBQXlCOztJQUN4RyxNQUFNLENBQUNTLGNBQWNDLGdCQUFnQixHQUFHNUQscURBQWMsQ0FBQztJQUV2RCxvRUFBb0U7SUFDcEVBLHNEQUFlO3NDQUFDO1lBQ2QsSUFBSSxDQUFDMEQsa0JBQWtCO2dCQUNyQixNQUFNRyxXQUFXO29CQUFDO29CQUFZO29CQUFlO29CQUFvQjtpQkFBcUI7Z0JBQ3RGLElBQUlDLFFBQVE7Z0JBRVosTUFBTUMsV0FBV0M7MkRBQVk7d0JBQzNCRixRQUFRLENBQUNBLFFBQVEsS0FBS0QsU0FBU0ksTUFBTTt3QkFDckNMLGdCQUFnQkMsUUFBUSxDQUFDQyxNQUFNO29CQUNqQzswREFBRztnQkFFSDtrREFBTyxJQUFNSSxjQUFjSDs7WUFDN0I7UUFDRjtxQ0FBRztRQUFDTDtLQUFpQjtJQUVyQixxQkFDRSw4REFBQzlCO1FBQUlELFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0g7b0JBQ0NDLEtBQUt3QjtvQkFDTHZCLEtBQUt3QixpQkFBaUI7Ozs7Ozs7Ozs7OzBCQUkxQiw4REFBQ3RCO2dCQUFJRCxXQUFVOztvQkFDWitCLGlDQUNDLDhEQUFDMUI7d0JBQWVDLFNBQVN5Qjt3QkFBa0J4QixNQUFLOzs7OztrREFFaEQsOERBQUNOO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3dDO2dDQUFLeEMsV0FBVTswQ0FBaUNnQzs7Ozs7OzBDQUNqRCw4REFBQy9CO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7OztrREFDZiw4REFBQ0M7d0NBQUlELFdBQVU7d0NBQTBEeUMsT0FBTzs0Q0FBRUMsZ0JBQWdCO3dDQUFPOzs7Ozs7a0RBQ3pHLDhEQUFDekM7d0NBQUlELFdBQVU7d0NBQTBEeUMsT0FBTzs0Q0FBRUMsZ0JBQWdCO3dDQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSy9HLDhEQUFDekM7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ3dDO29DQUFLeEMsV0FBVTs4Q0FDYitCLG1CQUFtQixnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2xEO01BcERNRDtBQTRETixNQUFNYSwrQkFBaUJqRSwyQ0FBSUEsQ0FBQztRQUFDLEVBQUVrRSxTQUFTLEVBQUVDLFlBQVksRUFBRXZCLGVBQWUsRUFBdUI7SUFDNUYsSUFBSSxDQUFDc0IsV0FBVyxPQUFPO0lBRXZCLHFCQUNFLDhEQUFDM0M7UUFBSUQsV0FBVTtrQkFFYiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNIO2dDQUNDQyxLQUFLd0I7Z0NBQ0x2QixLQUFLNkMsVUFBVUUsSUFBSTs7Ozs7Ozs7Ozs7c0NBSXZCLDhEQUFDN0M7NEJBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs4QkFHakIsOERBQUMrQztvQkFBRy9DLFdBQVU7OEJBQTJCNEMsVUFBVUUsSUFBSTs7Ozs7O2dCQUV0REYsVUFBVUksS0FBSyxrQkFDZCw4REFBQ3hDO29CQUFFUixXQUFVOzhCQUNWNEMsVUFBVUksS0FBSzs7Ozs7OzhCQUlwQiw4REFBQy9DO29CQUFJRCxXQUFVOzt3QkFDWjRDLFVBQVVLLFNBQVMsa0JBQ2xCLDhEQUFDbEUsdURBQUtBOzRCQUFDaUIsV0FBVTtzQ0FBaUM7Ozs7OztzQ0FJcEQsOERBQUNqQix1REFBS0E7NEJBQUNtRSxTQUFROzRCQUFVbEQsV0FBVTtzQ0FDaEM0QyxVQUFVTyxNQUFNOzs7Ozs7Ozs7Ozs7OEJBSXJCLDhEQUFDbEQ7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDd0M7O2dDQUFNSztnQ0FBYTs7Ozs7OztzQ0FDcEIsOERBQUNMO3NDQUFLOzs7Ozs7c0NBQ04sOERBQUNBOzRCQUFLeEMsV0FBVTtzQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzNDO01BL0NNMkM7QUF3RE4sTUFBTVMsNkJBQWUxRSwyQ0FBSUEsQ0FBQztRQUFDLEVBQUVrRSxTQUFTLEVBQUVTLFdBQVcsRUFBRS9CLGVBQWUsRUFBcUI7SUFDdkYsSUFBSSxFQUFDc0Isc0JBQUFBLGdDQUFBQSxVQUFXVSxZQUFZLEdBQUUsT0FBTztJQUVyQyxxQkFDRSw4REFBQ3JEO1FBQUlELFdBQVcsUUFBc0MsT0FBOUJxRCxjQUFjLFNBQVM7a0JBRTdDLDRFQUFDcEQ7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDSDt3QkFDQ0MsS0FBS3dCO3dCQUNMdkIsS0FBSzZDLFVBQVVFLElBQUk7Ozs7Ozs7Ozs7OzhCQUd2Qiw4REFBQzdDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDd0M7b0NBQUt4QyxXQUFVOzhDQUF1QjRDLFVBQVVFLElBQUk7Ozs7Ozs4Q0FDckQsOERBQUNOO29DQUFLeEMsV0FBVTs4Q0FBZ0M7Ozs7Ozs7Ozs7OztzQ0FFbEQsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDUTtnQ0FBRVIsV0FBVTswQ0FDVjRDLFVBQVVVLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPckM7TUEzQk1GO0FBb0NOLE1BQU1HLDBCQUFZN0UsMkNBQUlBLEtBQUM7UUFBQyxFQUFFOEUsYUFBYSxFQUFFQyxRQUFRLEVBQUVsQyxhQUFhLEVBQUVtQyxXQUFXLEVBQWtCOztJQUM3RixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR3RGLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3VGLFVBQVVDLFlBQVksR0FBR3hGLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU15RixXQUFXdkYsNkNBQU1BLENBQW1CO0lBRTFDLDJEQUEyRDtJQUMzRCxNQUFNd0YsYUFBYXhGLDZDQUFNQSxDQUFDO0lBQzFCd0YsV0FBV0MsT0FBTyxHQUFHTjtJQUVyQixnQkFBZ0I7SUFDaEJwRixnREFBU0E7K0JBQUM7WUFDUixNQUFNMkY7bURBQWM7b0JBQ2xCSixZQUFZSyxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUNBRjtZQUNBQyxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtZQUNsQzt1Q0FBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7OEJBQUcsRUFBRTtJQUVMLE1BQU1LLG9CQUFvQjlGLGtEQUFXQTtvREFBQztZQUNwQyxNQUFNK0YsaUJBQWlCUixXQUFXQyxPQUFPLENBQUNRLElBQUk7WUFDOUMsSUFBSSxDQUFDRCxrQkFBa0JmLFVBQVU7WUFDakNELGNBQWNnQjtZQUNkWixjQUFjO1lBQ2RJLFdBQVdDLE9BQU8sR0FBRztRQUN2QjttREFBRztRQUFDUjtRQUFVRDtLQUFjO0lBRTVCLE1BQU1rQixpQkFBaUJqRyxrREFBV0E7aURBQUMsQ0FBQ2tHO1lBQ2xDLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtnQkFDcENGLEVBQUVHLGNBQWM7Z0JBQ2hCUDtZQUNGO1FBQ0Y7Z0RBQUc7UUFBQ0E7S0FBa0I7SUFFdEIsTUFBTVEsb0JBQW9CdEcsa0RBQVdBO29EQUFDLENBQUNrRztZQUNyQyxNQUFNSyxRQUFRTCxFQUFFTSxNQUFNLENBQUNELEtBQUs7WUFDNUJwQixjQUFjb0I7WUFDZGhCLFdBQVdDLE9BQU8sR0FBR2U7UUFDdkI7bURBQUcsRUFBRTtJQUVMLE1BQU1FLG9CQUFvQnpHLGtEQUFXQTtvREFBQyxDQUFDMEc7WUFDckMsTUFBTUMsV0FBV3pCLGFBQWF3QjtZQUM5QnZCLGNBQWN3QjtZQUNkcEIsV0FBV0MsT0FBTyxHQUFHbUI7WUFDckIsNENBQTRDO1lBQzVDLElBQUlyQixTQUFTRSxPQUFPLEVBQUU7Z0JBQ3BCRixTQUFTRSxPQUFPLENBQUNvQixLQUFLO1lBQ3hCO1FBQ0Y7bURBQUc7UUFBQzFCO0tBQVc7SUFFZixxQkFDRSw4REFBQzFEO1FBQ0NELFdBQVcscUdBQ1IsT0FENkc2RCxXQUFXLGFBQWE7OzBCQUd4SSw4REFBQzVEO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDbEIsdURBQUtBO2dDQUNKd0csS0FBS3ZCO2dDQUNMaUIsT0FBT3JCO2dDQUNQNEIsVUFBVVI7Z0NBQ1ZTLFlBQVlkO2dDQUNaZSxhQUFhLFdBQXdDLE9BQTdCbEUsaUJBQWlCLGFBQVk7Z0NBQ3JEa0MsVUFBVUE7Z0NBQ1Z6RCxXQUFXLEdBQXdHNkQsT0FBckdBLFdBQVcsVUFBVSxTQUFRLDBFQUN4QyxPQURnSEEsV0FBVyxtQkFBbUI7Z0NBRWpKcEIsT0FBT29CLFdBQVc7b0NBQUU2QixVQUFVO2dDQUFPLElBQUksQ0FBQzs7Ozs7OzBDQUk1Qyw4REFBQ3pGO2dDQUFJRCxXQUFVOztrREFFYiw4REFBQ0osc0RBQVdBO3dDQUNWK0YsZUFBZVQ7d0NBQ2Z6QixVQUFVQTs7Ozs7O29DQUlYQSwwQkFDQyw4REFBQ3hEO3dDQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3JCLDhEQUFDbkIseURBQU1BO3dCQUNMK0csU0FBU3JCO3dCQUNUZCxVQUFVLENBQUNFLFdBQVdjLElBQUksTUFBTWhCO3dCQUNoQ3pELFdBQVcsdUlBQ1IsT0FEK0k2RCxXQUFXLGNBQWM7a0NBRzNLLDRFQUFDN0UsOEhBQUlBOzRCQUFDZ0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLbkIsQ0FBQzZELDBCQUNBLDhEQUFDNUQ7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDd0M7a0NBQUs7Ozs7OztvQkFDTGtCLDZCQUNDLDhEQUFDbEI7d0JBQUt4QyxXQUFVO2tDQUFzQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWxFO01BekdNdUQ7QUEyR04sTUFBTXNDLDhCQUFnQm5ILElBQUFBLDJDQUFJQSxXQUFDO1FBQUMsRUFBRW9ILElBQUksRUFBRUMsTUFBTSxFQUFFQyxlQUFlLEVBQUVDLFdBQVcsRUFBRUMsa0JBQWtCLEVBQXNCO1FBNEQvRUosaUJBQ0RBLGtCQThENUJBLGtCQUF1QkEsa0JBK1hzQkEsa0JBaUhUQTs7SUExbUJ4QyxNQUFNLENBQUM1RCxVQUFVaUUsWUFBWSxHQUFHN0gsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUM4SCxTQUFTQyxXQUFXLEdBQUcvSCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNnSSxTQUFTQyxXQUFXLEdBQUdqSSwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN5RCxrQkFBa0J5RSxvQkFBb0IsR0FBR2xJLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ29GLGFBQWErQyxlQUFlLEdBQUduSSwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN1RixVQUFVQyxZQUFZLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNvSSxpQkFBaUJDLG1CQUFtQixHQUFHckksK0NBQVFBLENBQW1CLEVBQUU7SUFDM0UsTUFBTSxDQUFDc0ksb0JBQW9CQyxzQkFBc0IsR0FBR3ZJLCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUN3SSxZQUFZQyxjQUFjLEdBQUd6SSwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNzRSxXQUFXb0UsYUFBYSxHQUFHMUksK0NBQVFBLENBQU07SUFDaEQsTUFBTSxDQUFDMkksZ0JBQWdCQyxrQkFBa0IsR0FBRzVJLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ2dELGlCQUFpQjZGLG1CQUFtQixHQUFHN0ksK0NBQVFBLENBQWdCO0lBQ3RFLE1BQU0sQ0FBQzhJLGlCQUFpQkMsbUJBQW1CLEdBQUcvSSwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNZ0osaUJBQWlCOUksNkNBQU1BLENBQWlCO0lBQzlDLE1BQU0rSSxzQkFBc0IvSSw2Q0FBTUEsQ0FBK0I7SUFDakUsTUFBTWdKLHNCQUFzQmhKLDZDQUFNQSxDQUF3QjtJQUMxRCxNQUFNaUosbUJBQW1CakosNkNBQU1BLENBQXdCO0lBRXZELGdDQUFnQztJQUNoQyxNQUFNa0osbUJBQW1CO1FBQ3ZCUixrQkFBa0I7SUFDcEI7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTVMsdUJBQXVCO1FBQzNCLElBQUksQ0FBQzdCLEtBQUt2RyxFQUFFLEVBQUU7UUFFZCxJQUFJO1lBQ0Z3SCxjQUFjO1lBQ2QsTUFBTXBJLHVEQUFXQSxDQUFDaUosa0JBQWtCLENBQUM5QixLQUFLdkcsRUFBRTtZQUU1Qyx5QkFBeUI7WUFDekI0RyxZQUFZLEVBQUU7WUFFZCxjQUFjO1lBQ2RlLGtCQUFrQjtZQUVsQixxQkFBcUI7WUFDckJ4SCx5Q0FBS0EsQ0FBQ21JLE9BQU8sQ0FBQywwQkFBMEI7Z0JBQ3RDQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7WUFFQSxtQ0FBbUM7WUFDbkMsSUFBSTlCLGFBQWE7Z0JBQ2ZBO1lBQ0Y7UUFDRixFQUFFLE9BQU8rQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDdEkseUNBQUtBLENBQUNzSSxLQUFLLENBQUMsc0JBQXNCO2dCQUNoQ0YsYUFBYTtnQkFDYkMsVUFBVTtZQUNaO1FBQ0YsU0FBVTtZQUNSaEIsY0FBYztRQUNoQjtJQUNGO0lBRUEseUVBQXlFO0lBQ3pFLE1BQU1tQixvQkFBb0IxSiw2Q0FBTUEsRUFBQ3NILGtCQUFBQSxLQUFLbEQsU0FBUyxjQUFka0Qsc0NBQUFBLGdCQUFnQnFDLEtBQUs7SUFDdEQsTUFBTUMsbUJBQW1CNUosNkNBQU1BLEVBQUNzSCxtQkFBQUEsS0FBS2xELFNBQVMsY0FBZGtELHVDQUFBQSxpQkFBZ0JoRCxJQUFJO0lBRXBELGlDQUFpQztJQUNqQ3ZFLGdEQUFTQTttQ0FBQztZQUNSLE1BQU04Sjs2REFBb0I7b0JBQ3hCLElBQUk7d0JBQ0YseUJBQXlCO3dCQUN6QixNQUFNQyxnQkFBZ0IsTUFBTTFKLGlFQUFnQkEsQ0FBQzJKLGdCQUFnQixDQUFDekMsS0FBSzBDLFdBQVc7d0JBQzlFeEIsYUFBYXNCO3dCQUViLHdCQUF3Qjt3QkFDeEIsTUFBTUcsU0FBUyxNQUFNN0osaUVBQWdCQSxDQUFDOEosa0JBQWtCLENBQUM1QyxLQUFLMEMsV0FBVzt3QkFDekU3QixtQkFBbUI4Qjt3QkFFbkIsaUVBQWlFO3dCQUNqRSxNQUFNRSxnQkFBZ0JGLE9BQU9HLE1BQU07dUZBQUNDLENBQUFBLFFBQ2xDQSxNQUFNQyxPQUFPLEtBQUssYUFDbEJELE1BQU1FLElBQUksS0FBSyxXQUNmRixNQUFNRyxXQUFXOzt3QkFHbkIsSUFBSUwsY0FBY3JHLE1BQU0sR0FBRyxHQUFHOzRCQUM1QjZFLG1CQUFtQndCLGFBQWEsQ0FBQyxFQUFFLENBQUNNLEdBQUc7d0JBQ3pDLE9BQU87NEJBQ0wsbURBQW1EOzRCQUNuRDlCLG1CQUFtQm1CLGNBQWNILEtBQUs7d0JBQ3hDO29CQUNGLEVBQUUsT0FBT0gsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7b0JBQ2xEO2dCQUNGOztZQUVBSztRQUNGO2tDQUFHO1FBQUN2QyxLQUFLMEMsV0FBVztLQUFDO0lBRXJCLHdEQUF3RDtJQUN4RGpLLGdEQUFTQTttQ0FBQztZQUNSLElBQUkySCxzQkFBc0JRLGdCQUFnQnBFLE1BQU0sR0FBRyxHQUFHO2dCQUNwRCxNQUFNNEcsa0JBQWtCeEMsZ0JBQWdCeUMsSUFBSTsrREFDMUNOLENBQUFBLFFBQVNBLE1BQU10SixFQUFFLEtBQUsyRyxzQkFBc0IyQyxNQUFNQyxPQUFPLEtBQUs7O2dCQUVoRWpDLHNCQUFzQnFDLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCRCxHQUFHLEtBQUk7WUFDaEQsT0FBTztnQkFDTHBDLHNCQUFzQjtZQUN4QjtRQUNGO2tDQUFHO1FBQUNYO1FBQW9CUTtLQUFnQjtJQUV4Qyw0QkFBNEI7SUFDNUJuSSxnREFBU0E7bUNBQUM7WUFDUixNQUFNMkY7dURBQWM7b0JBQ2xCSixZQUFZSyxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUVBRjtZQUNBQyxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtZQUNsQzsyQ0FBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7a0NBQUcsRUFBRTtJQUVMLGdDQUFnQztJQUNoQzNGLGdEQUFTQTttQ0FBQztnQkFDb0J1SCxpQkFDREE7WUFEM0JvQyxrQkFBa0JqRSxPQUFPLElBQUc2QixrQkFBQUEsS0FBS2xELFNBQVMsY0FBZGtELHNDQUFBQSxnQkFBZ0JxQyxLQUFLO1lBQ2pEQyxpQkFBaUJuRSxPQUFPLElBQUc2QixtQkFBQUEsS0FBS2xELFNBQVMsY0FBZGtELHVDQUFBQSxpQkFBZ0JoRCxJQUFJO1FBQ2pEO2tDQUFHO1NBQUNnRCxtQkFBQUEsS0FBS2xELFNBQVMsY0FBZGtELHVDQUFBQSxpQkFBZ0JxQyxLQUFLO1NBQUVyQyxtQkFBQUEsS0FBS2xELFNBQVMsY0FBZGtELHVDQUFBQSxpQkFBZ0JoRCxJQUFJO0tBQUM7SUFFaER2RSxnREFBU0E7bUNBQUM7WUFDUiwrQ0FBK0M7WUFDL0NrSSxlQUFlO1lBQ2ZELG9CQUFvQjtZQUNwQkQsV0FBVztZQUVYLGdEQUFnRDtZQUNoRCxJQUFJZ0Isb0JBQW9CdEQsT0FBTyxFQUFFO2dCQUMvQnNELG9CQUFvQnRELE9BQU8sQ0FBQ21GLEtBQUs7Z0JBQ2pDN0Isb0JBQW9CdEQsT0FBTyxHQUFHO1lBQ2hDO1lBQ0EsSUFBSXVELG9CQUFvQnZELE9BQU8sRUFBRTtnQkFDL0JvRixhQUFhN0Isb0JBQW9CdkQsT0FBTztnQkFDeEN1RCxvQkFBb0J2RCxPQUFPLEdBQUc7WUFDaEM7WUFDQSxJQUFJd0QsaUJBQWlCeEQsT0FBTyxFQUFFO2dCQUM1Qm9GLGFBQWE1QixpQkFBaUJ4RCxPQUFPO2dCQUNyQ3dELGlCQUFpQnhELE9BQU8sR0FBRztZQUM3QjtZQUVBcUY7WUFFQTsyQ0FBTztvQkFDTCx1Q0FBdUM7b0JBQ3ZDLElBQUkvQixvQkFBb0J0RCxPQUFPLEVBQUU7d0JBQy9Cc0Qsb0JBQW9CdEQsT0FBTyxDQUFDbUYsS0FBSztvQkFDbkM7b0JBQ0EsNEJBQTRCO29CQUM1QixJQUFJNUIsb0JBQW9CdkQsT0FBTyxFQUFFO3dCQUMvQm9GLGFBQWE3QixvQkFBb0J2RCxPQUFPO29CQUMxQztvQkFDQSx5QkFBeUI7b0JBQ3pCLElBQUl3RCxpQkFBaUJ4RCxPQUFPLEVBQUU7d0JBQzVCb0YsYUFBYTVCLGlCQUFpQnhELE9BQU87b0JBQ3ZDO2dCQUNGOztRQUNGO2tDQUFHO1FBQUM2QixLQUFLdkcsRUFBRTtLQUFDO0lBRVosdURBQXVEO0lBQ3ZELE1BQU1nSyxvQkFBb0IvSyw2Q0FBTUE7bURBQWEsS0FBUTs7SUFDckQrSyxrQkFBa0J0RixPQUFPLEdBQUc7WUFDMUJxRDtTQUFBQSwwQkFBQUEsZUFBZXJELE9BQU8sY0FBdEJxRCw4Q0FBQUEsd0JBQXdCa0MsY0FBYyxDQUFDO1lBQUVDLFVBQVU7UUFBUztJQUM5RDtJQUVBLCtEQUErRDtJQUMvRCxNQUFNQyxtQkFBbUJsTCw2Q0FBTUEsQ0FBQzBELFNBQVNJLE1BQU07SUFDL0MvRCxnREFBU0E7bUNBQUM7WUFDUixJQUFJMkQsU0FBU0ksTUFBTSxHQUFHb0gsaUJBQWlCekYsT0FBTyxFQUFFO2dCQUM5Q3lGLGlCQUFpQnpGLE9BQU8sR0FBRy9CLFNBQVNJLE1BQU07Z0JBQzFDLE1BQU1xSCxZQUFZQzt5REFBVzs0QkFDM0JMO3lCQUFBQSw2QkFBQUEsa0JBQWtCdEYsT0FBTyxjQUF6QnNGLGlEQUFBQSxnQ0FBQUE7b0JBQ0Y7d0RBQUc7Z0JBQ0g7K0NBQU8sSUFBTUYsYUFBYU07O1lBQzVCO1lBQ0FELGlCQUFpQnpGLE9BQU8sR0FBRy9CLFNBQVNJLE1BQU07UUFDNUM7a0NBQUc7UUFBQ0osU0FBU0ksTUFBTTtLQUFDO0lBRXBCLDRFQUE0RTtJQUM1RSxNQUFNdUgsaUJBQWlCckwsNkNBQU1BLENBQUNrRjtJQUM5Qm5GLGdEQUFTQTttQ0FBQztZQUNSLElBQUltRixlQUFlLENBQUNtRyxlQUFlNUYsT0FBTyxFQUFFO2dCQUMxQyx5QkFBeUI7Z0JBQ3pCLE1BQU0wRixZQUFZQzt5REFBVzs0QkFDM0JMO3lCQUFBQSw2QkFBQUEsa0JBQWtCdEYsT0FBTyxjQUF6QnNGLGlEQUFBQSxnQ0FBQUE7b0JBQ0Y7d0RBQUc7Z0JBQ0hNLGVBQWU1RixPQUFPLEdBQUdQO2dCQUN6QjsrQ0FBTyxJQUFNMkYsYUFBYU07O1lBQzVCO1lBQ0FFLGVBQWU1RixPQUFPLEdBQUdQO1FBQzNCO2tDQUFHO1FBQUNBO0tBQVk7SUFFaEIsTUFBTTRGLGVBQWU7UUFDbkIsSUFBSTtZQUNGakQsV0FBVztZQUNYLE1BQU15RCxXQUFXLE1BQU1uTCx1REFBV0EsQ0FBQ29MLGVBQWUsQ0FBQ2pFLEtBQUt2RyxFQUFFLEVBQUU7Z0JBQUV5SyxPQUFPO1lBQUc7WUFDeEU3RCxZQUFZMkQsU0FBU0csSUFBSSxDQUFDQyxPQUFPLEtBQUssK0JBQStCO1FBQ3ZFLEVBQUUsT0FBT2xDLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUMsU0FBVTtZQUNSM0IsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNOUIsb0JBQW9COUYsa0RBQVdBO3dEQUFDLE9BQU8wTDtZQUMzQyxJQUFJLENBQUNBLFlBQVkxRixJQUFJLE1BQU02QixTQUFTO1lBRXBDLE1BQU04RCxnQkFBZ0J0RSxLQUFLdkcsRUFBRSxFQUFFLDBCQUEwQjtZQUN6RCxNQUFNOEssU0FBUyxRQUFtQixPQUFYM0ksS0FBSzRJLEdBQUc7WUFDL0IvRCxXQUFXO1lBRVgsSUFBSTtnQkFDRixxQ0FBcUM7Z0JBQ3JDLE1BQU1nRSxjQUF1QjtvQkFDM0JoTCxJQUFJOEs7b0JBQ0o5SixNQUFNO29CQUNORCxTQUFTNko7b0JBQ1RLLGFBQWE7b0JBQ2IzSSxXQUFXLElBQUlILE9BQU8rSSxXQUFXO29CQUNqQ0MsV0FBVyxJQUFJaEosT0FBTytJLFdBQVc7Z0JBQ25DO2dCQUVBdEU7b0VBQVl3RSxDQUFBQSxPQUFROytCQUFJQTs0QkFBTUo7eUJBQVk7O2dCQUUxQyw2REFBNkQ7Z0JBQzdEOUQsZUFBZTtnQkFDZkQsb0JBQW9CO2dCQUVwQixNQUFNb0UsZ0JBQWdCQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxRQUFRLE1BQU0sY0FBYztnQkFDN0U5QyxRQUFRK0MsR0FBRyxDQUFDLG1CQUFpQyxPQUFkSixlQUFjO2dCQUU3QyxNQUFNLElBQUlLO29FQUFRQyxDQUFBQSxVQUFXdEIsV0FBV3NCLFNBQVNOOztnQkFFakQseURBQXlEO2dCQUN6RCxJQUFJOUUsS0FBS3ZHLEVBQUUsS0FBSzZLLGVBQWU7b0JBQzdCbkMsUUFBUStDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRjtnQkFFQS9DLFFBQVErQyxHQUFHLENBQUMsNEJBQTRCbEYsS0FBS3ZHLEVBQUUsRUFBRTtvQkFBRThCLFNBQVM4STtvQkFBYWdCLFdBQVc7Z0JBQUs7Z0JBRXpGLHNDQUFzQztnQkFDdEMsTUFBTXJCLFdBQVcsTUFBTW5MLHVEQUFXQSxDQUFDeU0sV0FBVyxDQUFDdEYsS0FBS3ZHLEVBQUUsRUFBRTtvQkFDdEQ4QixTQUFTOEk7b0JBQ1RnQixXQUFXO2dCQUNiO2dCQUVBbEQsUUFBUStDLEdBQUcsQ0FBQyw4QkFBOEJsQjtnQkFFMUMsb0RBQW9EO2dCQUNwRCxJQUFJaEUsS0FBS3ZHLEVBQUUsS0FBSzZLLGVBQWU7b0JBQzdCbkMsUUFBUStDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRjtnQkFFQSx5REFBeUQ7Z0JBQ3pELElBQUlsQixTQUFTdkssRUFBRSxFQUFFO29CQUNmNEc7d0VBQVl3RSxDQUFBQSxPQUFRQSxLQUFLVSxHQUFHO2dGQUFDQyxDQUFBQSxNQUMzQkEsSUFBSS9MLEVBQUUsS0FBSzhLLFNBQVM7d0NBQUUsR0FBR2lCLEdBQUc7d0NBQUUvTCxJQUFJdUssU0FBU3ZLLEVBQUU7b0NBQUMsSUFBSStMOzs7Z0JBRXREO2dCQUVBLDJCQUEyQjtnQkFDM0IsTUFBTUMsZUFBZW5CO1lBRXZCLEVBQUUsT0FBT3BDLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO2dCQUN6QyxxRUFBcUU7Z0JBQ3JFLElBQUlsQyxLQUFLdkcsRUFBRSxLQUFLNkssZUFBZTtvQkFDN0JqRTt3RUFBWXdFLENBQUFBLE9BQVFBLEtBQUsvQixNQUFNO2dGQUFDMEMsQ0FBQUEsTUFBT0EsSUFBSS9MLEVBQUUsS0FBSzhLOzs7b0JBQ2xEbUIsTUFBTTtnQkFDUjtZQUNGLFNBQVU7Z0JBQ1IsMkRBQTJEO2dCQUMzRCxJQUFJMUYsS0FBS3ZHLEVBQUUsS0FBSzZLLGVBQWU7b0JBQzdCN0QsV0FBVztnQkFDYjtZQUNGO1FBQ0Y7dURBQUc7UUFBQ0Q7UUFBU1IsS0FBS3ZHLEVBQUU7S0FBQztJQUVyQixNQUFNZ00saUJBQWlCLE9BQU9FO1FBQzVCLE1BQU1DLGVBQWVELGtCQUFrQjNGLEtBQUt2RyxFQUFFO1FBQzlDMEksUUFBUStDLEdBQUcsQ0FBQyw2QkFBNkJVO1FBRXpDLHNEQUFzRDtRQUN0RCxJQUFJRCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sZ0JBQWdCO1lBQ2hEeEQsUUFBUStDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSx1REFBdUQ7UUFDdkQsMERBQTBEO1FBRTFELCtDQUErQztRQUMvQyxJQUFJekQsb0JBQW9CdEQsT0FBTyxFQUFFO1lBQy9Cc0Qsb0JBQW9CdEQsT0FBTyxDQUFDbUYsS0FBSztRQUNuQztRQUNBLElBQUk1QixvQkFBb0J2RCxPQUFPLEVBQUU7WUFDL0JvRixhQUFhN0Isb0JBQW9CdkQsT0FBTztRQUMxQztRQUNBLElBQUl3RCxpQkFBaUJ4RCxPQUFPLEVBQUU7WUFDNUJvRixhQUFhNUIsaUJBQWlCeEQsT0FBTztRQUN2QztRQUVBLElBQUkwSCwwQkFBMEI7UUFFOUIsMkRBQTJEO1FBQzNELElBQUlDLGlCQUFpQjtRQUNyQixNQUFNQyx5QkFBeUIsQ0FBQ3hLO1lBQzlCLCtDQUErQztZQUMvQyxJQUFJb0ssa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjtnQkFDaEQ7WUFDRjtZQUVBLE1BQU1uQixNQUFNNUksS0FBSzRJLEdBQUc7WUFDcEIsSUFBSTlDLG9CQUFvQnZELE9BQU8sRUFBRTtnQkFDL0JvRixhQUFhN0Isb0JBQW9CdkQsT0FBTztZQUMxQztZQUVBLGtFQUFrRTtZQUNsRSxJQUFJcUcsTUFBTXNCLGlCQUFpQixLQUFLO2dCQUM5QnBGLG9CQUFvQm5GO2dCQUNwQnVLLGlCQUFpQnRCO1lBQ25CLE9BQU87Z0JBQ0wsaUNBQWlDO2dCQUNqQzlDLG9CQUFvQnZELE9BQU8sR0FBRzJGLFdBQVc7b0JBQ3ZDLCtEQUErRDtvQkFDL0QsSUFBSSxDQUFDNkIsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjt3QkFDakRqRixvQkFBb0JuRjt3QkFDcEJ1SyxpQkFBaUJsSyxLQUFLNEksR0FBRztvQkFDM0I7Z0JBQ0YsR0FBRztZQUNMO1FBQ0Y7UUFFQSxNQUFNd0IsWUFBWSxDQUFDN0I7WUFDakJoQyxRQUFRK0MsR0FBRyxDQUFDLHVCQUF1QmY7WUFFbkMsZ0RBQWdEO1lBQ2hELElBQUl3QixrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sZ0JBQWdCO2dCQUNoRHhELFFBQVErQyxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLE9BQVFmLEtBQUs4QixLQUFLO2dCQUNoQixLQUFLO29CQUNIOUQsUUFBUStDLEdBQUcsQ0FBQztvQkFDWlcsMEJBQTBCO29CQUMxQm5GLG9CQUFvQjtvQkFDcEI7Z0JBQ0YsS0FBSztvQkFDSG1GLDJCQUEyQjFCLEtBQUtBLElBQUk7b0JBQ3BDNEIsdUJBQXVCRjtvQkFDdkI7Z0JBQ0YsS0FBSztvQkFDSDFELFFBQVErQyxHQUFHLENBQUMsb0JBQW9CZixLQUFLQSxJQUFJO29CQUN6QztnQkFDRixLQUFLO29CQUNIaEMsUUFBUStDLEdBQUcsQ0FBQyxnQ0FBZ0NXO29CQUM1QyxzQ0FBc0M7b0JBQ3RDLElBQUluRSxvQkFBb0J2RCxPQUFPLEVBQUU7d0JBQy9Cb0YsYUFBYTdCLG9CQUFvQnZELE9BQU87b0JBQzFDO29CQUNBLHVEQUF1RDtvQkFDdkQsSUFBSXdELGlCQUFpQnhELE9BQU8sRUFBRTt3QkFDNUJvRixhQUFhNUIsaUJBQWlCeEQsT0FBTztvQkFDdkM7b0JBQ0EsNkRBQTZEO29CQUM3RCxJQUFJLENBQUN3SCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sZ0JBQWdCOzRCQUUzQ3hCO3dCQUROLE1BQU0rQixlQUF3Qjs0QkFDNUJ6TSxJQUFJMEssRUFBQUEsYUFBQUEsS0FBS0EsSUFBSSxjQUFUQSxpQ0FBQUEsV0FBV2dDLGFBQWEsS0FBSSxPQUFrQixPQUFYdkssS0FBSzRJLEdBQUc7NEJBQy9DL0osTUFBTTs0QkFDTkQsU0FBU3FMOzRCQUNUbkIsYUFBYTs0QkFDYjNJLFdBQVcsSUFBSUgsT0FBTytJLFdBQVc7NEJBQ2pDQyxXQUFXLElBQUloSixPQUFPK0ksV0FBVzt3QkFDbkM7d0JBRUF0RSxZQUFZd0UsQ0FBQUEsT0FBUTttQ0FBSUE7Z0NBQU1xQjs2QkFBYTt3QkFDM0N4RixvQkFBb0I7d0JBQ3BCQyxlQUFlO29CQUNqQjtvQkFDQSxJQUFJYyxvQkFBb0J0RCxPQUFPLEVBQUU7d0JBQy9Cc0Qsb0JBQW9CdEQsT0FBTyxDQUFDbUYsS0FBSztvQkFDbkM7b0JBQ0E7Z0JBQ0Y7b0JBQ0VuQixRQUFRK0MsR0FBRyxDQUFDLHNCQUFzQmYsS0FBSzhCLEtBQUs7WUFDaEQ7UUFDRjtRQUVBLE1BQU1HLFVBQVUsT0FBT2xFO2dCQXNCaEJBO1lBckJMQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtZQUUvQixpREFBaUQ7WUFDakQsSUFBSVIsb0JBQW9CdkQsT0FBTyxFQUFFO2dCQUMvQm9GLGFBQWE3QixvQkFBb0J2RCxPQUFPO1lBQzFDO1lBQ0EsSUFBSXdELGlCQUFpQnhELE9BQU8sRUFBRTtnQkFDNUJvRixhQUFhNUIsaUJBQWlCeEQsT0FBTztZQUN2QztZQUVBLDJDQUEyQztZQUMzQyxJQUFJLENBQUN3SCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sZ0JBQWdCO2dCQUNqRGhGLGVBQWU7Z0JBQ2ZELG9CQUFvQjtZQUN0QjtZQUVBLElBQUllLG9CQUFvQnRELE9BQU8sRUFBRTtnQkFDL0JzRCxvQkFBb0J0RCxPQUFPLENBQUNtRixLQUFLO1lBQ25DO1lBRUEsMkdBQTJHO1lBQzNHLElBQUksR0FBQ3BCLGlCQUFBQSxNQUFNM0csT0FBTyxjQUFiMkcscUNBQUFBLGVBQWVtRSxRQUFRLENBQUMsZUFBY3pJLGVBQWdCLEVBQUMrSCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sY0FBYSxHQUFJO2dCQUN6R3hELFFBQVErQyxHQUFHLENBQUM7Z0JBQ1osSUFBSTtvQkFDRixNQUFNMUI7Z0JBQ1IsRUFBRSxPQUFPOEMsYUFBYTtvQkFDcEJuRSxRQUFRRCxLQUFLLENBQUMsOEJBQThCb0U7Z0JBQzVDLCtEQUErRDtnQkFDakU7WUFDRjtRQUNGO1FBRUEsSUFBSTtZQUNGLHdFQUF3RTtZQUN4RSxJQUFJWCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sZ0JBQWdCO2dCQUNoRHhELFFBQVErQyxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLCtCQUErQjtZQUMvQixNQUFNcUIsYUFBYSxNQUFNMU4sdURBQVdBLENBQUMyTixzQkFBc0IsQ0FBQ1osY0FBY0ksV0FBV0k7WUFDckYzRSxvQkFBb0J0RCxPQUFPLEdBQUdvSTtZQUU5QiwyRUFBMkU7WUFDM0U1RSxpQkFBaUJ4RCxPQUFPLEdBQUcyRixXQUFXO2dCQUNwQyxnR0FBZ0c7Z0JBQ2hHLElBQUlsRyxlQUFlNkQsb0JBQW9CdEQsT0FBTyxJQUFLLEVBQUN3SCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sY0FBYSxHQUFJO29CQUNqR3hELFFBQVErQyxHQUFHLENBQUM7b0JBQ1prQixRQUFRLElBQUlLLE1BQU07Z0JBQ3BCO1lBQ0YsR0FBRztRQUVMLEVBQUUsT0FBT3ZFLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckQsMkNBQTJDO1lBQzNDLElBQUksQ0FBQ3lELGtCQUFrQjNGLEtBQUt2RyxFQUFFLEtBQUtrTSxnQkFBZ0I7Z0JBQ2pEaEYsZUFBZTtnQkFDZitFLE1BQU07WUFDUjtRQUNGO0lBQ0Y7SUFFQSxJQUFJcEYsU0FBUztRQUNYLHFCQUNFLDhEQUFDbkc7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOzs7Ozs7MENBQ2YsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7OztrREFDZiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUlyQiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ1p3TSxNQUFNQyxJQUFJLENBQUM7d0JBQUVuSyxRQUFRO29CQUFFLEdBQUcrSSxHQUFHLENBQUMsQ0FBQ3FCLEdBQUdDLGtCQUNqQyw4REFBQzFNOzRCQUFZRCxXQUFVOzs4Q0FDckIsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7Ozs7OztzREFDZiw4REFBQ0M7NENBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7MkJBSlQyTTs7Ozs7Ozs7Ozs7Ozs7OztJQVdwQjtJQUVBLHFCQUNFLDhEQUFDMU07UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFXLDhJQUNiLE9BRDJKNkQsV0FBVyxhQUFhOztrQ0FFcEwsOERBQUM1RDt3QkFBSUQsV0FBVTs7NEJBRVo2RCxZQUFZa0Msd0JBQ1gsOERBQUNsSCx5REFBTUE7Z0NBQ0xxRSxTQUFRO2dDQUNSMEosTUFBSztnQ0FDTGhILFNBQVNHO2dDQUNUL0YsV0FBVTtnQ0FDVjZNLGNBQVc7MENBRVgsNEVBQUM1Tiw4SEFBU0E7b0NBQUNlLFdBQVU7Ozs7Ozs7Ozs7OzBDQUt6Qiw4REFBQ0M7MENBQ0MsNEVBQUM4QztvQ0FBRy9DLFdBQVU7OENBQXlCOEYsRUFBQUEsbUJBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCx1Q0FBQUEsaUJBQWdCaEQsSUFBSSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJbkUsOERBQUM3Qzt3QkFBSUQsV0FBVTs7MENBRWIsOERBQUNuQix5REFBTUE7Z0NBQ0xxRSxTQUFRO2dDQUNSMEosTUFBSztnQ0FDTGhILFNBQVMsSUFBTXlCLG1CQUFtQixDQUFDRDtnQ0FDbkNwSCxXQUFVO2dDQUNWZ0QsT0FBT29FLGtCQUFrQix3QkFBd0I7O29DQUVoREEsZ0NBQWtCLDhEQUFDL0gsOEhBQVNBO3dDQUFDVyxXQUFVOzs7OztrRUFBZSw4REFBQ1osOEhBQVNBO3dDQUFDWSxXQUFVOzs7Ozs7b0NBQzNFLENBQUM2RCxZQUFhdUQsQ0FBQUEsa0JBQWtCLFNBQVMsV0FBVTs7Ozs7Ozs0QkFJckR2RCxZQUFZbUMsaUNBQ1gsOERBQUNuSCx5REFBTUE7Z0NBQ0xxRSxTQUFRO2dDQUNSMEosTUFBSztnQ0FDTGhILFNBQVNJO2dDQUNUaEcsV0FBVTtnQ0FDVjZNLGNBQVc7MENBRVgsNEVBQUMzTiw4SEFBSUE7b0NBQUNjLFdBQVU7Ozs7Ozs7Ozs7OzBDQUtwQiw4REFBQ25CLHlEQUFNQTtnQ0FDTHFFLFNBQVE7Z0NBQ1IwSixNQUFLO2dDQUNMaEgsU0FBUzhCO2dDQUNUakUsVUFBVXFELGNBQWM1RSxTQUFTSSxNQUFNLEtBQUs7Z0NBQzVDdEMsV0FBVTs7a0RBRVYsOERBQUNiLDhIQUFTQTt3Q0FBQ2EsV0FBVTs7Ozs7O29DQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU92Qyw4REFBQ0M7Z0JBQ0NELFdBQVU7Z0JBQ1Z5QyxPQUFPO29CQUNMcUssWUFBWWxHLHFCQUNSLGdFQUFtRixPQUFuQkEsb0JBQW1CLE9BQ25GO29CQUNKbUcsZ0JBQWdCbkcscUJBQXFCLFVBQVU7b0JBQy9Db0csb0JBQW9CcEcscUJBQXFCLFdBQVc7b0JBQ3BEcUcsa0JBQWtCckcscUJBQXFCLGNBQWM7Z0JBQ3ZEOztvQkFHQ2hFLDJCQUNDLDhEQUFDRDt3QkFDQ0MsV0FBV0E7d0JBQ1hDLGNBQWNpRCxLQUFLakQsWUFBWTt3QkFDL0J2QixpQkFBaUJBOzs7Ozs7b0JBS3BCc0IsMkJBQ0MsOERBQUNRO3dCQUNDUixXQUFXQTt3QkFDWFMsYUFBYW5CLFNBQVNJLE1BQU0sR0FBRzt3QkFDL0JoQixpQkFBaUJBOzs7Ozs7b0JBS3BCWSxTQUFTSSxNQUFNLEdBQUcsbUJBQ2pCLDhEQUFDckM7d0JBQUlELFdBQVU7OzRCQUNaa0MsU0FBU21KLEdBQUcsQ0FBQyxDQUFDaEssd0JBQ2IsOERBQUNEO29DQUVDQyxTQUFTQTtvQ0FDVEMsaUJBQWlCQTtvQ0FDakJDLGVBQWU2RyxpQkFBaUJuRSxPQUFPO21DQUhsQzVDLFFBQVE5QixFQUFFOzs7Ozs0QkFRbEJtRSw2QkFDQyw4REFBQzVCO2dDQUNDQyxrQkFBa0JBO2dDQUNsQlQsaUJBQWlCQTtnQ0FDakJDLGVBQWU2RyxpQkFBaUJuRSxPQUFPOzs7Ozs7Ozs7Ozs7a0NBTS9DLDhEQUFDaEU7d0JBQUlxRixLQUFLZ0M7Ozs7Ozs7Ozs7OzswQkFJWiw4REFBQy9EO2dCQUNDQyxlQUFlZTtnQkFDZmQsVUFBVTZDLFdBQVc1QztnQkFDckJuQyxlQUFlNkcsaUJBQWlCbkUsT0FBTztnQkFDdkNQLGFBQWFBOzs7Ozs7MEJBSWYsOERBQUMvRCw2REFBY0E7Z0JBQ2J1TixRQUFRakc7Z0JBQ1JrRyxTQUFTLElBQU1qRyxrQkFBa0I7Z0JBQ2pDa0csV0FBV3pGO2dCQUNYcEcsZUFBZXFCLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0UsSUFBSSxPQUFJZ0QsbUJBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCx1Q0FBQUEsaUJBQWdCaEQsSUFBSTtnQkFDdERELGNBQWNYLFNBQVNJLE1BQU07Z0JBQzdCK0ssV0FBV3ZHOzs7Ozs7Ozs7Ozs7QUFJbkI7O0FBRUFqQixjQUFjeUgsV0FBVyxHQUFHO0FBRUgiLCJzb3VyY2VzIjpbIkU6XFxiZXNzdGlla3VcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGNoYXRcXGNoYXQtaW50ZXJmYWNlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjaywgbWVtbyB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgQ2hhdCwgTWVzc2FnZSwgU1NFRXZlbnQgfSBmcm9tICdAL3R5cGVzL2NoYXQnO1xyXG5pbXBvcnQgeyBDaGFyYWN0ZXJBc3NldCB9IGZyb20gJ0AvdHlwZXMvY2hhcmFjdGVyJztcclxuaW1wb3J0IHsgY2hhdFNlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL2NoYXQnO1xyXG5pbXBvcnQgeyBjaGFyYWN0ZXJTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9jaGFyYWN0ZXInO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnO1xyXG5cclxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xyXG5pbXBvcnQgeyBTZW5kLCBBcnJvd0xlZnQsIEluZm8sIFJvdGF0ZUNjdywgTWF4aW1pemUyLCBNaW5pbWl6ZTIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5pbXBvcnQgeyBmb3JtYXREaXN0YW5jZVRvTm93IH0gZnJvbSAnZGF0ZS1mbnMnO1xyXG5pbXBvcnQgeyBpZCB9IGZyb20gJ2RhdGUtZm5zL2xvY2FsZSc7XHJcbmltcG9ydCBSZWFjdE1hcmtkb3duIGZyb20gJ3JlYWN0LW1hcmtkb3duJztcclxuaW1wb3J0IHJlbWFya0dmbSBmcm9tICdyZW1hcmstZ2ZtJztcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xyXG5pbXBvcnQgeyBSZXNldENoYXRNb2RhbCB9IGZyb20gJy4vcmVzZXQtY2hhdC1tb2RhbCc7XHJcbmltcG9ydCB7IEVtb2ppUGlja2VyIH0gZnJvbSAnLi9lbW9qaS1waWNrZXInO1xyXG5pbXBvcnQgeyBJbW1lcnNpdmVNb2RlIH0gZnJvbSAnLi9pbW1lcnNpdmUtbW9kZSc7XHJcblxyXG4vLyBTaW1wbGUgYXZhdGFyIGNvbXBvbmVudCB0aGF0IGFsd2F5cyB1c2VzIHN0YXRpYyBpbWFnZVxyXG5jb25zdCBTaW1wbGVBdmF0YXIgPSAoeyBzcmMsIGFsdCwgY2xhc3NOYW1lIH06IHtcclxuICBzcmM6IHN0cmluZyB8IG51bGw7XHJcbiAgYWx0OiBzdHJpbmc7XHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG59KSA9PiB7XHJcbiAgaWYgKCFzcmMpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYmctYmVzdGlla3UtcHJpbWFyeS8xMCBmbGV4IHNpemUtZnVsbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIHRleHQteHMgdGV4dC1iZXN0aWVrdS1wcmltYXJ5ICR7Y2xhc3NOYW1lIHx8ICcnfWB9PlxyXG4gICAgICAgIHthbHQ/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnQyd9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8aW1nXHJcbiAgICAgIHNyYz17c3JjfVxyXG4gICAgICBhbHQ9e2FsdH1cclxuICAgICAgY2xhc3NOYW1lPXtgYXNwZWN0LXNxdWFyZSBzaXplLWZ1bGwgb2JqZWN0LWNvdmVyICR7Y2xhc3NOYW1lIHx8ICcnfWB9XHJcbiAgICAvPlxyXG4gICk7XHJcbn07XHJcblxyXG5pbnRlcmZhY2UgQ2hhdEludGVyZmFjZVByb3BzIHtcclxuICBjaGF0OiBDaGF0O1xyXG4gIG9uQmFjaz86ICgpID0+IHZvaWQ7XHJcbiAgb25Ub2dnbGVQcm9maWxlPzogKCkgPT4gdm9pZDtcclxuICBvbkNoYXRSZXNldD86ICgpID0+IHZvaWQ7XHJcbiAgc2VsZWN0ZWRCYWNrZ3JvdW5kPzogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuaW50ZXJmYWNlIE1lc3NhZ2VDb250ZW50UHJvcHMge1xyXG4gIGNvbnRlbnQ6IHN0cmluZztcclxuICByb2xlOiAndXNlcicgfCAnYXNzaXN0YW50JztcclxufVxyXG5cclxuY29uc3QgTWVzc2FnZUNvbnRlbnQgPSBtZW1vKCh7IGNvbnRlbnQsIHJvbGUgfTogTWVzc2FnZUNvbnRlbnRQcm9wcykgPT4ge1xyXG4gIGlmIChyb2xlID09PSAndXNlcicpIHtcclxuICAgIC8vIEZvciB1c2VyIG1lc3NhZ2VzLCBqdXN0IGRpc3BsYXkgYXMgcGxhaW4gdGV4dCB3aXRoIGxpbmUgYnJlYWtzXHJcbiAgICByZXR1cm4gPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB3aGl0ZXNwYWNlLXByZS13cmFwIGxlYWRpbmctcmVsYXhlZFwiPntjb250ZW50fTwvcD47XHJcbiAgfVxyXG5cclxuICAvLyBGb3IgQUkgbWVzc2FnZXMsIHJlbmRlciBhcyBNYXJrZG93blxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gbGVhZGluZy1yZWxheGVkIHByb3NlIHByb3NlLXNtIG1heC13LW5vbmUgZGFyazpwcm9zZS1pbnZlcnRcIj5cclxuICAgICAgPFJlYWN0TWFya2Rvd25cclxuICAgICAgICByZW1hcmtQbHVnaW5zPXtbcmVtYXJrR2ZtXX1cclxuICAgICAgICBjb21wb25lbnRzPXt7XHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgcGFyYWdyYXBoIHN0eWxpbmdcclxuICAgICAgICAgIHA6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItMiBsYXN0Om1iLTAgbGVhZGluZy1yZWxheGVkXCI+e2NoaWxkcmVufTwvcD5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgZW1waGFzaXMgKGl0YWxpYylcclxuICAgICAgICAgIGVtOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxlbSBjbGFzc05hbWU9XCJpdGFsaWMgdGV4dC1pbmhlcml0XCI+e2NoaWxkcmVufTwvZW0+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIHN0cm9uZyAoYm9sZClcclxuICAgICAgICAgIHN0cm9uZzogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8c3Ryb25nIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1pbmhlcml0XCI+e2NoaWxkcmVufTwvc3Ryb25nPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBjb2RlIGJsb2Nrc1xyXG4gICAgICAgICAgY29kZTogKHsgY2hpbGRyZW4sIGNsYXNzTmFtZSB9KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGlzSW5saW5lID0gIWNsYXNzTmFtZTtcclxuICAgICAgICAgICAgaWYgKGlzSW5saW5lKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cImJnLW11dGVkIHB4LTEuNSBweS0wLjUgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbW9ub1wiPlxyXG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgICAgICA8L2NvZGU+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cImJsb2NrIGJnLW11dGVkIHAtMyByb3VuZGVkLWxnIHRleHQteHMgZm9udC1tb25vIG92ZXJmbG93LXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICAgIDwvY29kZT5cclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgbGlzdHNcclxuICAgICAgICAgIHVsOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgbWItMiBzcGFjZS15LTFcIj57Y2hpbGRyZW59PC91bD5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICBvbDogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8b2wgY2xhc3NOYW1lPVwibGlzdC1kZWNpbWFsIGxpc3QtaW5zaWRlIG1iLTIgc3BhY2UteS0xXCI+e2NoaWxkcmVufTwvb2w+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgbGk6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cInRleHQtc21cIj57Y2hpbGRyZW59PC9saT5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgYmxvY2txdW90ZXNcclxuICAgICAgICAgIGJsb2NrcXVvdGU6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPGJsb2NrcXVvdGUgY2xhc3NOYW1lPVwiYm9yZGVyLWwtNCBib3JkZXItbXV0ZWQgcGwtNCBpdGFsaWMgbXktMlwiPlxyXG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgPC9ibG9ja3F1b3RlPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAge2NvbnRlbnR9XHJcbiAgICAgIDwvUmVhY3RNYXJrZG93bj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuaW50ZXJmYWNlIE1lc3NhZ2VJdGVtUHJvcHMge1xyXG4gIG1lc3NhZ2U6IE1lc3NhZ2U7XHJcbiAgcHJvZmlsZUltYWdlVXJsOiBzdHJpbmcgfCBudWxsO1xyXG4gIGNoYXJhY3Rlck5hbWU/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IE1lc3NhZ2VJdGVtID0gbWVtbygoeyBtZXNzYWdlLCBwcm9maWxlSW1hZ2VVcmwsIGNoYXJhY3Rlck5hbWUgfTogTWVzc2FnZUl0ZW1Qcm9wcykgPT4ge1xyXG4gIGNvbnN0IGZvcm1hdFRpbWUgPSB1c2VDYWxsYmFjaygoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4gZm9ybWF0RGlzdGFuY2VUb05vdyhuZXcgRGF0ZShkYXRlU3RyaW5nKSwgeyBhZGRTdWZmaXg6IHRydWUsIGxvY2FsZTogaWQgfSk7XHJcbiAgICB9IGNhdGNoIHtcclxuICAgICAgcmV0dXJuICcnO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXZcclxuICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1lbmQgZ2FwLTIgJHttZXNzYWdlLnJvbGUgPT09ICd1c2VyJyA/ICdqdXN0aWZ5LWVuZCcgOiAnanVzdGlmeS1zdGFydCd9YH1cclxuICAgID5cclxuICAgICAgey8qIENoYXJhY3RlciBBdmF0YXIgZm9yIGFzc2lzdGFudCBtZXNzYWdlcyAqL31cclxuICAgICAge21lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcgJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBtYi0xIHJlbGF0aXZlIGZsZXggc2hyaW5rLTAgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgPFNpbXBsZUF2YXRhclxyXG4gICAgICAgICAgICBzcmM9e3Byb2ZpbGVJbWFnZVVybH1cclxuICAgICAgICAgICAgYWx0PXtjaGFyYWN0ZXJOYW1lIHx8ICdDaGFyYWN0ZXInfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9e2BtYXgtdy1bNzAlXSByb3VuZGVkLTJ4bCBwLTQgc2hhZG93LXNtICR7bWVzc2FnZS5yb2xlID09PSAndXNlcidcclxuICAgICAgICAgICAgPyAnYmctYmVzdGlla3UtcHJpbWFyeSB0ZXh0LXdoaXRlIHJvdW5kZWQtYnItbWQnXHJcbiAgICAgICAgICAgIDogJ2JnLXdoaXRlIGRhcms6YmctbXV0ZWQgYm9yZGVyIHJvdW5kZWQtYmwtbWQnXHJcbiAgICAgICAgICB9YH1cclxuICAgICAgPlxyXG4gICAgICAgIDxNZXNzYWdlQ29udGVudCBjb250ZW50PXttZXNzYWdlLmNvbnRlbnR9IHJvbGU9e21lc3NhZ2Uucm9sZX0gLz5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC0yICR7bWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAndGV4dC13aGl0ZS83MCcgOiAndGV4dC1tdXRlZC1mb3JlZ3JvdW5kJ1xyXG4gICAgICAgICAgfWB9PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxyXG4gICAgICAgICAgICB7Zm9ybWF0VGltZShtZXNzYWdlLmNyZWF0ZWRBdCl9XHJcbiAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICB7bWVzc2FnZS5yb2xlID09PSAndXNlcicgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC0xIGJnLXdoaXRlLzcwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtMSBiZy13aGl0ZS83MCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuaW50ZXJmYWNlIFN0cmVhbWluZ01lc3NhZ2VQcm9wcyB7XHJcbiAgc3RyZWFtaW5nTWVzc2FnZTogc3RyaW5nO1xyXG4gIHByb2ZpbGVJbWFnZVVybDogc3RyaW5nIHwgbnVsbDtcclxuICBjaGFyYWN0ZXJOYW1lPzogc3RyaW5nO1xyXG59XHJcblxyXG5jb25zdCBTdHJlYW1pbmdNZXNzYWdlID0gbWVtbygoeyBzdHJlYW1pbmdNZXNzYWdlLCBwcm9maWxlSW1hZ2VVcmwsIGNoYXJhY3Rlck5hbWUgfTogU3RyZWFtaW5nTWVzc2FnZVByb3BzKSA9PiB7XHJcbiAgY29uc3QgW3RoaW5raW5nVGV4dCwgc2V0VGhpbmtpbmdUZXh0XSA9IFJlYWN0LnVzZVN0YXRlKCdCZXJwaWtpcicpO1xyXG5cclxuICAvLyBDeWNsZSB0aHJvdWdoIHRoaW5raW5nIG1lc3NhZ2VzIHdoZW4gbm90IHN0cmVhbWluZyBhY3R1YWwgY29udGVudFxyXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIXN0cmVhbWluZ01lc3NhZ2UpIHtcclxuICAgICAgY29uc3QgbWVzc2FnZXMgPSBbJ0JlcnBpa2lyJywgJ01lcmVudW5na2FuJywgJ01lbXBlcnRpbWJhbmdrYW4nLCAnTWVtaWtpcmthbiBqYXdhYmFuJ107XHJcbiAgICAgIGxldCBpbmRleCA9IDA7XHJcblxyXG4gICAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgICBpbmRleCA9IChpbmRleCArIDEpICUgbWVzc2FnZXMubGVuZ3RoO1xyXG4gICAgICAgIHNldFRoaW5raW5nVGV4dChtZXNzYWdlc1tpbmRleF0pO1xyXG4gICAgICB9LCAyMDAwKTtcclxuXHJcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcclxuICAgIH1cclxuICB9LCBbc3RyZWFtaW5nTWVzc2FnZV0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBnYXAtMiBqdXN0aWZ5LXN0YXJ0XCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBtYi0xIHJlbGF0aXZlIGZsZXggc2hyaW5rLTAgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgIDxTaW1wbGVBdmF0YXJcclxuICAgICAgICAgIHNyYz17cHJvZmlsZUltYWdlVXJsfVxyXG4gICAgICAgICAgYWx0PXtjaGFyYWN0ZXJOYW1lIHx8ICdDaGFyYWN0ZXInfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1bNzAlXSByb3VuZGVkLTJ4bCByb3VuZGVkLWJsLW1kIHAtNCBiZy13aGl0ZSBkYXJrOmJnLW11dGVkIGJvcmRlciBzaGFkb3ctc21cIj5cclxuICAgICAgICB7c3RyZWFtaW5nTWVzc2FnZSA/IChcclxuICAgICAgICAgIDxNZXNzYWdlQ29udGVudCBjb250ZW50PXtzdHJlYW1pbmdNZXNzYWdlfSByb2xlPVwiYXNzaXN0YW50XCIgLz5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPnt0aGlua2luZ1RleHR9PC9zcGFuPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIG1sLTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmVzdGlla3UtcHJpbWFyeSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiAvPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1iZXN0aWVrdS1wcmltYXJ5IHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMC4xcycgfX0gLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmVzdGlla3UtcHJpbWFyeSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzAuMnMnIH19IC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0yIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTEgYmctYmVzdGlla3UtcHJpbWFyeSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XHJcbiAgICAgICAgICAgICAge3N0cmVhbWluZ01lc3NhZ2UgPyAnTWVuZ2V0aWsuLi4nIDogJ1NlZGFuZyBtZW1pa2lya2FuIHJlc3BvbnMuLi4nfVxyXG4gICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbmludGVyZmFjZSBXZWxjb21lTWVzc2FnZVByb3BzIHtcclxuICBjaGFyYWN0ZXI6IGFueTtcclxuICBtZXNzYWdlQ291bnQ6IG51bWJlcjtcclxuICBwcm9maWxlSW1hZ2VVcmw6IHN0cmluZyB8IG51bGw7XHJcbn1cclxuXHJcbmNvbnN0IFdlbGNvbWVNZXNzYWdlID0gbWVtbygoeyBjaGFyYWN0ZXIsIG1lc3NhZ2VDb3VudCwgcHJvZmlsZUltYWdlVXJsIH06IFdlbGNvbWVNZXNzYWdlUHJvcHMpID0+IHtcclxuICBpZiAoIWNoYXJhY3RlcikgcmV0dXJuIG51bGw7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LTggcHgtNCBzcGFjZS15LTZcIj5cclxuICAgICAgey8qIENoYXJhY3RlciBQcm9maWxlIFNlY3Rpb24gKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLTRcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCBoLTIwIG14LWF1dG8gcmluZy00IHJpbmctYmVzdGlla3UtcHJpbWFyeS8yMCByZWxhdGl2ZSBmbGV4IHNocmluay0wIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgPFNpbXBsZUF2YXRhclxyXG4gICAgICAgICAgICAgIHNyYz17cHJvZmlsZUltYWdlVXJsfVxyXG4gICAgICAgICAgICAgIGFsdD17Y2hhcmFjdGVyLm5hbWV9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIHsvKiBPbmxpbmUgaW5kaWNhdG9yICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMSByaWdodC0xLzIgdHJhbnNmb3JtIHRyYW5zbGF0ZS14LTYgdy00IGgtNCBiZy1ncmVlbi01MDAgYm9yZGVyLTIgYm9yZGVyLWJhY2tncm91bmQgcm91bmRlZC1mdWxsXCI+PC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItMlwiPntjaGFyYWN0ZXIubmFtZX08L2gyPlxyXG5cclxuICAgICAgICB7Y2hhcmFjdGVyLnRpdGxlICYmIChcclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0zIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgIHtjaGFyYWN0ZXIudGl0bGV9XHJcbiAgICAgICAgICA8L3A+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiBtYi00XCI+XHJcbiAgICAgICAgICB7Y2hhcmFjdGVyLnN0b3J5TW9kZSAmJiAoXHJcbiAgICAgICAgICAgIDxCYWRnZSBjbGFzc05hbWU9XCJiZy1iZXN0aWVrdS1wcmltYXJ5IHRleHQtd2hpdGVcIj5cclxuICAgICAgICAgICAgICBNb2RlIENlcml0YVxyXG4gICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cImNhcGl0YWxpemVcIj5cclxuICAgICAgICAgICAge2NoYXJhY3Rlci5zdGF0dXN9XHJcbiAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC00IHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICA8c3Bhbj57bWVzc2FnZUNvdW50fSBwZXNhbjwvc3Bhbj5cclxuICAgICAgICAgIDxzcGFuPuKAojwvc3Bhbj5cclxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwXCI+T25saW5lPC9zcGFuPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcblxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG4vLyBLb21wb25lbiB0ZXJwaXNhaCB1bnR1ayBPcGVuaW5nIFNjZW5lIHlhbmcgc2VsYWx1IG11bmN1bFxyXG5pbnRlcmZhY2UgT3BlbmluZ1NjZW5lUHJvcHMge1xyXG4gIGNoYXJhY3RlcjogYW55O1xyXG4gIGhhc01lc3NhZ2VzOiBib29sZWFuO1xyXG4gIHByb2ZpbGVJbWFnZVVybDogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuY29uc3QgT3BlbmluZ1NjZW5lID0gbWVtbygoeyBjaGFyYWN0ZXIsIGhhc01lc3NhZ2VzLCBwcm9maWxlSW1hZ2VVcmwgfTogT3BlbmluZ1NjZW5lUHJvcHMpID0+IHtcclxuICBpZiAoIWNoYXJhY3Rlcj8ub3BlbmluZ1NjZW5lKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcHgtNCAke2hhc01lc3NhZ2VzID8gJ3BiLTQnIDogJ3B0LTQnfWB9PlxyXG4gICAgICB7LyogT3BlbmluZyBzY2VuZSBhcyBjaGFyYWN0ZXIgbWVzc2FnZSAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtMyBtYXgtdy00eGxcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggZmxleC1zaHJpbmstMCByZWxhdGl2ZSBmbGV4IHNocmluay0wIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgIDxTaW1wbGVBdmF0YXJcclxuICAgICAgICAgICAgc3JjPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICAgIGFsdD17Y2hhcmFjdGVyLm5hbWV9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMVwiPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e2NoYXJhY3Rlci5uYW1lfTwvc3Bhbj5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BZGVnYW4gUGVtYnVrYTwvc3Bhbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLW11dGVkIHJvdW5kZWQtMnhsIHJvdW5kZWQtdGwtbWQgcC00IHNoYWRvdy1zbSBib3JkZXJcIj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWRcIj5cclxuICAgICAgICAgICAgICB7Y2hhcmFjdGVyLm9wZW5pbmdTY2VuZX1cclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5pbnRlcmZhY2UgQ2hhdElucHV0UHJvcHMge1xyXG4gIG9uU2VuZE1lc3NhZ2U6IChtZXNzYWdlOiBzdHJpbmcpID0+IHZvaWQ7XHJcbiAgZGlzYWJsZWQ6IGJvb2xlYW47XHJcbiAgY2hhcmFjdGVyTmFtZT86IHN0cmluZztcclxuICBpc1N0cmVhbWluZzogYm9vbGVhbjtcclxufVxyXG5cclxuY29uc3QgQ2hhdElucHV0ID0gbWVtbygoeyBvblNlbmRNZXNzYWdlLCBkaXNhYmxlZCwgY2hhcmFjdGVyTmFtZSwgaXNTdHJlYW1pbmcgfTogQ2hhdElucHV0UHJvcHMpID0+IHtcclxuICBjb25zdCBbbmV3TWVzc2FnZSwgc2V0TmV3TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2lzTW9iaWxlLCBzZXRJc01vYmlsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgaW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbCk7XHJcblxyXG4gIC8vIFVzZSByZWYgdG8gc3RvcmUgY3VycmVudCBtZXNzYWdlIHRvIGF2b2lkIHN0YWxlIGNsb3N1cmVzXHJcbiAgY29uc3QgbWVzc2FnZVJlZiA9IHVzZVJlZignJyk7XHJcbiAgbWVzc2FnZVJlZi5jdXJyZW50ID0gbmV3TWVzc2FnZTtcclxuXHJcbiAgLy8gRGV0ZWN0IG1vYmlsZVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjaGVja01vYmlsZSA9ICgpID0+IHtcclxuICAgICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCA3NjgpO1xyXG4gICAgfTtcclxuICAgIGNoZWNrTW9iaWxlKCk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGNvbnN0IGN1cnJlbnRNZXNzYWdlID0gbWVzc2FnZVJlZi5jdXJyZW50LnRyaW0oKTtcclxuICAgIGlmICghY3VycmVudE1lc3NhZ2UgfHwgZGlzYWJsZWQpIHJldHVybjtcclxuICAgIG9uU2VuZE1lc3NhZ2UoY3VycmVudE1lc3NhZ2UpO1xyXG4gICAgc2V0TmV3TWVzc2FnZSgnJyk7XHJcbiAgICBtZXNzYWdlUmVmLmN1cnJlbnQgPSAnJztcclxuICB9LCBbZGlzYWJsZWQsIG9uU2VuZE1lc3NhZ2VdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xyXG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmICFlLnNoaWZ0S2V5KSB7XHJcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKTtcclxuICAgIH1cclxuICB9LCBbaGFuZGxlU2VuZE1lc3NhZ2VdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHZhbHVlID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICBzZXROZXdNZXNzYWdlKHZhbHVlKTtcclxuICAgIG1lc3NhZ2VSZWYuY3VycmVudCA9IHZhbHVlO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRW1vamlTZWxlY3QgPSB1c2VDYWxsYmFjaygoZW1vamk6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgbmV3VmFsdWUgPSBuZXdNZXNzYWdlICsgZW1vamk7XHJcbiAgICBzZXROZXdNZXNzYWdlKG5ld1ZhbHVlKTtcclxuICAgIG1lc3NhZ2VSZWYuY3VycmVudCA9IG5ld1ZhbHVlO1xyXG4gICAgLy8gRm9jdXMgYmFjayB0byBpbnB1dCBhZnRlciBlbW9qaSBzZWxlY3Rpb25cclxuICAgIGlmIChpbnB1dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGlucHV0UmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgIH1cclxuICB9LCBbbmV3TWVzc2FnZV0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e2BmbGV4LXNocmluay0wIGJvcmRlci10IGJnLWJhY2tncm91bmQvOTUgYmFja2Ryb3AtYmx1ciBzdXBwb3J0cy1bYmFja2Ryb3AtZmlsdGVyXTpiZy1iYWNrZ3JvdW5kLzYwICR7aXNNb2JpbGUgPyAncC0zIHBiLTYnIDogJ3AtNCdcclxuICAgICAgICB9YH1cclxuICAgID5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBzcGFjZS14LTJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSByZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgIHJlZj17aW5wdXRSZWZ9XHJcbiAgICAgICAgICAgIHZhbHVlPXtuZXdNZXNzYWdlfVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XHJcbiAgICAgICAgICAgIG9uS2V5UHJlc3M9e2hhbmRsZUtleVByZXNzfVxyXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj17YE1lc3NhZ2UgJHtjaGFyYWN0ZXJOYW1lIHx8ICdjaGFyYWN0ZXInfS4uLmB9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtpc01vYmlsZSA/ICdwci0yMCcgOiAncHItMjQnfSByb3VuZGVkLTJ4bCBib3JkZXItMiBmb2N1czpib3JkZXItYmVzdGlla3UtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9ycyAke2lzTW9iaWxlID8gJ3B5LTMgdGV4dC1iYXNlJyA6ICdweS0zJ1xyXG4gICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICBzdHlsZT17aXNNb2JpbGUgPyB7IGZvbnRTaXplOiAnMTZweCcgfSA6IHt9fVxyXG4gICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICB7LyogSW5wdXQgYWN0aW9ucyBjb250YWluZXIgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTIgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICB7LyogRW1vamkgUGlja2VyICovfVxyXG4gICAgICAgICAgICA8RW1vamlQaWNrZXJcclxuICAgICAgICAgICAgICBvbkVtb2ppU2VsZWN0PXtoYW5kbGVFbW9qaVNlbGVjdH1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XHJcbiAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICB7LyogTG9hZGluZyBpbmRpY2F0b3IgKi99XHJcbiAgICAgICAgICAgIHtkaXNhYmxlZCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IGJvcmRlci0yIGJvcmRlci1iZXN0aWVrdS1wcmltYXJ5IGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbWwtMVwiPjwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmRNZXNzYWdlfVxyXG4gICAgICAgICAgZGlzYWJsZWQ9eyFuZXdNZXNzYWdlLnRyaW0oKSB8fCBkaXNhYmxlZH1cclxuICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWJlc3RpZWt1LXByaW1hcnkgaG92ZXI6YmctYmVzdGlla3UtcHJpbWFyeS1kYXJrIHJvdW5kZWQtMnhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzY2FsZS0xMDUgZGlzYWJsZWQ6aG92ZXI6c2NhbGUtMTAwICR7aXNNb2JpbGUgPyAncHgtNCBweS0zJyA6ICdweC02IHB5LTMnXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBRdWljayBhY3Rpb25zIG9yIHN1Z2dlc3Rpb25zIC0gSGlkZSBvbiBtb2JpbGUgdG8gc2F2ZSBzcGFjZSAqL31cclxuICAgICAgeyFpc01vYmlsZSAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbXQtMiB0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgPHNwYW4+VGVrYW4gRW50ZXIgdW50dWsgbWVuZ2lyaW0sIFNoaWZ0K0VudGVyIHVudHVrIGJhcmlzIGJhcnU8L3NwYW4+XHJcbiAgICAgICAgICB7aXNTdHJlYW1pbmcgJiYgKFxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJlc3RpZWt1LXByaW1hcnkgYW5pbWF0ZS1wdWxzZVwiPkFJIHNlZGFuZyBtZXJlc3BvbnMuLi48L3NwYW4+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5jb25zdCBDaGF0SW50ZXJmYWNlID0gbWVtbygoeyBjaGF0LCBvbkJhY2ssIG9uVG9nZ2xlUHJvZmlsZSwgb25DaGF0UmVzZXQsIHNlbGVjdGVkQmFja2dyb3VuZCB9OiBDaGF0SW50ZXJmYWNlUHJvcHMpID0+IHtcclxuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE1lc3NhZ2VbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtzZW5kaW5nLCBzZXRTZW5kaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc3RyZWFtaW5nTWVzc2FnZSwgc2V0U3RyZWFtaW5nTWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2lzU3RyZWFtaW5nLCBzZXRJc1N0cmVhbWluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTW9iaWxlLCBzZXRJc01vYmlsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2NoYXJhY3RlckFzc2V0cywgc2V0Q2hhcmFjdGVyQXNzZXRzXSA9IHVzZVN0YXRlPENoYXJhY3RlckFzc2V0W10+KFtdKTtcclxuICBjb25zdCBbYmFja2dyb3VuZEFzc2V0VXJsLCBzZXRCYWNrZ3JvdW5kQXNzZXRVcmxdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2lzRGVsZXRpbmcsIHNldElzRGVsZXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjaGFyYWN0ZXIsIHNldENoYXJhY3Rlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IFtzaG93UmVzZXRNb2RhbCwgc2V0U2hvd1Jlc2V0TW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwcm9maWxlSW1hZ2VVcmwsIHNldFByb2ZpbGVJbWFnZVVybF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaXNJbW1lcnNpdmVNb2RlLCBzZXRJc0ltbWVyc2l2ZU1vZGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IG1lc3NhZ2VzRW5kUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBzdHJlYW1Db25uZWN0aW9uUmVmID0gdXNlUmVmPHsgY2xvc2U6ICgpID0+IHZvaWQgfSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHN0cmVhbWluZ1RpbWVvdXRSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBzdHJlYW1UaW1lb3V0UmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIFNob3cgcmVzZXQgY29uZmlybWF0aW9uIG1vZGFsXHJcbiAgY29uc3QgaGFuZGxlUmVzZXRDbGljayA9ICgpID0+IHtcclxuICAgIHNldFNob3dSZXNldE1vZGFsKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIC8vIERlbGV0ZSBjaGF0IG1lc3NhZ2VzIGZ1bmN0aW9uXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlTWVzc2FnZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIWNoYXQuaWQpIHJldHVybjtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0RlbGV0aW5nKHRydWUpO1xyXG4gICAgICBhd2FpdCBjaGF0U2VydmljZS5kZWxldGVDaGF0TWVzc2FnZXMoY2hhdC5pZCk7XHJcblxyXG4gICAgICAvLyBDbGVhciBtZXNzYWdlcyBsb2NhbGx5XHJcbiAgICAgIHNldE1lc3NhZ2VzKFtdKTtcclxuXHJcbiAgICAgIC8vIENsb3NlIG1vZGFsXHJcbiAgICAgIHNldFNob3dSZXNldE1vZGFsKGZhbHNlKTtcclxuXHJcbiAgICAgIC8vIFNob3cgc3VjY2VzcyB0b2FzdFxyXG4gICAgICB0b2FzdC5zdWNjZXNzKFwiQ2hhdCBiZXJoYXNpbCBkaXJlc2V0IVwiLCB7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiU2VtdWEgcGVzYW4gdGVsYWggZGloYXB1cy4gQW5kYSBiaXNhIG1lbXVsYWkgcGVyY2FrYXBhbiBiYXJ1LlwiLFxyXG4gICAgICAgIGR1cmF0aW9uOiA0MDAwLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIENhbGwgcGFyZW50IGNhbGxiYWNrIGlmIHByb3ZpZGVkXHJcbiAgICAgIGlmIChvbkNoYXRSZXNldCkge1xyXG4gICAgICAgIG9uQ2hhdFJlc2V0KCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgbWVzc2FnZXM6JywgZXJyb3IpO1xyXG4gICAgICB0b2FzdC5lcnJvcihcIkdhZ2FsIG1lcmVzZXQgY2hhdFwiLCB7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiVGVyamFkaSBrZXNhbGFoYW4gc2FhdCBtZW5naGFwdXMgcGVzYW4uIFNpbGFrYW4gY29iYSBsYWdpLlwiLFxyXG4gICAgICAgIGR1cmF0aW9uOiA0MDAwLFxyXG4gICAgICB9KTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzRGVsZXRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIFN0YWJsZSByZWZlcmVuY2VzIGZvciBjaGFyYWN0ZXIgZGF0YSB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlLXJlbmRlcnNcclxuICBjb25zdCBjaGFyYWN0ZXJJbWFnZVJlZiA9IHVzZVJlZihjaGF0LmNoYXJhY3Rlcj8uaW1hZ2UpO1xyXG4gIGNvbnN0IGNoYXJhY3Rlck5hbWVSZWYgPSB1c2VSZWYoY2hhdC5jaGFyYWN0ZXI/Lm5hbWUpO1xyXG5cclxuICAvLyBMb2FkIGNoYXJhY3RlciBkYXRhIGFuZCBhc3NldHNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgbG9hZENoYXJhY3RlckRhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gTG9hZCBjaGFyYWN0ZXIgZGV0YWlsc1xyXG4gICAgICAgIGNvbnN0IGNoYXJhY3RlckRhdGEgPSBhd2FpdCBjaGFyYWN0ZXJTZXJ2aWNlLmdldENoYXJhY3RlckJ5SWQoY2hhdC5jaGFyYWN0ZXJJZCk7XHJcbiAgICAgICAgc2V0Q2hhcmFjdGVyKGNoYXJhY3RlckRhdGEpO1xyXG5cclxuICAgICAgICAvLyBMb2FkIGNoYXJhY3RlciBhc3NldHNcclxuICAgICAgICBjb25zdCBhc3NldHMgPSBhd2FpdCBjaGFyYWN0ZXJTZXJ2aWNlLmdldENoYXJhY3RlckFzc2V0cyhjaGF0LmNoYXJhY3RlcklkKTtcclxuICAgICAgICBzZXRDaGFyYWN0ZXJBc3NldHMoYXNzZXRzKTtcclxuXHJcbiAgICAgICAgLy8gR2V0IHByb2ZpbGUgaW1hZ2UgZnJvbSBhc3NldHMgKHByZWZlciBzdGF0aWMgaW1hZ2Ugb3ZlciB2aWRlbylcclxuICAgICAgICBjb25zdCBwcm9maWxlQXNzZXRzID0gYXNzZXRzLmZpbHRlcihhc3NldCA9PlxyXG4gICAgICAgICAgYXNzZXQucHVycG9zZSA9PT0gJ3Byb2ZpbGUnICYmXHJcbiAgICAgICAgICBhc3NldC50eXBlID09PSAnaW1hZ2UnICYmXHJcbiAgICAgICAgICBhc3NldC5pc1B1Ymxpc2hlZFxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIGlmIChwcm9maWxlQXNzZXRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIHNldFByb2ZpbGVJbWFnZVVybChwcm9maWxlQXNzZXRzWzBdLnVybCk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIGNoYXJhY3Rlci5pbWFnZSBpZiBubyBwcm9maWxlIGFzc2V0c1xyXG4gICAgICAgICAgc2V0UHJvZmlsZUltYWdlVXJsKGNoYXJhY3RlckRhdGEuaW1hZ2UpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBjaGFyYWN0ZXIgZGF0YTonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgbG9hZENoYXJhY3RlckRhdGEoKTtcclxuICB9LCBbY2hhdC5jaGFyYWN0ZXJJZF0pO1xyXG5cclxuICAvLyBVcGRhdGUgYmFja2dyb3VuZCBVUkwgd2hlbiBzZWxlY3RlZEJhY2tncm91bmQgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRCYWNrZ3JvdW5kICYmIGNoYXJhY3RlckFzc2V0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGNvbnN0IGJhY2tncm91bmRBc3NldCA9IGNoYXJhY3RlckFzc2V0cy5maW5kKFxyXG4gICAgICAgIGFzc2V0ID0+IGFzc2V0LmlkID09PSBzZWxlY3RlZEJhY2tncm91bmQgJiYgYXNzZXQucHVycG9zZSA9PT0gJ2JhY2tncm91bmQnXHJcbiAgICAgICk7XHJcbiAgICAgIHNldEJhY2tncm91bmRBc3NldFVybChiYWNrZ3JvdW5kQXNzZXQ/LnVybCB8fCBudWxsKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEJhY2tncm91bmRBc3NldFVybChudWxsKTtcclxuICAgIH1cclxuICB9LCBbc2VsZWN0ZWRCYWNrZ3JvdW5kLCBjaGFyYWN0ZXJBc3NldHNdKTtcclxuXHJcbiAgLy8gRGV0ZWN0IG1vYmlsZSBzY3JlZW4gc2l6ZVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjaGVja01vYmlsZSA9ICgpID0+IHtcclxuICAgICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCA3NjgpO1xyXG4gICAgfTtcclxuXHJcbiAgICBjaGVja01vYmlsZSgpO1xyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrTW9iaWxlKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gVXBkYXRlIHJlZnMgd2hlbiBjaGF0IGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY2hhcmFjdGVySW1hZ2VSZWYuY3VycmVudCA9IGNoYXQuY2hhcmFjdGVyPy5pbWFnZTtcclxuICAgIGNoYXJhY3Rlck5hbWVSZWYuY3VycmVudCA9IGNoYXQuY2hhcmFjdGVyPy5uYW1lO1xyXG4gIH0sIFtjaGF0LmNoYXJhY3Rlcj8uaW1hZ2UsIGNoYXQuY2hhcmFjdGVyPy5uYW1lXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBSZXNldCBhbGwgc3RyZWFtaW5nIHN0YXRlcyB3aGVuIGNoYXQgY2hhbmdlc1xyXG4gICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcbiAgICBzZXRTZW5kaW5nKGZhbHNlKTtcclxuXHJcbiAgICAvLyBDbGVhbnVwIGFueSBleGlzdGluZyBjb25uZWN0aW9ucyBhbmQgdGltZW91dHNcclxuICAgIGlmIChzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50LmNsb3NlKCk7XHJcbiAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCA9IG51bGw7XHJcbiAgICB9XHJcbiAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICBzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQgPSBudWxsO1xyXG4gICAgfVxyXG4gICAgaWYgKHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgc3RyZWFtVGltZW91dFJlZi5jdXJyZW50ID0gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICBsb2FkTWVzc2FnZXMoKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAvLyBDbGVhbnVwIHN0cmVhbSBjb25uZWN0aW9uIG9uIHVubW91bnRcclxuICAgICAgaWYgKHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIENsZWFudXAgc3RyZWFtaW5nIHRpbWVvdXRcclxuICAgICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICAgIC8vIENsZWFudXAgc3RyZWFtIHRpbWVvdXRcclxuICAgICAgaWYgKHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFtjaGF0LmlkXSk7XHJcblxyXG4gIC8vIFN0YWJsZSBzY3JvbGwgZnVuY3Rpb24gdGhhdCBkb2Vzbid0IGNhdXNlIHJlLXJlbmRlcnNcclxuICBjb25zdCBzY3JvbGxUb0JvdHRvbVJlZiA9IHVzZVJlZjwoKSA9PiB2b2lkPigoKSA9PiB7IH0pO1xyXG4gIHNjcm9sbFRvQm90dG9tUmVmLmN1cnJlbnQgPSAoKSA9PiB7XHJcbiAgICBtZXNzYWdlc0VuZFJlZi5jdXJyZW50Py5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KTtcclxuICB9O1xyXG5cclxuICAvLyBPbmx5IHNjcm9sbCB3aGVuIG1lc3NhZ2VzIGNvdW50IGNoYW5nZXMsIG5vdCBvbiBldmVyeSByZW5kZXJcclxuICBjb25zdCBtZXNzYWdlc0NvdW50UmVmID0gdXNlUmVmKG1lc3NhZ2VzLmxlbmd0aCk7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChtZXNzYWdlcy5sZW5ndGggPiBtZXNzYWdlc0NvdW50UmVmLmN1cnJlbnQpIHtcclxuICAgICAgbWVzc2FnZXNDb3VudFJlZi5jdXJyZW50ID0gbWVzc2FnZXMubGVuZ3RoO1xyXG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBzY3JvbGxUb0JvdHRvbVJlZi5jdXJyZW50Py4oKTtcclxuICAgICAgfSwgMTAwKTtcclxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gICAgfVxyXG4gICAgbWVzc2FnZXNDb3VudFJlZi5jdXJyZW50ID0gbWVzc2FnZXMubGVuZ3RoO1xyXG4gIH0sIFttZXNzYWdlcy5sZW5ndGhdKTtcclxuXHJcbiAgLy8gU2VwYXJhdGUgZWZmZWN0IGZvciBzdHJlYW1pbmcgLSBvbmx5IHNjcm9sbCB3aGVuIHN0cmVhbWluZyBzdGFydHMgb3IgZW5kc1xyXG4gIGNvbnN0IGlzU3RyZWFtaW5nUmVmID0gdXNlUmVmKGlzU3RyZWFtaW5nKTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzU3RyZWFtaW5nICYmICFpc1N0cmVhbWluZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgIC8vIFN0cmVhbWluZyBqdXN0IHN0YXJ0ZWRcclxuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgc2Nyb2xsVG9Cb3R0b21SZWYuY3VycmVudD8uKCk7XHJcbiAgICAgIH0sIDIwMCk7XHJcbiAgICAgIGlzU3RyZWFtaW5nUmVmLmN1cnJlbnQgPSBpc1N0cmVhbWluZztcclxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gICAgfVxyXG4gICAgaXNTdHJlYW1pbmdSZWYuY3VycmVudCA9IGlzU3RyZWFtaW5nO1xyXG4gIH0sIFtpc1N0cmVhbWluZ10pO1xyXG5cclxuICBjb25zdCBsb2FkTWVzc2FnZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNoYXRTZXJ2aWNlLmdldENoYXRNZXNzYWdlcyhjaGF0LmlkLCB7IGxpbWl0OiA1MCB9KTtcclxuICAgICAgc2V0TWVzc2FnZXMocmVzcG9uc2UuZGF0YS5yZXZlcnNlKCkpOyAvLyBSZXZlcnNlIHRvIHNob3cgb2xkZXN0IGZpcnN0XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBtZXNzYWdlczonLCBlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChtZXNzYWdlVGV4dDogc3RyaW5nKSA9PiB7XHJcbiAgICBpZiAoIW1lc3NhZ2VUZXh0LnRyaW0oKSB8fCBzZW5kaW5nKSByZXR1cm47XHJcblxyXG4gICAgY29uc3QgY3VycmVudENoYXRJZCA9IGNoYXQuaWQ7IC8vIENhcHR1cmUgY3VycmVudCBjaGF0IElEXHJcbiAgICBjb25zdCB0ZW1wSWQgPSBgdGVtcC0ke0RhdGUubm93KCl9YDtcclxuICAgIHNldFNlbmRpbmcodHJ1ZSk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQWRkIHVzZXIgbWVzc2FnZSB0byBVSSBpbW1lZGlhdGVseVxyXG4gICAgICBjb25zdCB1c2VyTWVzc2FnZTogTWVzc2FnZSA9IHtcclxuICAgICAgICBpZDogdGVtcElkLFxyXG4gICAgICAgIHJvbGU6ICd1c2VyJyxcclxuICAgICAgICBjb250ZW50OiBtZXNzYWdlVGV4dCxcclxuICAgICAgICBjb250ZW50VHlwZTogJ3RleHQnLFxyXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgdXNlck1lc3NhZ2VdKTtcclxuXHJcbiAgICAgIC8vIFNob3cgQUkgdGhpbmtpbmcgZm9yIDMtOSBzZWNvbmRzIGJlZm9yZSBzZW5kaW5nIHRvIGJhY2tlbmRcclxuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XHJcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UoJycpO1xyXG5cclxuICAgICAgY29uc3QgdGhpbmtpbmdEZWxheSA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDYwMDApICsgMzAwMDsgLy8gMzAwMC05MDAwbXNcclxuICAgICAgY29uc29sZS5sb2coYEFJIHRoaW5raW5nIGZvciAke3RoaW5raW5nRGVsYXl9bXMgYmVmb3JlIHByb2Nlc3NpbmcuLi5gKTtcclxuXHJcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCB0aGlua2luZ0RlbGF5KSk7XHJcblxyXG4gICAgICAvLyBDaGVjayBpZiBjaGF0IGhhc24ndCBjaGFuZ2VkIGR1cmluZyB0aGUgdGhpbmtpbmcgZGVsYXlcclxuICAgICAgaWYgKGNoYXQuaWQgIT09IGN1cnJlbnRDaGF0SWQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBjaGFuZ2VkIGR1cmluZyB0aGlua2luZyBkZWxheSwgYWJvcnRpbmcgbWVzc2FnZSBzZW5kJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnU2VuZGluZyBtZXNzYWdlIHRvIGNoYXQ6JywgY2hhdC5pZCwgeyBtZXNzYWdlOiBtZXNzYWdlVGV4dCwgc3RyZWFtaW5nOiB0cnVlIH0pO1xyXG5cclxuICAgICAgLy8gU2VuZCBtZXNzYWdlIHdpdGggc3RyZWFtaW5nIGVuYWJsZWRcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjaGF0U2VydmljZS5zZW5kTWVzc2FnZShjaGF0LmlkLCB7XHJcbiAgICAgICAgbWVzc2FnZTogbWVzc2FnZVRleHQsXHJcbiAgICAgICAgc3RyZWFtaW5nOiB0cnVlLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdNZXNzYWdlIHNlbnQgc3VjY2Vzc2Z1bGx5OicsIHJlc3BvbnNlKTtcclxuXHJcbiAgICAgIC8vIENoZWNrIGFnYWluIGlmIGNoYXQgaGFzbid0IGNoYW5nZWQgYWZ0ZXIgQVBJIGNhbGxcclxuICAgICAgaWYgKGNoYXQuaWQgIT09IGN1cnJlbnRDaGF0SWQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBjaGFuZ2VkIGFmdGVyIG1lc3NhZ2Ugc2VuZCwgYWJvcnRpbmcgc3RyZWFtaW5nJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBVcGRhdGUgdGhlIHRlbXBvcmFyeSBtZXNzYWdlIHdpdGggcmVhbCBJRCBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKHJlc3BvbnNlLmlkKSB7XHJcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cclxuICAgICAgICAgIG1zZy5pZCA9PT0gdGVtcElkID8geyAuLi5tc2csIGlkOiByZXNwb25zZS5pZCB9IDogbXNnXHJcbiAgICAgICAgKSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFN0YXJ0IHN0cmVhbWluZyByZXNwb25zZVxyXG4gICAgICBhd2FpdCBzdGFydFN0cmVhbWluZyhjdXJyZW50Q2hhdElkKTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2VuZCBtZXNzYWdlOicsIGVycm9yKTtcclxuICAgICAgLy8gT25seSBzaG93IGVycm9yIGFuZCByZW1vdmUgbWVzc2FnZSBpZiB3ZSdyZSBzdGlsbCBvbiB0aGUgc2FtZSBjaGF0XHJcbiAgICAgIGlmIChjaGF0LmlkID09PSBjdXJyZW50Q2hhdElkKSB7XHJcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2LmZpbHRlcihtc2cgPT4gbXNnLmlkICE9PSB0ZW1wSWQpKTtcclxuICAgICAgICBhbGVydCgnRmFpbGVkIHRvIHNlbmQgbWVzc2FnZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgLy8gT25seSByZXNldCBzZW5kaW5nIHN0YXRlIGlmIHdlJ3JlIHN0aWxsIG9uIHRoZSBzYW1lIGNoYXRcclxuICAgICAgaWYgKGNoYXQuaWQgPT09IGN1cnJlbnRDaGF0SWQpIHtcclxuICAgICAgICBzZXRTZW5kaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtzZW5kaW5nLCBjaGF0LmlkXSk7XHJcblxyXG4gIGNvbnN0IHN0YXJ0U3RyZWFtaW5nID0gYXN5bmMgKGV4cGVjdGVkQ2hhdElkPzogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCB0YXJnZXRDaGF0SWQgPSBleHBlY3RlZENoYXRJZCB8fCBjaGF0LmlkO1xyXG4gICAgY29uc29sZS5sb2coJ1N0YXJ0aW5nIHN0cmVhbSBmb3IgY2hhdDonLCB0YXJnZXRDaGF0SWQpO1xyXG5cclxuICAgIC8vIENoZWNrIGlmIGNoYXQgaGFzbid0IGNoYW5nZWQgYmVmb3JlIHN0YXJ0aW5nIHN0cmVhbVxyXG4gICAgaWYgKGV4cGVjdGVkQ2hhdElkICYmIGNoYXQuaWQgIT09IGV4cGVjdGVkQ2hhdElkKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdDaGF0IGNoYW5nZWQgYmVmb3JlIHN0cmVhbWluZyBzdGFydGVkLCBhYm9ydGluZycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gaXNTdHJlYW1pbmcgYWxyZWFkeSBzZXQgdG8gdHJ1ZSBpbiBoYW5kbGVTZW5kTWVzc2FnZVxyXG4gICAgLy8gc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7IC8vIEtlZXAgY3VycmVudCB0aGlua2luZyBzdGF0ZVxyXG5cclxuICAgIC8vIENsb3NlIGV4aXN0aW5nIGNvbm5lY3Rpb24gYW5kIGNsZWFyIHRpbWVvdXRzXHJcbiAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgfVxyXG4gICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgIH1cclxuICAgIGlmIChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICB9XHJcblxyXG4gICAgbGV0IGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlID0gJyc7XHJcblxyXG4gICAgLy8gTW9yZSBhZ2dyZXNzaXZlIHRocm90dGxpbmcgZm9yIHN0cmVhbWluZyBtZXNzYWdlIHVwZGF0ZXNcclxuICAgIGxldCBsYXN0VXBkYXRlVGltZSA9IDA7XHJcbiAgICBjb25zdCB1cGRhdGVTdHJlYW1pbmdNZXNzYWdlID0gKG1lc3NhZ2U6IHN0cmluZykgPT4ge1xyXG4gICAgICAvLyBDaGVjayBpZiBjaGF0IGhhc24ndCBjaGFuZ2VkIGJlZm9yZSB1cGRhdGluZ1xyXG4gICAgICBpZiAoZXhwZWN0ZWRDaGF0SWQgJiYgY2hhdC5pZCAhPT0gZXhwZWN0ZWRDaGF0SWQpIHtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gSW1tZWRpYXRlIHVwZGF0ZSBpZiBpdCdzIGJlZW4gbW9yZSB0aGFuIDEwMG1zIHNpbmNlIGxhc3QgdXBkYXRlXHJcbiAgICAgIGlmIChub3cgLSBsYXN0VXBkYXRlVGltZSA+IDEwMCkge1xyXG4gICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UobWVzc2FnZSk7XHJcbiAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBub3c7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gT3RoZXJ3aXNlLCB0aHJvdHRsZSB0aGUgdXBkYXRlXHJcbiAgICAgICAgc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAvLyBDaGVjayBhZ2FpbiBpZiBjaGF0IGhhc24ndCBjaGFuZ2VkIGJlZm9yZSB0aGUgZGVsYXllZCB1cGRhdGVcclxuICAgICAgICAgIGlmICghZXhwZWN0ZWRDaGF0SWQgfHwgY2hhdC5pZCA9PT0gZXhwZWN0ZWRDaGF0SWQpIHtcclxuICAgICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZShtZXNzYWdlKTtcclxuICAgICAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBEYXRlLm5vdygpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sIDEwMCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3Qgb25NZXNzYWdlID0gKGRhdGE6IFNTRUV2ZW50KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdSZWNlaXZlZCBTU0UgZXZlbnQ6JywgZGF0YSk7XHJcblxyXG4gICAgICAvLyBDaGVjayBpZiBjaGF0IGhhc24ndCBjaGFuZ2VkIGR1cmluZyBzdHJlYW1pbmdcclxuICAgICAgaWYgKGV4cGVjdGVkQ2hhdElkICYmIGNoYXQuaWQgIT09IGV4cGVjdGVkQ2hhdElkKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NoYXQgY2hhbmdlZCBkdXJpbmcgc3RyZWFtaW5nLCBpZ25vcmluZyBTU0UgZXZlbnQnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHN3aXRjaCAoZGF0YS5ldmVudCkge1xyXG4gICAgICAgIGNhc2UgJ3N0YXJ0JzpcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gc3RhcnRlZCcpO1xyXG4gICAgICAgICAgY3VycmVudFN0cmVhbWluZ01lc3NhZ2UgPSAnJztcclxuICAgICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UoJycpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSAndG9rZW4nOlxyXG4gICAgICAgICAgY3VycmVudFN0cmVhbWluZ01lc3NhZ2UgKz0gZGF0YS5kYXRhO1xyXG4gICAgICAgICAgdXBkYXRlU3RyZWFtaW5nTWVzc2FnZShjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlICdtZXRhZGF0YSc6XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnU3RyZWFtIG1ldGFkYXRhOicsIGRhdGEuZGF0YSk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlICdlbmQnOlxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSBlbmRlZCwgZmluYWwgbWVzc2FnZTonLCBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSk7XHJcbiAgICAgICAgICAvLyBDbGVhciBhbnkgcGVuZGluZyBzdHJlYW1pbmcgdXBkYXRlc1xyXG4gICAgICAgICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIC8vIENsZWFyIHN0cmVhbSB0aW1lb3V0IHRvIHByZXZlbnQgZmFsc2UgdGltZW91dCBlcnJvcnNcclxuICAgICAgICAgIGlmIChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICAvLyBGaW5hbGl6ZSB0aGUgc3RyZWFtaW5nIG1lc3NhZ2Ugb25seSBpZiBjaGF0IGhhc24ndCBjaGFuZ2VkXHJcbiAgICAgICAgICBpZiAoIWV4cGVjdGVkQ2hhdElkIHx8IGNoYXQuaWQgPT09IGV4cGVjdGVkQ2hhdElkKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpbmFsTWVzc2FnZTogTWVzc2FnZSA9IHtcclxuICAgICAgICAgICAgICBpZDogZGF0YS5kYXRhPy5jaGF0TWVzc2FnZUlkIHx8IGBtc2ctJHtEYXRlLm5vdygpfWAsXHJcbiAgICAgICAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXHJcbiAgICAgICAgICAgICAgY29udGVudDogY3VycmVudFN0cmVhbWluZ01lc3NhZ2UsXHJcbiAgICAgICAgICAgICAgY29udGVudFR5cGU6ICd0ZXh0JyxcclxuICAgICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGZpbmFsTWVzc2FnZV0pO1xyXG4gICAgICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKCcnKTtcclxuICAgICAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaWYgKHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQuY2xvc2UoKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnVW5rbm93biBTU0UgZXZlbnQ6JywgZGF0YS5ldmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3Qgb25FcnJvciA9IGFzeW5jIChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0cmVhbSBFcnJvcjonLCBlcnJvcik7XHJcblxyXG4gICAgICAvLyBDbGVhciB0aW1lb3V0cyBmaXJzdCB0byBwcmV2ZW50IGZ1cnRoZXIgZXJyb3JzXHJcbiAgICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE9ubHkgdXBkYXRlIHN0YXRlIGlmIGNoYXQgaGFzbid0IGNoYW5nZWRcclxuICAgICAgaWYgKCFleHBlY3RlZENoYXRJZCB8fCBjaGF0LmlkID09PSBleHBlY3RlZENoYXRJZCkge1xyXG4gICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcclxuICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKCcnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBPbmx5IHJlbG9hZCBtZXNzYWdlcyBpZiBpdCdzIG5vdCBhIHRpbWVvdXQgZXJyb3IsIHN0cmVhbWluZyB3YXMgYWN0dWFsbHkgYWN0aXZlLCBhbmQgY2hhdCBoYXNuJ3QgY2hhbmdlZFxyXG4gICAgICBpZiAoIWVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCd0aW1lb3V0JykgJiYgaXNTdHJlYW1pbmcgJiYgKCFleHBlY3RlZENoYXRJZCB8fCBjaGF0LmlkID09PSBleHBlY3RlZENoYXRJZCkpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXR0ZW1wdGluZyB0byByZWxvYWQgbWVzc2FnZXMgYXMgZmFsbGJhY2suLi4nKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgYXdhaXQgbG9hZE1lc3NhZ2VzKCk7XHJcbiAgICAgICAgfSBjYXRjaCAocmVsb2FkRXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZWxvYWQgbWVzc2FnZXM6JywgcmVsb2FkRXJyb3IpO1xyXG4gICAgICAgICAgLy8gRG9uJ3Qgc2hvdyBhbGVydCBmb3IgdGltZW91dCBlcnJvcnMgdG8gYXZvaWQgZGlzcnVwdGluZyB1c2VyXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENoZWNrIG9uZSBtb3JlIHRpbWUgaWYgY2hhdCBoYXNuJ3QgY2hhbmdlZCBiZWZvcmUgY3JlYXRpbmcgY29ubmVjdGlvblxyXG4gICAgICBpZiAoZXhwZWN0ZWRDaGF0SWQgJiYgY2hhdC5pZCAhPT0gZXhwZWN0ZWRDaGF0SWQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBjaGFuZ2VkIGJlZm9yZSBjcmVhdGluZyBzdHJlYW0gY29ubmVjdGlvbiwgYWJvcnRpbmcnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENyZWF0ZSBuZXcgc3RyZWFtIGNvbm5lY3Rpb25cclxuICAgICAgY29uc3QgY29ubmVjdGlvbiA9IGF3YWl0IGNoYXRTZXJ2aWNlLmNyZWF0ZVN0cmVhbUNvbm5lY3Rpb24odGFyZ2V0Q2hhdElkLCBvbk1lc3NhZ2UsIG9uRXJyb3IpO1xyXG4gICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQgPSBjb25uZWN0aW9uO1xyXG5cclxuICAgICAgLy8gU2V0IHRpbWVvdXQgZm9yIHN0cmVhbWluZyAoMzAgc2Vjb25kcykgLSBidXQgc3RvcmUgcmVmZXJlbmNlIHRvIGNsZWFyIGl0XHJcbiAgICAgIHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIC8vIE9ubHkgdHJpZ2dlciB0aW1lb3V0IGlmIHN0cmVhbWluZyBpcyBzdGlsbCBhY3RpdmUsIGNvbm5lY3Rpb24gZXhpc3RzLCBhbmQgY2hhdCBoYXNuJ3QgY2hhbmdlZFxyXG4gICAgICAgIGlmIChpc1N0cmVhbWluZyAmJiBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQgJiYgKCFleHBlY3RlZENoYXRJZCB8fCBjaGF0LmlkID09PSBleHBlY3RlZENoYXRJZCkpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gdGltZW91dCwgZmFsbGluZyBiYWNrIHRvIG1lc3NhZ2UgcmVsb2FkJyk7XHJcbiAgICAgICAgICBvbkVycm9yKG5ldyBFcnJvcignU3RyZWFtIHRpbWVvdXQnKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCAzMDAwMCk7XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBzdHJlYW0gY29ubmVjdGlvbjonLCBlcnJvcik7XHJcbiAgICAgIC8vIE9ubHkgdXBkYXRlIHN0YXRlIGlmIGNoYXQgaGFzbid0IGNoYW5nZWRcclxuICAgICAgaWYgKCFleHBlY3RlZENoYXRJZCB8fCBjaGF0LmlkID09PSBleHBlY3RlZENoYXRJZCkge1xyXG4gICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcclxuICAgICAgICBhbGVydCgnRmFpbGVkIHRvIGVzdGFibGlzaCBjb25uZWN0aW9uIGZvciBBSSByZXNwb25zZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBoLTIwIGJvcmRlci1iIHAtNCBhbmltYXRlLXB1bHNlIGZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1tdXRlZCByb3VuZGVkLWZ1bGxcIiAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0zMlwiIC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctbXV0ZWQgcm91bmRlZCB3LTIwXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBwLTQgc3BhY2UteS00IG92ZXJmbG93LXktYXV0b1wiPlxyXG4gICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IDMgfSkubWFwKChfLCBpKSA9PiAoXHJcbiAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMyBhbmltYXRlLXB1bHNlXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLW11dGVkIHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgc3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1tdXRlZCByb3VuZGVkIHctMy80XCIgLz5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0xLzJcIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICkpfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbCBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgey8qIENoYXQgSGVhZGVyIC0gU2ltcGxpZmllZCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LXNocmluay0wIGJvcmRlci1iIGJnLWJhY2tncm91bmQvOTUgYmFja2Ryb3AtYmx1ciBzdXBwb3J0cy1bYmFja2Ryb3AtZmlsdGVyXTpiZy1iYWNrZ3JvdW5kLzYwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB3LWZ1bGwgJHtpc01vYmlsZSA/ICdoLTE2IHAtMycgOiAnaC0yMCBwLTQnXHJcbiAgICAgICAgfWB9PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICB7LyogTW9iaWxlIGJhY2sgYnV0dG9uICovfVxyXG4gICAgICAgICAge2lzTW9iaWxlICYmIG9uQmFjayAmJiAoXHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbkJhY2t9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6YmctbXV0ZWQvNTAgbWQ6aGlkZGVuXCJcclxuICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQmFjayB0byBjaGF0IGxpc3RcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgIHsvKiBTaW1wbGUgdGl0bGUgKi99XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+e2NoYXQuY2hhcmFjdGVyPy5uYW1lIHx8ICdDaGF0J308L2gyPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgIHsvKiBJbW1lcnNpdmUgTW9kZSBUb2dnbGUgQnV0dG9uICovfVxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0ltbWVyc2l2ZU1vZGUoIWlzSW1tZXJzaXZlTW9kZSl9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmVzdGlla3UtcHJpbWFyeSBob3Zlcjp0ZXh0LWJlc3RpZWt1LXByaW1hcnktZGFyayBob3ZlcjpiZy1iZXN0aWVrdS1wcmltYXJ5LzEwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcclxuICAgICAgICAgICAgdGl0bGU9e2lzSW1tZXJzaXZlTW9kZSA/IFwiRXhpdCBJbW1lcnNpdmUgTW9kZVwiIDogXCJFbnRlciBJbW1lcnNpdmUgTW9kZVwifVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7aXNJbW1lcnNpdmVNb2RlID8gPE1pbmltaXplMiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz4gOiA8TWF4aW1pemUyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cclxuICAgICAgICAgICAgeyFpc01vYmlsZSAmJiAoaXNJbW1lcnNpdmVNb2RlID8gXCJFeGl0XCIgOiBcIkltbWVyc2l2ZVwiKX1cclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgIHsvKiBNb2JpbGUgcHJvZmlsZSB0b2dnbGUgYnV0dG9uICovfVxyXG4gICAgICAgICAge2lzTW9iaWxlICYmIG9uVG9nZ2xlUHJvZmlsZSAmJiAoXHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZVByb2ZpbGV9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6YmctbXV0ZWQvNTAgbWQ6aGlkZGVuXCJcclxuICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiVmlldyBjaGFyYWN0ZXIgcHJvZmlsZVwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8SW5mbyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgIHsvKiBSZXNldCBDaGF0IEJ1dHRvbiAqL31cclxuICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUmVzZXRDbGlja31cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRGVsZXRpbmcgfHwgbWVzc2FnZXMubGVuZ3RoID09PSAwfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtNzAwIGhvdmVyOmJnLXJlZC01MCBkYXJrOmhvdmVyOmJnLXJlZC05NTAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8Um90YXRlQ2N3IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICBSZXNldFxyXG4gICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFBlc2FuIC0gU2Nyb2xsYWJsZSAqL31cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gb3ZlcmZsb3cteC1oaWRkZW4gcmVsYXRpdmVcIlxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBiYWNrZ3JvdW5kQXNzZXRVcmxcclxuICAgICAgICAgICAgPyBgbGluZWFyLWdyYWRpZW50KHJnYmEoMCwgMCwgMCwgMC42KSwgcmdiYSgwLCAwLCAwLCAwLjgpKSwgdXJsKCR7YmFja2dyb3VuZEFzc2V0VXJsfSlgXHJcbiAgICAgICAgICAgIDogJ2xpbmVhci1ncmFkaWVudCh0byBib3R0b20sIGhzbCh2YXIoLS1iYWNrZ3JvdW5kKSksIGhzbCh2YXIoLS1tdXRlZCkpLzAuMiknLFxyXG4gICAgICAgICAgYmFja2dyb3VuZFNpemU6IGJhY2tncm91bmRBc3NldFVybCA/ICdjb3ZlcicgOiAnYXV0bycsXHJcbiAgICAgICAgICBiYWNrZ3JvdW5kUG9zaXRpb246IGJhY2tncm91bmRBc3NldFVybCA/ICdjZW50ZXInIDogJ2F1dG8nLFxyXG4gICAgICAgICAgYmFja2dyb3VuZFJlcGVhdDogYmFja2dyb3VuZEFzc2V0VXJsID8gJ25vLXJlcGVhdCcgOiAnYXV0bydcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgey8qIFdlbGNvbWUgTWVzc2FnZSB3aXRoIFByb2ZpbGUgLSBBbHdheXMgc2hvdyB3aGVuIGNoYXJhY3RlciBpcyBsb2FkZWQgKi99XHJcbiAgICAgICAge2NoYXJhY3RlciAmJiAoXHJcbiAgICAgICAgICA8V2VsY29tZU1lc3NhZ2VcclxuICAgICAgICAgICAgY2hhcmFjdGVyPXtjaGFyYWN0ZXJ9XHJcbiAgICAgICAgICAgIG1lc3NhZ2VDb3VudD17Y2hhdC5tZXNzYWdlQ291bnR9XHJcbiAgICAgICAgICAgIHByb2ZpbGVJbWFnZVVybD17cHJvZmlsZUltYWdlVXJsfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogT3BlbmluZyBTY2VuZSAtIEFsd2F5cyBzaG93IGFmdGVyIHByb2ZpbGUgKi99XHJcbiAgICAgICAge2NoYXJhY3RlciAmJiAoXHJcbiAgICAgICAgICA8T3BlbmluZ1NjZW5lXHJcbiAgICAgICAgICAgIGNoYXJhY3Rlcj17Y2hhcmFjdGVyfVxyXG4gICAgICAgICAgICBoYXNNZXNzYWdlcz17bWVzc2FnZXMubGVuZ3RoID4gMH1cclxuICAgICAgICAgICAgcHJvZmlsZUltYWdlVXJsPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBNZXNzYWdlcyAqL31cclxuICAgICAgICB7bWVzc2FnZXMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcGItNCBzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAge21lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxyXG4gICAgICAgICAgICAgIDxNZXNzYWdlSXRlbVxyXG4gICAgICAgICAgICAgICAga2V5PXttZXNzYWdlLmlkfVxyXG4gICAgICAgICAgICAgICAgbWVzc2FnZT17bWVzc2FnZX1cclxuICAgICAgICAgICAgICAgIHByb2ZpbGVJbWFnZVVybD17cHJvZmlsZUltYWdlVXJsfVxyXG4gICAgICAgICAgICAgICAgY2hhcmFjdGVyTmFtZT17Y2hhcmFjdGVyTmFtZVJlZi5jdXJyZW50fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICkpfVxyXG5cclxuICAgICAgICAgICAgey8qIFN0cmVhbWluZyBNZXNzYWdlICovfVxyXG4gICAgICAgICAgICB7aXNTdHJlYW1pbmcgJiYgKFxyXG4gICAgICAgICAgICAgIDxTdHJlYW1pbmdNZXNzYWdlXHJcbiAgICAgICAgICAgICAgICBzdHJlYW1pbmdNZXNzYWdlPXtzdHJlYW1pbmdNZXNzYWdlfVxyXG4gICAgICAgICAgICAgICAgcHJvZmlsZUltYWdlVXJsPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICAgICAgICBjaGFyYWN0ZXJOYW1lPXtjaGFyYWN0ZXJOYW1lUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIDxkaXYgcmVmPXttZXNzYWdlc0VuZFJlZn0gLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTWVzc2FnZSBJbnB1dCAtIEZpeGVkICovfVxyXG4gICAgICA8Q2hhdElucHV0XHJcbiAgICAgICAgb25TZW5kTWVzc2FnZT17aGFuZGxlU2VuZE1lc3NhZ2V9XHJcbiAgICAgICAgZGlzYWJsZWQ9e3NlbmRpbmcgfHwgaXNTdHJlYW1pbmd9XHJcbiAgICAgICAgY2hhcmFjdGVyTmFtZT17Y2hhcmFjdGVyTmFtZVJlZi5jdXJyZW50fVxyXG4gICAgICAgIGlzU3RyZWFtaW5nPXtpc1N0cmVhbWluZ31cclxuICAgICAgLz5cclxuXHJcbiAgICAgIHsvKiBSZXNldCBDaGF0IE1vZGFsICovfVxyXG4gICAgICA8UmVzZXRDaGF0TW9kYWxcclxuICAgICAgICBpc09wZW49e3Nob3dSZXNldE1vZGFsfVxyXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dSZXNldE1vZGFsKGZhbHNlKX1cclxuICAgICAgICBvbkNvbmZpcm09e2hhbmRsZURlbGV0ZU1lc3NhZ2VzfVxyXG4gICAgICAgIGNoYXJhY3Rlck5hbWU9e2NoYXJhY3Rlcj8ubmFtZSB8fCBjaGF0LmNoYXJhY3Rlcj8ubmFtZX1cclxuICAgICAgICBtZXNzYWdlQ291bnQ9e21lc3NhZ2VzLmxlbmd0aH1cclxuICAgICAgICBpc0xvYWRpbmc9e2lzRGVsZXRpbmd9XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbkNoYXRJbnRlcmZhY2UuZGlzcGxheU5hbWUgPSAnQ2hhdEludGVyZmFjZSc7XHJcblxyXG5leHBvcnQgeyBDaGF0SW50ZXJmYWNlIH07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJtZW1vIiwiY2hhdFNlcnZpY2UiLCJjaGFyYWN0ZXJTZXJ2aWNlIiwiQnV0dG9uIiwiSW5wdXQiLCJCYWRnZSIsIlNlbmQiLCJBcnJvd0xlZnQiLCJJbmZvIiwiUm90YXRlQ2N3IiwiTWF4aW1pemUyIiwiTWluaW1pemUyIiwiZm9ybWF0RGlzdGFuY2VUb05vdyIsImlkIiwiUmVhY3RNYXJrZG93biIsInJlbWFya0dmbSIsInRvYXN0IiwiUmVzZXRDaGF0TW9kYWwiLCJFbW9qaVBpY2tlciIsIlNpbXBsZUF2YXRhciIsInNyYyIsImFsdCIsImNsYXNzTmFtZSIsImRpdiIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwiaW1nIiwiTWVzc2FnZUNvbnRlbnQiLCJjb250ZW50Iiwicm9sZSIsInAiLCJyZW1hcmtQbHVnaW5zIiwiY29tcG9uZW50cyIsImNoaWxkcmVuIiwiZW0iLCJzdHJvbmciLCJjb2RlIiwiaXNJbmxpbmUiLCJ1bCIsIm9sIiwibGkiLCJibG9ja3F1b3RlIiwiTWVzc2FnZUl0ZW0iLCJtZXNzYWdlIiwicHJvZmlsZUltYWdlVXJsIiwiY2hhcmFjdGVyTmFtZSIsImZvcm1hdFRpbWUiLCJkYXRlU3RyaW5nIiwiRGF0ZSIsImFkZFN1ZmZpeCIsImxvY2FsZSIsImNyZWF0ZWRBdCIsIlN0cmVhbWluZ01lc3NhZ2UiLCJzdHJlYW1pbmdNZXNzYWdlIiwidGhpbmtpbmdUZXh0Iiwic2V0VGhpbmtpbmdUZXh0IiwibWVzc2FnZXMiLCJpbmRleCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJsZW5ndGgiLCJjbGVhckludGVydmFsIiwic3BhbiIsInN0eWxlIiwiYW5pbWF0aW9uRGVsYXkiLCJXZWxjb21lTWVzc2FnZSIsImNoYXJhY3RlciIsIm1lc3NhZ2VDb3VudCIsIm5hbWUiLCJoMiIsInRpdGxlIiwic3RvcnlNb2RlIiwidmFyaWFudCIsInN0YXR1cyIsIk9wZW5pbmdTY2VuZSIsImhhc01lc3NhZ2VzIiwib3BlbmluZ1NjZW5lIiwiQ2hhdElucHV0Iiwib25TZW5kTWVzc2FnZSIsImRpc2FibGVkIiwiaXNTdHJlYW1pbmciLCJuZXdNZXNzYWdlIiwic2V0TmV3TWVzc2FnZSIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJpbnB1dFJlZiIsIm1lc3NhZ2VSZWYiLCJjdXJyZW50IiwiY2hlY2tNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVTZW5kTWVzc2FnZSIsImN1cnJlbnRNZXNzYWdlIiwidHJpbSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVJbnB1dENoYW5nZSIsInZhbHVlIiwidGFyZ2V0IiwiaGFuZGxlRW1vamlTZWxlY3QiLCJlbW9qaSIsIm5ld1ZhbHVlIiwiZm9jdXMiLCJyZWYiLCJvbkNoYW5nZSIsIm9uS2V5UHJlc3MiLCJwbGFjZWhvbGRlciIsImZvbnRTaXplIiwib25FbW9qaVNlbGVjdCIsIm9uQ2xpY2siLCJDaGF0SW50ZXJmYWNlIiwiY2hhdCIsIm9uQmFjayIsIm9uVG9nZ2xlUHJvZmlsZSIsIm9uQ2hhdFJlc2V0Iiwic2VsZWN0ZWRCYWNrZ3JvdW5kIiwic2V0TWVzc2FnZXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNlbmRpbmciLCJzZXRTZW5kaW5nIiwic2V0U3RyZWFtaW5nTWVzc2FnZSIsInNldElzU3RyZWFtaW5nIiwiY2hhcmFjdGVyQXNzZXRzIiwic2V0Q2hhcmFjdGVyQXNzZXRzIiwiYmFja2dyb3VuZEFzc2V0VXJsIiwic2V0QmFja2dyb3VuZEFzc2V0VXJsIiwiaXNEZWxldGluZyIsInNldElzRGVsZXRpbmciLCJzZXRDaGFyYWN0ZXIiLCJzaG93UmVzZXRNb2RhbCIsInNldFNob3dSZXNldE1vZGFsIiwic2V0UHJvZmlsZUltYWdlVXJsIiwiaXNJbW1lcnNpdmVNb2RlIiwic2V0SXNJbW1lcnNpdmVNb2RlIiwibWVzc2FnZXNFbmRSZWYiLCJzdHJlYW1Db25uZWN0aW9uUmVmIiwic3RyZWFtaW5nVGltZW91dFJlZiIsInN0cmVhbVRpbWVvdXRSZWYiLCJoYW5kbGVSZXNldENsaWNrIiwiaGFuZGxlRGVsZXRlTWVzc2FnZXMiLCJkZWxldGVDaGF0TWVzc2FnZXMiLCJzdWNjZXNzIiwiZGVzY3JpcHRpb24iLCJkdXJhdGlvbiIsImVycm9yIiwiY29uc29sZSIsImNoYXJhY3RlckltYWdlUmVmIiwiaW1hZ2UiLCJjaGFyYWN0ZXJOYW1lUmVmIiwibG9hZENoYXJhY3RlckRhdGEiLCJjaGFyYWN0ZXJEYXRhIiwiZ2V0Q2hhcmFjdGVyQnlJZCIsImNoYXJhY3RlcklkIiwiYXNzZXRzIiwiZ2V0Q2hhcmFjdGVyQXNzZXRzIiwicHJvZmlsZUFzc2V0cyIsImZpbHRlciIsImFzc2V0IiwicHVycG9zZSIsInR5cGUiLCJpc1B1Ymxpc2hlZCIsInVybCIsImJhY2tncm91bmRBc3NldCIsImZpbmQiLCJjbG9zZSIsImNsZWFyVGltZW91dCIsImxvYWRNZXNzYWdlcyIsInNjcm9sbFRvQm90dG9tUmVmIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsIm1lc3NhZ2VzQ291bnRSZWYiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiaXNTdHJlYW1pbmdSZWYiLCJyZXNwb25zZSIsImdldENoYXRNZXNzYWdlcyIsImxpbWl0IiwiZGF0YSIsInJldmVyc2UiLCJtZXNzYWdlVGV4dCIsImN1cnJlbnRDaGF0SWQiLCJ0ZW1wSWQiLCJub3ciLCJ1c2VyTWVzc2FnZSIsImNvbnRlbnRUeXBlIiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJwcmV2IiwidGhpbmtpbmdEZWxheSIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImxvZyIsIlByb21pc2UiLCJyZXNvbHZlIiwic3RyZWFtaW5nIiwic2VuZE1lc3NhZ2UiLCJtYXAiLCJtc2ciLCJzdGFydFN0cmVhbWluZyIsImFsZXJ0IiwiZXhwZWN0ZWRDaGF0SWQiLCJ0YXJnZXRDaGF0SWQiLCJjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSIsImxhc3RVcGRhdGVUaW1lIiwidXBkYXRlU3RyZWFtaW5nTWVzc2FnZSIsIm9uTWVzc2FnZSIsImV2ZW50IiwiZmluYWxNZXNzYWdlIiwiY2hhdE1lc3NhZ2VJZCIsIm9uRXJyb3IiLCJpbmNsdWRlcyIsInJlbG9hZEVycm9yIiwiY29ubmVjdGlvbiIsImNyZWF0ZVN0cmVhbUNvbm5lY3Rpb24iLCJFcnJvciIsIkFycmF5IiwiZnJvbSIsIl8iLCJpIiwic2l6ZSIsImFyaWEtbGFiZWwiLCJiYWNrZ3JvdW5kIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kUG9zaXRpb24iLCJiYWNrZ3JvdW5kUmVwZWF0IiwiaXNPcGVuIiwib25DbG9zZSIsIm9uQ29uZmlybSIsImlzTG9hZGluZyIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});