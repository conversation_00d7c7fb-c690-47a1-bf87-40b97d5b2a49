"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/immersive-mode.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/immersive-mode.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImmersiveMode: () => (/* binding */ ImmersiveMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Minimize2,Send,Volume2,VolumeX!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ImmersiveMode auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ImmersiveMode(param) {\n    let { isOpen, onClose, character, messages, streamingMessage, isStreaming, onSendMessage, disabled, characterAssets, profileImageUrl } = param;\n    var _backgroundAssets_;\n    _s();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showParticles, setShowParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [characterMood, setCharacterMood] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('neutral');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Get profile assets for character display\n    const profileAssets = characterAssets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    // Get background assets for ambient backgrounds\n    const backgroundAssets = characterAssets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    // Cycle through character images every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (profileAssets.length <= 1) return;\n            const interval = setInterval({\n                \"ImmersiveMode.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"ImmersiveMode.useEffect.interval\": (prev)=>(prev + 1) % profileAssets.length\n                    }[\"ImmersiveMode.useEffect.interval\"]);\n                }\n            }[\"ImmersiveMode.useEffect.interval\"], 10000);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>clearInterval(interval)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        profileAssets.length\n    ]);\n    // Focus input when opening\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isOpen) {\n                setTimeout({\n                    \"ImmersiveMode.useEffect\": ()=>{\n                        var _inputRef_current;\n                        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n                    }\n                }[\"ImmersiveMode.useEffect\"], 100);\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen\n    ]);\n    // Update character mood based on chat state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (isStreaming) {\n                setCharacterMood('thinking');\n            } else if (messages.length > 0) {\n                const lastMessage = messages[messages.length - 1];\n                if (lastMessage.role === 'user') {\n                    setCharacterMood('happy');\n                } else {\n                    setCharacterMood('neutral');\n                }\n            }\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isStreaming,\n        messages\n    ]);\n    // Handle keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImmersiveMode.useEffect\": ()=>{\n            if (!isOpen) return;\n            const handleKeyDown = {\n                \"ImmersiveMode.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape') {\n                        onClose();\n                    } else if (e.key === 'F11') {\n                        e.preventDefault();\n                        // Toggle fullscreen if supported\n                        if (document.fullscreenElement) {\n                            document.exitFullscreen();\n                        } else {\n                            document.documentElement.requestFullscreen();\n                        }\n                    } else if (e.ctrlKey && e.key === 'p') {\n                        e.preventDefault();\n                        setShowParticles(!showParticles);\n                    } else if (e.ctrlKey && e.key === 'm') {\n                        e.preventDefault();\n                        setIsAudioEnabled(!isAudioEnabled);\n                    }\n                }\n            }[\"ImmersiveMode.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ImmersiveMode.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"ImmersiveMode.useEffect\"];\n        }\n    }[\"ImmersiveMode.useEffect\"], [\n        isOpen,\n        onClose,\n        showParticles,\n        isAudioEnabled\n    ]);\n    const handleSendMessage = ()=>{\n        const message = newMessage.trim();\n        if (!message || disabled) return;\n        onSendMessage(message);\n        setNewMessage('');\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleEmojiSelect = (emoji)=>{\n        var _inputRef_current;\n        setNewMessage((prev)=>prev + emoji);\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    if (!isOpen) return null;\n    const currentProfileAsset = profileAssets[currentImageIndex];\n    const currentProfileImage = (currentProfileAsset === null || currentProfileAsset === void 0 ? void 0 : currentProfileAsset.url) || profileImageUrl || (character === null || character === void 0 ? void 0 : character.image);\n    const currentBackground = (_backgroundAssets_ = backgroundAssets[0]) === null || _backgroundAssets_ === void 0 ? void 0 : _backgroundAssets_.url;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20\",\n                style: {\n                    backgroundImage: currentBackground ? \"linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)), url(\".concat(currentBackground, \")\") : undefined,\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            showParticles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    Array.from({\n                        length: characterMood === 'thinking' ? 30 : 20\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute rounded-full animate-pulse \".concat(characterMood === 'thinking' ? 'w-1 h-1 bg-blue-400/30' : characterMood === 'happy' ? 'w-2 h-2 bg-yellow-400/20' : 'w-1 h-1 bg-white/20'),\n                            style: {\n                                left: \"\".concat(Math.random() * 100, \"%\"),\n                                top: \"\".concat(Math.random() * 100, \"%\"),\n                                animationDelay: \"\".concat(Math.random() * 3, \"s\"),\n                                animationDuration: \"\".concat(characterMood === 'thinking' ? 1 + Math.random() * 2 : 2 + Math.random() * 3, \"s\")\n                            }\n                        }, i, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)),\n                    characterMood === 'thinking' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                0,\n                                1,\n                                2\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-400/50 rounded-full animate-bounce\",\n                                    style: {\n                                        animationDelay: \"\".concat(i * 0.2, \"s\"),\n                                        animationDuration: '1s'\n                                    }\n                                }, i, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 right-4 flex justify-between items-center z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-white text-xl font-bold\",\n                                children: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 text-sm\",\n                                children: \"Theater Mode\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                className: \"text-white hover:bg-white/10\",\n                                title: isAudioEnabled ? \"Disable Audio\" : \"Enable Audio\",\n                                children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 65\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>setShowParticles(!showParticles),\n                                className: \"text-white hover:bg-white/10\",\n                                title: showParticles ? \"Hide Effects\" : \"Show Effects\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onClose,\n                                className: \"text-white hover:bg-white/10\",\n                                title: \"Exit Immersive Mode\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-full pt-16 pb-4 flex-col md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center justify-center p-4 md:p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 shadow-2xl transition-all duration-1000 \".concat(characterMood === 'thinking' ? 'border-blue-400/50 shadow-blue-400/20' : characterMood === 'happy' ? 'border-yellow-400/50 shadow-yellow-400/20' : 'border-white/20 shadow-white/10'),\n                                    children: [\n                                        currentProfileImage && /\\.mp4(\\?|$)/i.test(currentProfileImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: currentProfileImage,\n                                            className: \"w-full h-full object-cover\",\n                                            autoPlay: true,\n                                            muted: true,\n                                            loop: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentProfileImage,\n                                            alt: character === null || character === void 0 ? void 0 : character.name,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-transparent via-transparent transition-all duration-1000 \".concat(characterMood === 'thinking' ? 'to-blue-400/10 animate-pulse' : characterMood === 'happy' ? 'to-yellow-400/10 animate-bounce' : 'to-white/5 animate-pulse')\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        (characterMood === 'thinking' || characterMood === 'happy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 rounded-full \".concat(characterMood === 'thinking' ? 'shadow-[0_0_50px_rgba(59,130,246,0.3)]' : 'shadow-[0_0_50px_rgba(251,191,36,0.3)]', \" animate-pulse\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm transition-all duration-500 \".concat(isStreaming ? 'bg-blue-500/60 shadow-lg shadow-blue-500/20' : 'bg-black/50'),\n                                        children: isStreaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        0,\n                                                        1,\n                                                        2\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 bg-white rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: \"\".concat(i * 0.2, \"s\"),\n                                                                animationDuration: '1s'\n                                                            }\n                                                        }, i, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1\",\n                                                    children: \"Thinking...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ready to chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 bg-black/40 backdrop-blur-md border-l border-white/10 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                                children: [\n                                    messages.slice(-10).map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-[80%] rounded-2xl px-4 py-3 \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white' : 'bg-white/10 backdrop-blur-sm text-white border border-white/20'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: message.content\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, message.id, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this)),\n                                    isStreaming && streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-w-[80%] rounded-2xl px-4 py-3 bg-white/10 backdrop-blur-sm text-white border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                                                    remarkPlugins: [\n                                                        remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                                    ],\n                                                    className: \"text-sm leading-relaxed prose prose-sm max-w-none prose-invert\",\n                                                    children: streamingMessage\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block w-2 h-4 bg-white/60 animate-pulse ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-end space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    ref: inputRef,\n                                                    value: newMessage,\n                                                    onChange: (e)=>setNewMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Message \".concat((character === null || character === void 0 ? void 0 : character.name) || 'character', \"...\"),\n                                                    disabled: disabled,\n                                                    className: \"bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-white/60 rounded-2xl pr-12\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_4__.EmojiPicker, {\n                                                        onEmojiSelect: handleEmojiSelect,\n                                                        disabled: disabled\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleSendMessage,\n                                            disabled: !newMessage.trim() || disabled,\n                                            className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minimize2_Send_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\immersive-mode.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(ImmersiveMode, \"NmcayRwAXc8xtHdXruQ832Az/7k=\");\n_c = ImmersiveMode;\nvar _c;\n$RefreshReg$(_c, \"ImmersiveMode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/immersive-mode.tsx\n"));

/***/ })

});