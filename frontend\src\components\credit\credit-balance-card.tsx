import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditBalance } from "@/types/credit";
import { Coins, RefreshCw } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { id } from 'date-fns/locale';

interface CreditBalanceCardProps {
  balance: CreditBalance | null;
  loading: boolean;
  onRefresh: () => void;
}

export function CreditBalanceCard({ balance, loading, onRefresh }: CreditBalanceCardProps) {
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: id });
    } catch {
      return 'Unknown';
    }
  };

  if (loading) {
    return (
      <Card className="bg-gradient-to-br from-bestieku-primary/10 to-bestieku-primary/5 border-bestieku-primary/20">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Coins className="w-5 h-5 text-bestieku-primary" />
            Saldo B Coin
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded mb-2" />
            <div className="h-4 bg-muted rounded w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!balance) {
    return (
      <Card className="bg-gradient-to-br from-red-50 to-red-100/50 border-red-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg text-red-700">
            <Coins className="w-5 h-5" />
            Saldo B Coin
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <p className="text-red-600 mb-4">Gagal memuat saldo B Coin</p>
            <Button
              onClick={onRefresh}
              variant="outline"
              size="sm"
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-br from-bestieku-primary/10 to-bestieku-primary/5 border-bestieku-primary/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Coins className="w-5 h-5 text-bestieku-primary" />
            Saldo B Coin
          </CardTitle>
          <Button
            onClick={onRefresh}
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-bestieku-primary/10"
          >
            <RefreshCw className="w-4 h-4 text-bestieku-primary" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <div className="text-3xl font-bold text-bestieku-primary mb-1">
            {balance.balance.toLocaleString('id-ID')}
          </div>
          <p className="text-sm text-muted-foreground">
            B Coin tersedia
          </p>
        </div>
        
        <div className="text-center pt-2 border-t border-bestieku-primary/20">
          <p className="text-xs text-muted-foreground">
            Terakhir diperbarui {formatDate(balance.updatedAt)}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
