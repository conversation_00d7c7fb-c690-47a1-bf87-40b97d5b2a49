'use client';

import { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';
import { authService } from '@/services/auth';

function AuthSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { initializeAfterLogin } = useAuth();

  useEffect(() => {
    const processTokens = async () => {
      try {
        // Get tokens from URL params
        const token = searchParams.get('token');
        const expires = searchParams.get('expires');

        if (token && expires) {
          console.log('Tokens received from URL params');

          // Decode and store tokens in localStorage
          const accessToken = decodeURIComponent(token);
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('tokenExpiredAt', expires);

          // Initialize auth context with user profile
          await initializeAfterLogin();

          // Clean URL and redirect to dashboard
          window.history.replaceState({}, '', '/auth/success');
          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
          return;
        }

        // Fallback: check if tokens are already in localStorage
        if (authService.isTokenValid()) {
          console.log('Tokens found in localStorage, proceeding...');
          await initializeAfterLogin();
          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
          return;
        }

        // No tokens found
        console.error('No tokens found in URL params or localStorage');
        router.push('/auth/error?error=no_tokens_found');
      } catch (error) {
        console.error('Token processing failed:', error);
        router.push('/auth/error?error=token_processing_failed');
      }
    };

    processTokens();
  }, [router, searchParams, initializeAfterLogin]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 p-4">
      <div className="max-w-md w-full space-y-8 text-center">
        <div className="space-y-6">
          {/* Success Icon */}
          <div className="flex justify-center">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
              Login Berhasil!
            </h1>
            <p className="text-muted-foreground">
              Anda berhasil masuk dengan Google. Sedang mengalihkan ke dashboard...
            </p>
          </div>

          {/* Loading Indicator */}
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="w-5 h-5 animate-spin text-bestieku-primary" />
            <span className="text-sm text-muted-foreground">
              Memproses login...
            </span>
          </div>

          {/* Manual Redirect Button */}
          <div className="pt-4">
            <Button
              onClick={() => router.push('/dashboard')}
              className="bg-bestieku-primary hover:bg-bestieku-primary-dark text-white"
            >
              Lanjut ke Dashboard
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/20 p-4">
        <div className="max-w-md w-full space-y-8 text-center">
          <div className="space-y-6">
            <div className="flex justify-center">
              <div className="w-20 h-20 bg-muted rounded-full flex items-center justify-center animate-pulse">
                <Loader2 className="w-10 h-10 text-muted-foreground animate-spin" />
              </div>
            </div>
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-foreground">
                Memuat...
              </h1>
              <p className="text-muted-foreground">
                Sedang memproses login...
              </p>
            </div>
          </div>
        </div>
      </div>
    }>
      <AuthSuccessContent />
    </Suspense>
  );
}
