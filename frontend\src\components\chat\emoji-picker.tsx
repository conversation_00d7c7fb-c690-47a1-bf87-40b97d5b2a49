'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Smile } from 'lucide-react';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  disabled?: boolean;
}

// Popular emojis organized by category
const EMOJI_CATEGORIES = {
  faces: {
    name: 'Wajah',
    emojis: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐']
  },
  hearts: {
    name: 'Hati',
    emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟']
  },
  gestures: {
    name: 'Gerakan',
    emojis: ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏']
  },
  activities: {
    name: 'Aktivitas',
    emojis: ['🎮', '🎯', '🎲', '🃏', '🎭', '🎨', '🎪', '🎵', '🎶', '🎤', '🎧', '🎬', '📚', '✍️', '💻', '📱', '🎸', '🥁', '🎹', '🎺', '🎻', '🏆', '🥇', '🎊', '🎉', '🎈', '🎁', '🎀', '🌟', '⭐', '✨', '💫', '🔥', '💯', '💢', '💥', '💨', '💦', '💤', '🗯️', '💭', '🗨️', '💬', '💌', '💕', '💖', '💗']
  },
  expressions: {
    name: 'Ekspresi',
    emojis: ['😳', '😱', '🤯', '😨', '😰', '😥', '😓', '🤗', '🤭', '🤫', '🤔', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😌', '😔', '😪', '🤤', '😴', '🥱', '😵', '🤐', '🤢', '🤮', '🤧', '🤒', '🤕', '🥴', '🥵', '🥶', '😈', '👿', '👹', '👺', '💀', '☠️', '👻', '👽', '👾', '🤖', '🎭', '💩']
  },
  symbols: {
    name: 'Simbol',
    emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚡', '🌟', '⭐', '✨', '💫']
  }
};

export function EmojiPicker({ onEmojiSelect, disabled = false }: EmojiPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState('faces');
  const [recentEmojis, setRecentEmojis] = useState<string[]>([]);
  const pickerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Load recent emojis from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem('bestieku-recent-emojis');
      if (saved) {
        setRecentEmojis(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Failed to load recent emojis:', error);
    }
  }, []);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current && 
        !pickerRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    setIsOpen(false);
    
    // Update recent emojis
    try {
      const newRecent = [emoji, ...recentEmojis.filter(e => e !== emoji)].slice(0, 12);
      setRecentEmojis(newRecent);
      localStorage.setItem('bestieku-recent-emojis', JSON.stringify(newRecent));
    } catch (error) {
      console.error('Failed to save recent emoji:', error);
    }
  };

  const togglePicker = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className="relative">
      {/* Emoji Button */}
      <Button
        ref={buttonRef}
        type="button"
        variant="ghost"
        size="icon"
        onClick={togglePicker}
        disabled={disabled}
        className="hover:bg-muted/50 text-muted-foreground hover:text-foreground"
        aria-label="Add emoji"
      >
        <Smile className="w-4 h-4" />
      </Button>

      {/* Emoji Picker Popup */}
      {isOpen && (
        <div
          ref={pickerRef}
          className="absolute bottom-full right-0 mb-2 w-80 max-w-[90vw] bg-background border rounded-lg shadow-xl z-50 max-h-80 flex flex-col animate-in slide-in-from-bottom-2 duration-200"
        >
          {/* Category Tabs */}
          <div className="flex border-b overflow-x-auto scrollbar-hide">
            {Object.entries(EMOJI_CATEGORIES).map(([key, category]) => (
              <button
                key={key}
                onClick={() => setActiveCategory(key)}
                className={`px-3 py-2 text-xs font-medium whitespace-nowrap transition-colors min-w-0 flex-shrink-0 ${
                  activeCategory === key
                    ? 'text-bestieku-primary border-b-2 border-bestieku-primary bg-bestieku-primary/5'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Emoji Grid */}
          <div className="flex-1 overflow-y-auto p-3">
            <div className="grid grid-cols-8 gap-1">
              {EMOJI_CATEGORIES[activeCategory as keyof typeof EMOJI_CATEGORIES].emojis.map((emoji, index) => (
                <button
                  key={`${emoji}-${index}`}
                  onClick={() => handleEmojiClick(emoji)}
                  className="w-8 h-8 flex items-center justify-center text-lg hover:bg-muted rounded transition-colors active:scale-110 active:bg-bestieku-primary/10"
                  title={emoji}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>

          {/* Recent & Popular Emojis */}
          <div className="border-t p-2">
            <div className="text-xs text-muted-foreground mb-2">
              {recentEmojis.length > 0 ? 'Baru Digunakan' : 'Populer'}
            </div>
            <div className="flex gap-1 flex-wrap">
              {(recentEmojis.length > 0 ? recentEmojis : ['😀', '😍', '😂', '👍', '❤️', '😊', '🤔', '😎', '🥰', '😘', '🤗', '😋']).map((emoji, index) => (
                <button
                  key={`quick-${emoji}-${index}`}
                  onClick={() => handleEmojiClick(emoji)}
                  className="w-8 h-8 flex items-center justify-center text-lg hover:bg-muted rounded transition-colors active:scale-110 active:bg-bestieku-primary/10"
                  title={emoji}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}