import { env } from '@/lib/env';
import { FavoritesResponse, FavoriteActionResponse, GetFavoritesParams } from '@/types/favorite';

class FavoriteService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async addToFavorite(characterId: string): Promise<FavoriteActionResponse> {
    const response = await fetch(`${env.API_BASE_URL}/characters/${characterId}/favorite`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add to favorites');
    }

    return response.json();
  }

  async removeFromFavorite(characterId: string): Promise<FavoriteActionResponse> {
    const response = await fetch(`${env.API_BASE_URL}/characters/${characterId}/favorite`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to remove from favorites');
    }

    return response.json();
  }

  async getFavorites(params: GetFavoritesParams = {}): Promise<FavoritesResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) {
      searchParams.append('page', params.page.toString());
    }
    
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }

    const url = `${env.API_BASE_URL}/characters/favorite${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch favorites');
    }

    return response.json();
  }
}

export const favoriteService = new FavoriteService();
