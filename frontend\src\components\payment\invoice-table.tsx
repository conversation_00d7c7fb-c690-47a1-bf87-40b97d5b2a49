import { useState } from 'react';
import { PaymentInvoice } from '@/types/payment';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Receipt, 
  Calendar, 
  CreditCard, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';

interface InvoiceTableProps {
  invoices: PaymentInvoice[];
  loading: boolean;
  onPayNow: (invoice: PaymentInvoice) => void;
  onRefresh: () => void;
}

export function InvoiceTable({ invoices, loading, onPayNow, onRefresh }: InvoiceTableProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: id });
    } catch {
      return 'Unknown';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: {
        color: 'bg-yellow-100 text-yellow-700 border-yellow-200',
        icon: Clock,
        text: 'Menunggu Pembayaran'
      },
      paid: {
        color: 'bg-green-100 text-green-700 border-green-200',
        icon: CheckCircle,
        text: 'Lunas'
      },
      failed: {
        color: 'bg-red-100 text-red-700 border-red-200',
        icon: XCircle,
        text: 'Gagal'
      },
      expired: {
        color: 'bg-gray-100 text-gray-700 border-gray-200',
        icon: AlertCircle,
        text: 'Kedaluwarsa'
      },
      cancelled: {
        color: 'bg-gray-100 text-gray-700 border-gray-200',
        icon: XCircle,
        text: 'Dibatalkan'
      }
    } as const;

    // Fallback untuk status yang tidak dikenal
    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: 'bg-gray-100 text-gray-700 border-gray-200',
      icon: AlertCircle,
      text: status || 'Unknown'
    };

    const IconComponent = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1 font-medium`}>
        <IconComponent className="w-3 h-3" />
        {config.text}
      </Badge>
    );
  };

  const getTotalAmount = (invoice: PaymentInvoice) => {
    const total = invoice.totals.find(t => t.type === 'total');
    return total ? total.amount : 0;
  };

  const getCreditsQuantity = (invoice: PaymentInvoice) => {
    const creditItem = invoice.items.find(item => item.id === 'credit');
    return creditItem ? creditItem.quantity : 0;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            Riwayat Pembelian
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/30">
                  <th className="text-left p-4 font-semibold text-sm">Invoice</th>
                  <th className="text-left p-4 font-semibold text-sm">B Coin</th>
                  <th className="text-left p-4 font-semibold text-sm">Total</th>
                  <th className="text-left p-4 font-semibold text-sm">Status</th>
                  <th className="text-left p-4 font-semibold text-sm">Tanggal</th>
                  <th className="text-center p-4 font-semibold text-sm">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {Array.from({ length: 3 }).map((_, i) => (
                  <tr key={i} className="border-b">
                    <td className="p-4"><div className="h-4 bg-muted rounded animate-pulse" /></td>
                    <td className="p-4"><div className="h-4 bg-muted rounded animate-pulse" /></td>
                    <td className="p-4"><div className="h-4 bg-muted rounded animate-pulse" /></td>
                    <td className="p-4"><div className="h-6 bg-muted rounded animate-pulse" /></td>
                    <td className="p-4"><div className="h-4 bg-muted rounded animate-pulse" /></td>
                    <td className="p-4"><div className="h-8 bg-muted rounded animate-pulse mx-auto w-20" /></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (invoices.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            Riwayat Pembelian
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Receipt className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Belum ada riwayat pembelian</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            Riwayat Pembelian
          </CardTitle>
          <Button
            onClick={onRefresh}
            variant="outline"
            size="sm"
          >
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-muted/30">
                <th className="text-left p-4 font-semibold text-sm">Invoice</th>
                <th className="text-left p-4 font-semibold text-sm">B Coin</th>
                <th className="text-left p-4 font-semibold text-sm">Total</th>
                <th className="text-left p-4 font-semibold text-sm">Status</th>
                <th className="text-left p-4 font-semibold text-sm">Tanggal</th>
                <th className="text-center p-4 font-semibold text-sm">Aksi</th>
              </tr>
            </thead>
            <tbody>
              {invoices.map((invoice) => (
                <tr key={invoice.id} className="border-b hover:bg-muted/20 transition-colors">
                  <td className="p-4">
                    <div className="font-medium text-sm">{invoice.invoiceCode}</div>
                  </td>
                  <td className="p-4">
                    <div className="font-semibold">
                      {getCreditsQuantity(invoice).toLocaleString('id-ID')}
                    </div>
                    <div className="text-xs text-muted-foreground">B Coin</div>
                  </td>
                  <td className="p-4">
                    <div className="font-semibold text-bestieku-primary">
                      {formatPrice(getTotalAmount(invoice))}
                    </div>
                  </td>
                  <td className="p-4">
                    {getStatusBadge(invoice.status)}
                  </td>
                  <td className="p-4">
                    <div className="text-sm">{formatDate(invoice.createdAt)}</div>
                    {invoice.status === 'paid' && invoice.paidDate && (
                      <div className="text-xs text-muted-foreground">
                        Dibayar {formatDate(invoice.paidDate)}
                      </div>
                    )}
                  </td>
                  <td className="p-4 text-center">
                    {invoice.status === 'pending' && (
                      <Button
                        onClick={() => onPayNow(invoice)}
                        className="bg-bestieku-primary hover:bg-bestieku-primary-dark text-white font-semibold"
                        size="sm"
                      >
                        <CreditCard className="w-4 h-4 mr-2" />
                        Bayar
                      </Button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
