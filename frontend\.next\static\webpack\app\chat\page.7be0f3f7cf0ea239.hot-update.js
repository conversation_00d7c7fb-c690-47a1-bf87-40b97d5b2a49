"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx":
/*!***********************************************************!*\
  !*** ./src/components/chat/character-profile-sidebar.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterProfileSidebar: () => (/* binding */ CharacterProfileSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _services_favorite__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/favorite */ \"(app-pages-browser)/./src/services/favorite.ts\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _phone_number_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./phone-number-modal */ \"(app-pages-browser)/./src/components/chat/phone-number-modal.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Check,ChevronDown,ChevronLeft,ChevronRight,ChevronUp,Copy,ExternalLink,Heart,Image,Info,MessageCircle,Settings,Share2,Tag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* __next_internal_client_entry_do_not_use__ CharacterProfileSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-2xl flex size-full items-center justify-center rounded-full bg-muted \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nfunction CharacterProfileSidebar(param) {\n    let { characterId, messageCount, isOpen, onToggle, onBackgroundChange } = param;\n    var _getProfileAssets_currentProfileImageIndex, _getProfileAssets_modalImageIndex;\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assets, setAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [assetsLoading, setAssetsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentProfileImageIndex, setCurrentProfileImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedBackground, setSelectedBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBackgroundSettings, setShowBackgroundSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavoriteLoading, setIsFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPhoneModal, setShowPhoneModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [linkCopied, setLinkCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalImageIndex, setModalImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            loadCharacter();\n            loadAssets();\n            checkFavoriteStatus();\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        characterId\n    ]);\n    // Keyboard navigation for image modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CharacterProfileSidebar.useEffect\": ()=>{\n            if (!showImageModal) return;\n            const handleKeyDown = {\n                \"CharacterProfileSidebar.useEffect.handleKeyDown\": (e)=>{\n                    switch(e.key){\n                        case 'Escape':\n                            handleModalClose();\n                            break;\n                        case 'ArrowLeft':\n                            e.preventDefault();\n                            handleModalPrevious();\n                            break;\n                        case 'ArrowRight':\n                            e.preventDefault();\n                            handleModalNext();\n                            break;\n                    }\n                }\n            }[\"CharacterProfileSidebar.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"CharacterProfileSidebar.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"CharacterProfileSidebar.useEffect\"];\n        }\n    }[\"CharacterProfileSidebar.useEffect\"], [\n        showImageModal,\n        modalImageIndex\n    ]);\n    const loadCharacter = async ()=>{\n        try {\n            setLoading(true);\n            const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterById(characterId);\n            setCharacter(characterData);\n        } catch (error) {\n            console.error('Failed to load character:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadAssets = async ()=>{\n        try {\n            setAssetsLoading(true);\n            const assetsData = await _services_character__WEBPACK_IMPORTED_MODULE_2__.characterService.getCharacterAssets(characterId);\n            setAssets(assetsData);\n            // Auto-select first background if character has backgrounds and no background is currently selected\n            const backgroundAssets = assetsData.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n            if (backgroundAssets.length > 0 && selectedBackground === null) {\n                const defaultBackground = backgroundAssets[0].id;\n                setSelectedBackground(defaultBackground);\n                onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(defaultBackground);\n            }\n        } catch (error) {\n            console.error('Failed to load character assets:', error);\n        } finally{\n            setAssetsLoading(false);\n        }\n    };\n    const checkFavoriteStatus = async ()=>{\n        try {\n            const favoritesResponse = await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.getFavorites();\n            const isFavorited = favoritesResponse.data.some((fav)=>fav.id === characterId);\n            setIsFavorite(isFavorited);\n        } catch (error) {\n            console.error('Failed to check favorite status:', error);\n        // Don't show error toast for this, just keep default state\n        }\n    };\n    const formatDate = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true,\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.id\n            });\n        } catch (e) {\n            return 'Unknown';\n        }\n    };\n    // Helper functions for assets\n    const getProfileAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'profile' && asset.isPublished);\n    };\n    const getBackgroundAssets = ()=>{\n        return assets.filter((asset)=>asset.purpose === 'background' && asset.isPublished);\n    };\n    const handleBackgroundChange = (backgroundId)=>{\n        setSelectedBackground(backgroundId);\n        onBackgroundChange === null || onBackgroundChange === void 0 ? void 0 : onBackgroundChange(backgroundId);\n    };\n    const handleFavoriteToggle = async ()=>{\n        if (!character) return;\n        setIsFavoriteLoading(true);\n        try {\n            if (isFavorite) {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.removeFromFavorite(character.id);\n                setIsFavorite(false);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Dihapus dari favorit\", {\n                    description: \"\".concat(character.name, \" telah dihapus dari daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            } else {\n                await _services_favorite__WEBPACK_IMPORTED_MODULE_3__.favoriteService.addToFavorite(character.id);\n                setIsFavorite(true);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Ditambahkan ke favorit\", {\n                    description: \"\".concat(character.name, \" telah ditambahkan ke daftar favorit Anda.\"),\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal mengubah favorit\", {\n                description: error instanceof Error ? error.message : \"Terjadi kesalahan saat mengubah status favorit.\",\n                duration: 4000\n            });\n        } finally{\n            setIsFavoriteLoading(false);\n        }\n    };\n    const handleWhatsAppClick = ()=>{\n        if (!(character === null || character === void 0 ? void 0 : character.whatsappUrl)) return;\n        // Check if user has phone number\n        if (!(user === null || user === void 0 ? void 0 : user.phoneNumber)) {\n            setShowPhoneModal(true);\n            return;\n        }\n        // If user has phone number, proceed to WhatsApp\n        window.open(character.whatsappUrl, '_blank');\n    };\n    const handlePhoneModalSuccess = ()=>{\n        if (character === null || character === void 0 ? void 0 : character.whatsappUrl) {\n            window.open(character.whatsappUrl, '_blank');\n        }\n    };\n    const handleCopyLink = async ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        try {\n            await navigator.clipboard.writeText(characterUrl);\n            setLinkCopied(true);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Link disalin!\", {\n                description: \"Link karakter telah disalin ke clipboard. Bagikan ke teman-temanmu!\",\n                duration: 3000\n            });\n            // Reset copied state after 3 seconds\n            setTimeout(()=>setLinkCopied(false), 3000);\n        } catch (error) {\n            console.error('Failed to copy link:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Gagal menyalin link\", {\n                description: \"Terjadi kesalahan saat menyalin link karakter.\",\n                duration: 3000\n            });\n        }\n    };\n    const handleShareToWhatsApp = ()=>{\n        if (!character) return;\n        const characterUrl = \"\".concat(window.location.origin, \"/dashboard?character=\").concat(character.id);\n        const message = \"Halo! Ayo chat dengan \".concat(character.name, \" di Bestieku! \\uD83E\\uDD16\\n\\n\").concat(character.description, \"\\n\\nKlik link ini: \").concat(characterUrl);\n        const whatsappUrl = \"https://wa.me/?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleImageClick = (index)=>{\n        setModalImageIndex(index);\n        setShowImageModal(true);\n        setImageLoading(true);\n    };\n    const handleModalClose = ()=>{\n        setShowImageModal(false);\n    };\n    const handleModalPrevious = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex > 0 ? modalImageIndex - 1 : profileAssets.length - 1);\n    };\n    const handleModalNext = ()=>{\n        const profileAssets = getProfileAssets();\n        setModalImageIndex(modalImageIndex < profileAssets.length - 1 ? modalImageIndex + 1 : 0);\n    };\n    // Touch handlers for swipe navigation\n    const handleTouchStart = (e)=>{\n        setTouchEnd(null);\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && getProfileAssets().length > 1) {\n            handleModalNext();\n        }\n        if (isRightSwipe && getProfileAssets().length > 1) {\n            handleModalPrevious();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-32 bg-muted rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-2/3\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 318,\n            columnNumber: 7\n        }, this);\n    }\n    if (!character) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onToggle,\n                        className: \"w-full\",\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 62\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Karakter tidak ditemukan\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden \".concat(isOpen ? 'w-80' : 'w-12'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-20 p-4 border-b flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    onClick: onToggle,\n                    className: \"w-full\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 60\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 mx-auto mb-4 relative flex shrink-0 overflow-hidden rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                        src: (()=>{\n                                            // Get first profile image asset (not video)\n                                            const profileAssets = getProfileAssets().filter((asset)=>asset.type === 'image');\n                                            return profileAssets.length > 0 ? profileAssets[0].url : character.image;\n                                        })(),\n                                        alt: character.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold mb-1\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground mb-3 font-medium\",\n                                    children: character.title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-2 mb-3\",\n                                    children: [\n                                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-bestieku-primary text-white\",\n                                            children: \"Mode Cerita\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: character.status\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this),\n                                character.whatsappUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        onClick: handleWhatsAppClick,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Chat di WhatsApp\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: isFavorite ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: handleFavoriteToggle,\n                                        disabled: isFavoriteLoading,\n                                        className: \"flex items-center gap-2 \".concat(isFavorite ? 'bg-red-500 hover:bg-red-600 text-white' : 'hover:bg-red-50 hover:text-red-600 hover:border-red-300'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isFavorite ? 'fill-current' : '')\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this),\n                                            isFavoriteLoading ? 'Loading...' : isFavorite ? 'Favorit' : 'Tambah ke Favorit'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleCopyLink,\n                                            className: \"flex items-center gap-2 flex-1\",\n                                            children: linkCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Link Disalin!\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Salin Link\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleShareToWhatsApp,\n                                            className: \"flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"WhatsApp\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Tentang\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                    children: character.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 13\n                        }, this),\n                        character.tags && character.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Tag\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: character.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Galeri Profil\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            onMouseEnter: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            onTouchStart: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) video.play();\n                                            },\n                                            onTouchEnd: (e)=>{\n                                                const video = e.currentTarget.querySelector('video');\n                                                if (video) {\n                                                    video.pause();\n                                                    video.currentTime = 0;\n                                                }\n                                            },\n                                            children: [\n                                                (()=>{\n                                                    const asset = getProfileAssets()[currentProfileImageIndex];\n                                                    const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                                    return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        src: url,\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        muted: true,\n                                                        playsInline: true,\n                                                        preload: \"metadata\",\n                                                        poster: url + \"#t=0.1\",\n                                                        onLoadedData: (e)=>{\n                                                            e.currentTarget.currentTime = 0;\n                                                        },\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: url,\n                                                        alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                                        className: \"w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity\",\n                                                        onClick: ()=>handleImageClick(currentProfileImageIndex)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })(),\n                                                getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-x-0 bottom-2 flex justify-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex > 0 ? currentProfileImageIndex - 1 : getProfileAssets().length - 1),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"secondary\",\n                                                            size: \"icon\",\n                                                            className: \"w-8 h-8 bg-black/50 hover:bg-black/70 text-white\",\n                                                            onClick: ()=>setCurrentProfileImageIndex(currentProfileImageIndex < getProfileAssets().length - 1 ? currentProfileImageIndex + 1 : 0),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 19\n                                        }, this),\n                                        ((_getProfileAssets_currentProfileImageIndex = getProfileAssets()[currentProfileImageIndex]) === null || _getProfileAssets_currentProfileImageIndex === void 0 ? void 0 : _getProfileAssets_currentProfileImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground text-center\",\n                                            children: getProfileAssets()[currentProfileImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 21\n                                        }, this),\n                                        getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-1\",\n                                            children: getProfileAssets().map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentProfileImageIndex ? 'bg-bestieku-primary' : 'bg-muted'),\n                                                    onClick: ()=>setCurrentProfileImageIndex(index)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getProfileAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 65\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Statistik Chat\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Pesan Anda\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Total Pesan\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-bestieku-primary\",\n                                                    children: character.messageCount > 999 ? '999+' : character.messageCount\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Jenis Karakter\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: character.storyMode ? 'Story' : 'Chat'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Info Karakter\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Dibuat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Diperbarui\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: formatDate(character.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 13\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Latar Belakang Chat\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: ()=>setShowBackgroundSettings(!showBackgroundSettings),\n                                            className: \"w-6 h-6\",\n                                            children: showBackgroundSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 47\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 83\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 17\n                                }, this),\n                                showBackgroundSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === null ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                            onClick: ()=>handleBackgroundChange(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-8 bg-gradient-to-b from-background to-muted/20 rounded border\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Latar belakang polos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 21\n                                        }, this),\n                                        getBackgroundAssets().map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border-2 cursor-pointer transition-colors \".concat(selectedBackground === asset.id ? 'border-bestieku-primary bg-bestieku-primary/10' : 'border-muted hover:border-muted-foreground/50'),\n                                                onClick: ()=>handleBackgroundChange(asset.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /\\.mp4(\\?|$)/i.test(asset.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                            src: asset.url,\n                                                            className: \"w-12 h-8 object-cover rounded border pointer-events-none\",\n                                                            muted: true,\n                                                            playsInline: true,\n                                                            preload: \"metadata\",\n                                                            onMouseEnter: (e)=>e.currentTarget.play(),\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.pause();\n                                                                e.currentTarget.currentTime = 0;\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: asset.url,\n                                                            alt: asset.caption || 'Background',\n                                                            className: \"w-12 h-8 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: asset.caption || 'Latar Belakang Kustom'\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Latar belakang karakter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, asset.id, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 23\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 15\n                        }, this),\n                        !assetsLoading && getBackgroundAssets().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {}, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 68\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this),\n            showImageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl max-h-[90vh] w-full mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleModalClose,\n                                className: \"absolute top-4 right-4 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalPrevious,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleModalNext,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Check_ChevronDown_ChevronLeft_ChevronRight_ChevronUp_Copy_ExternalLink_Heart_Image_Info_MessageCircle_Settings_Share2_Tag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-black rounded-lg overflow-hidden\",\n                                onTouchStart: handleTouchStart,\n                                onTouchMove: handleTouchMove,\n                                onTouchEnd: handleTouchEnd,\n                                children: [\n                                    (()=>{\n                                        const asset = getProfileAssets()[modalImageIndex];\n                                        const url = asset === null || asset === void 0 ? void 0 : asset.url;\n                                        return url && /\\.mp4(\\?|$)/i.test(url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: url,\n                                            className: \"w-full max-h-[80vh] object-contain\",\n                                            controls: true,\n                                            autoPlay: true,\n                                            muted: true,\n                                            playsInline: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: url,\n                                            alt: (asset === null || asset === void 0 ? void 0 : asset.caption) || 'Profile image',\n                                            className: \"w-full max-h-[80vh] object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 19\n                                        }, this);\n                                    })(),\n                                    ((_getProfileAssets_modalImageIndex = getProfileAssets()[modalImageIndex]) === null || _getProfileAssets_modalImageIndex === void 0 ? void 0 : _getProfileAssets_modalImageIndex.caption) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-center\",\n                                            children: getProfileAssets()[modalImageIndex].caption\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 13\n                            }, this),\n                            getProfileAssets().length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm\",\n                                children: [\n                                    modalImageIndex + 1,\n                                    \" / \",\n                                    getProfileAssets().length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                                lineNumber: 852,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 785,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        onClick: handleModalClose\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 784,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phone_number_modal__WEBPACK_IMPORTED_MODULE_8__.PhoneNumberModal, {\n                isOpen: showPhoneModal,\n                onClose: ()=>setShowPhoneModal(false),\n                onSuccess: handlePhoneModalSuccess,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || 'Character'\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n                lineNumber: 867,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\character-profile-sidebar.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterProfileSidebar, \"3mCKT5IcF9Xt3P2BS7RMtQHfEtw=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c1 = CharacterProfileSidebar;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"CharacterProfileSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\n"));

/***/ })

});