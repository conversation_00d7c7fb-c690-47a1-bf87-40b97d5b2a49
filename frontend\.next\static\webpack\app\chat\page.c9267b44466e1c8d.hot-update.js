"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./reset-chat-modal */ \"(app-pages-browser)/./src/components/chat/reset-chat-modal.tsx\");\n/* harmony import */ var _emoji_picker__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./emoji-picker */ \"(app-pages-browser)/./src/components/chat/emoji-picker.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Simple avatar component that always uses static image\nconst SimpleAvatar = (param)=>{\n    let { src, alt, className } = param;\n    if (!src) {\n        var _alt_charAt;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary \".concat(className || ''),\n            children: (alt === null || alt === void 0 ? void 0 : (_alt_charAt = alt.charAt(0)) === null || _alt_charAt === void 0 ? void 0 : _alt_charAt.toUpperCase()) || 'C'\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: alt,\n        className: \"aspect-square size-full object-cover \".concat(className || '')\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleAvatar;\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, profileImageUrl, characterName } = param;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c2 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { streamingMessage, profileImageUrl, characterName } = param;\n    _s1();\n    const [thinkingText, setThinkingText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('Berpikir');\n    // Cycle through thinking messages when not streaming actual content\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamingMessage.useEffect\": ()=>{\n            if (!streamingMessage) {\n                const messages = [\n                    'Berpikir',\n                    'Merenungkan',\n                    'Mempertimbangkan',\n                    'Memikirkan jawaban'\n                ];\n                let index = 0;\n                const interval = setInterval({\n                    \"StreamingMessage.useEffect.interval\": ()=>{\n                        index = (index + 1) % messages.length;\n                        setThinkingText(messages[index]);\n                    }\n                }[\"StreamingMessage.useEffect.interval\"], 2000);\n                return ({\n                    \"StreamingMessage.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMessage.useEffect\"];\n            }\n        }\n    }[\"StreamingMessage.useEffect\"], [\n        streamingMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                    src: profileImageUrl,\n                    alt: characterName || 'Character'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: thinkingText\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n}, \"W7FciA9Z3UpxXy1pJlVC9GvQ8tw=\"));\n_c3 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount, profileImageUrl } = param;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                                src: profileImageUrl,\n                                alt: character.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Mode Cerita\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages, profileImageUrl } = param;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleAvatar, {\n                        src: profileImageUrl,\n                        alt: character.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s2((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s2();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    // Detect mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInput.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInput.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInput.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInput.useEffect\"];\n        }\n    }[\"ChatInput.useEffect\"], []);\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    const handleEmojiSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleEmojiSelect]\": (emoji)=>{\n            const newValue = newMessage + emoji;\n            setNewMessage(newValue);\n            messageRef.current = newValue;\n            // Focus back to input after emoji selection\n            if (inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"ChatInput.useCallback[handleEmojiSelect]\"], [\n        newMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 \".concat(isMobile ? 'p-3 pb-6' : 'p-4'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"\".concat(isMobile ? 'pr-20' : 'pr-24', \" rounded-2xl border-2 focus:border-bestieku-primary transition-colors \").concat(isMobile ? 'py-3 text-base' : 'py-3'),\n                                style: isMobile ? {\n                                    fontSize: '16px'\n                                } : {}\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emoji_picker__WEBPACK_IMPORTED_MODULE_9__.EmojiPicker, {\n                                        onEmojiSelect: handleEmojiSelect,\n                                        disabled: disabled\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 \".concat(isMobile ? 'px-4 py-3' : 'px-6 py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 383,\n        columnNumber: 5\n    }, undefined);\n}, \"0TgdyP+W7Ya7Fi9I41+kS8az6FU=\"));\n_c6 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s3((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c7 = _s3((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5;\n    _s3();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileImageUrl, setProfileImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isImmersiveMode, setIsImmersiveMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Show reset confirmation modal\n    const handleResetClick = ()=>{\n        setShowResetModal(true);\n    };\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Close modal\n            setShowResetModal(false);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                        // Get profile image from assets (prefer static image over video)\n                        const profileAssets = assets.filter({\n                            \"ChatInterface.useEffect.loadCharacterData.profileAssets\": (asset)=>asset.purpose === 'profile' && asset.type === 'image' && asset.isPublished\n                        }[\"ChatInterface.useEffect.loadCharacterData.profileAssets\"]);\n                        if (profileAssets.length > 0) {\n                            setProfileImageUrl(profileAssets[0].url);\n                        } else {\n                            // Fallback to character.image if no profile assets\n                            setProfileImageUrl(characterData.image);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Reset all streaming states when chat changes\n            setIsStreaming(false);\n            setStreamingMessage('');\n            setSending(false);\n            // Cleanup any existing connections and timeouts\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n                streamConnectionRef.current = null;\n            }\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n                streamingTimeoutRef.current = null;\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n                streamTimeoutRef.current = null;\n            }\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const currentChatId = chat.id; // Capture current chat ID\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Show AI thinking for 3-9 seconds before sending to backend\n                setIsStreaming(true);\n                setStreamingMessage('');\n                const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms\n                console.log(\"AI thinking for \".concat(thinkingDelay, \"ms before processing...\"));\n                await new Promise({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (resolve)=>setTimeout(resolve, thinkingDelay)\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                // Check if chat hasn't changed during the thinking delay\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed during thinking delay, aborting message send');\n                    return;\n                }\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Check again if chat hasn't changed after API call\n                if (chat.id !== currentChatId) {\n                    console.log('Chat changed after message send, aborting streaming');\n                    return;\n                }\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming(currentChatId);\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Only show error and remove message if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                    alert('Failed to send message. Please try again.');\n                }\n            } finally{\n                // Only reset sending state if we're still on the same chat\n                if (chat.id === currentChatId) {\n                    setSending(false);\n                }\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async (expectedChatId)=>{\n        const targetChatId = expectedChatId || chat.id;\n        console.log('Starting stream for chat:', targetChatId);\n        // Check if chat hasn't changed before starting stream\n        if (expectedChatId && chat.id !== expectedChatId) {\n            console.log('Chat changed before streaming started, aborting');\n            return;\n        }\n        // isStreaming already set to true in handleSendMessage\n        // setStreamingMessage(''); // Keep current thinking state\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            // Check if chat hasn't changed before updating\n            if (expectedChatId && chat.id !== expectedChatId) {\n                return;\n            }\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    // Check again if chat hasn't changed before the delayed update\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        setStreamingMessage(message);\n                        lastUpdateTime = Date.now();\n                    }\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            // Check if chat hasn't changed during streaming\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed during streaming, ignoring SSE event');\n                return;\n            }\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message only if chat hasn't changed\n                    if (!expectedChatId || chat.id === expectedChatId) {\n                        var _data_data;\n                        const finalMessage = {\n                            id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                            role: 'assistant',\n                            content: currentStreamingMessage,\n                            contentType: 'text',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                finalMessage\n                            ]);\n                        setStreamingMessage('');\n                        setIsStreaming(false);\n                    }\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                setStreamingMessage('');\n            }\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error, streaming was actually active, and chat hasn't changed\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming && (!expectedChatId || chat.id === expectedChatId)) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Check one more time if chat hasn't changed before creating connection\n            if (expectedChatId && chat.id !== expectedChatId) {\n                console.log('Chat changed before creating stream connection, aborting');\n                return;\n            }\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(targetChatId, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active, connection exists, and chat hasn't changed\n                if (isStreaming && streamConnectionRef.current && (!expectedChatId || chat.id === expectedChatId)) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            // Only update state if chat hasn't changed\n            if (!expectedChatId || chat.id === expectedChatId) {\n                setIsStreaming(false);\n                alert('Failed to establish connection for AI response. Please try again.');\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 900,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 909,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 899,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full \".concat(isMobile ? 'h-16 p-3' : 'h-20 p-4'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 944,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 929,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleResetClick,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 964,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 927,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0,\n                        profileImageUrl: profileImageUrl\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    profileImageUrl: profileImageUrl,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                profileImageUrl: profileImageUrl,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1009,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 1030,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 978,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1034,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_reset_chat_modal__WEBPACK_IMPORTED_MODULE_8__.ResetChatModal, {\n                isOpen: showResetModal,\n                onClose: ()=>setShowResetModal(false),\n                onConfirm: handleDeleteMessages,\n                characterName: (character === null || character === void 0 ? void 0 : character.name) || ((_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name),\n                messageCount: messages.length,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 1042,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 925,\n        columnNumber: 5\n    }, undefined);\n}, \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\")), \"E0wRIfZTQyF1oUVuBeyXVmz4FGA=\");\n_c8 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"SimpleAvatar\");\n$RefreshReg$(_c1, \"MessageContent\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"StreamingMessage\");\n$RefreshReg$(_c4, \"WelcomeMessage\");\n$RefreshReg$(_c5, \"OpeningScene\");\n$RefreshReg$(_c6, \"ChatInput\");\n$RefreshReg$(_c7, \"ChatInterface$memo\");\n$RefreshReg$(_c8, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvY2hhdC1pbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEU7QUFHaEM7QUFDVTtBQUNSO0FBQ0Y7QUFFQTtBQUN3QztBQUN2QztBQUNWO0FBQ007QUFDUjtBQUNKO0FBQ3FCO0FBQ1A7QUFFN0Msd0RBQXdEO0FBQ3hELE1BQU1zQixlQUFlO1FBQUMsRUFBRUMsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLFNBQVMsRUFJMUM7SUFDQyxJQUFJLENBQUNGLEtBQUs7WUFHSEM7UUFGTCxxQkFDRSw4REFBQ0U7WUFBSUQsV0FBVyxnSEFBZ0ksT0FBaEJBLGFBQWE7c0JBQzFJRCxDQUFBQSxnQkFBQUEsMkJBQUFBLGNBQUFBLElBQUtHLE1BQU0sQ0FBQyxnQkFBWkgsa0NBQUFBLFlBQWdCSSxXQUFXLE9BQU07Ozs7OztJQUd4QztJQUVBLHFCQUNFLDhEQUFDQztRQUNDTixLQUFLQTtRQUNMQyxLQUFLQTtRQUNMQyxXQUFXLHdDQUF3RCxPQUFoQkEsYUFBYTs7Ozs7O0FBR3RFO0tBcEJNSDtBQW1DTixNQUFNUSwrQkFBaUJ6QiwyQ0FBSUEsQ0FBQztRQUFDLEVBQUUwQixPQUFPLEVBQUVDLElBQUksRUFBdUI7SUFDakUsSUFBSUEsU0FBUyxRQUFRO1FBQ25CLGlFQUFpRTtRQUNqRSxxQkFBTyw4REFBQ0M7WUFBRVIsV0FBVTtzQkFBK0NNOzs7Ozs7SUFDckU7SUFFQSxzQ0FBc0M7SUFDdEMscUJBQ0UsOERBQUNMO1FBQUlELFdBQVU7a0JBQ2IsNEVBQUNSLHFEQUFhQTtZQUNaaUIsZUFBZTtnQkFBQ2hCLG1EQUFTQTthQUFDO1lBQzFCaUIsWUFBWTtnQkFDViw4QkFBOEI7Z0JBQzlCRixHQUFHO3dCQUFDLEVBQUVHLFFBQVEsRUFBRTt5Q0FDZCw4REFBQ0g7d0JBQUVSLFdBQVU7a0NBQWtDVzs7Ozs7OztnQkFFakQsOEJBQThCO2dCQUM5QkMsSUFBSTt3QkFBQyxFQUFFRCxRQUFRLEVBQUU7eUNBQ2YsOERBQUNDO3dCQUFHWixXQUFVO2tDQUF1Qlc7Ozs7Ozs7Z0JBRXZDLDBCQUEwQjtnQkFDMUJFLFFBQVE7d0JBQUMsRUFBRUYsUUFBUSxFQUFFO3lDQUNuQiw4REFBQ0U7d0JBQU9iLFdBQVU7a0NBQThCVzs7Ozs7OztnQkFFbEQsd0JBQXdCO2dCQUN4QkcsTUFBTTt3QkFBQyxFQUFFSCxRQUFRLEVBQUVYLFNBQVMsRUFBRTtvQkFDNUIsTUFBTWUsV0FBVyxDQUFDZjtvQkFDbEIsSUFBSWUsVUFBVTt3QkFDWixxQkFDRSw4REFBQ0Q7NEJBQUtkLFdBQVU7c0NBQ2JXOzs7Ozs7b0JBR1A7b0JBQ0EscUJBQ0UsOERBQUNHO3dCQUFLZCxXQUFVO2tDQUNiVzs7Ozs7O2dCQUdQO2dCQUNBLGtCQUFrQjtnQkFDbEJLLElBQUk7d0JBQUMsRUFBRUwsUUFBUSxFQUFFO3lDQUNmLDhEQUFDSzt3QkFBR2hCLFdBQVU7a0NBQXdDVzs7Ozs7OztnQkFFeERNLElBQUk7d0JBQUMsRUFBRU4sUUFBUSxFQUFFO3lDQUNmLDhEQUFDTTt3QkFBR2pCLFdBQVU7a0NBQTJDVzs7Ozs7OztnQkFFM0RPLElBQUk7d0JBQUMsRUFBRVAsUUFBUSxFQUFFO3lDQUNmLDhEQUFDTzt3QkFBR2xCLFdBQVU7a0NBQVdXOzs7Ozs7O2dCQUUzQix3QkFBd0I7Z0JBQ3hCUSxZQUFZO3dCQUFDLEVBQUVSLFFBQVEsRUFBRTt5Q0FDdkIsOERBQUNRO3dCQUFXbkIsV0FBVTtrQ0FDbkJXOzs7Ozs7O1lBR1A7c0JBRUNMOzs7Ozs7Ozs7OztBQUlUO01BOURNRDtBQXNFTixNQUFNZSw0QkFBY3hDLDJDQUFJQSxJQUFDO1FBQUMsRUFBRXlDLE9BQU8sRUFBRUMsZUFBZSxFQUFFQyxhQUFhLEVBQW9COztJQUNyRixNQUFNQyxhQUFhN0Msa0RBQVdBOytDQUFDLENBQUM4QztZQUM5QixJQUFJO2dCQUNGLE9BQU9uQyx5R0FBbUJBLENBQUMsSUFBSW9DLEtBQUtELGFBQWE7b0JBQUVFLFdBQVc7b0JBQU1DLFFBQVFyQyxnREFBRUE7Z0JBQUM7WUFDakYsRUFBRSxVQUFNO2dCQUNOLE9BQU87WUFDVDtRQUNGOzhDQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ1U7UUFDQ0QsV0FBVyx3QkFBa0YsT0FBMURxQixRQUFRZCxJQUFJLEtBQUssU0FBUyxnQkFBZ0I7O1lBRzVFYyxRQUFRZCxJQUFJLEtBQUssNkJBQ2hCLDhEQUFDTjtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0g7b0JBQ0NDLEtBQUt3QjtvQkFDTHZCLEtBQUt3QixpQkFBaUI7Ozs7Ozs7Ozs7OzBCQUs1Qiw4REFBQ3RCO2dCQUNDRCxXQUFXLHlDQUdSLE9BSGlEcUIsUUFBUWQsSUFBSSxLQUFLLFNBQy9ELGlEQUNBOztrQ0FHTiw4REFBQ0Y7d0JBQWVDLFNBQVNlLFFBQVFmLE9BQU87d0JBQUVDLE1BQU1jLFFBQVFkLElBQUk7Ozs7OztrQ0FDNUQsOERBQUNOO3dCQUFJRCxXQUFXLDBDQUNiLE9BRHVEcUIsUUFBUWQsSUFBSSxLQUFLLFNBQVMsa0JBQWtCOzswQ0FFcEcsOERBQUNDO2dDQUFFUixXQUFVOzBDQUNWd0IsV0FBV0gsUUFBUVEsU0FBUzs7Ozs7OzRCQUU5QlIsUUFBUWQsSUFBSSxLQUFLLHdCQUNoQiw4REFBQ047Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzdCO01BN0NNb0I7QUFxRE4sTUFBTVUsaUNBQW1CbEQsMkNBQUlBLEtBQUM7UUFBQyxFQUFFbUQsZ0JBQWdCLEVBQUVULGVBQWUsRUFBRUMsYUFBYSxFQUF5Qjs7SUFDeEcsTUFBTSxDQUFDUyxjQUFjQyxnQkFBZ0IsR0FBRzFELHFEQUFjLENBQUM7SUFFdkQsb0VBQW9FO0lBQ3BFQSxzREFBZTtzQ0FBQztZQUNkLElBQUksQ0FBQ3dELGtCQUFrQjtnQkFDckIsTUFBTUcsV0FBVztvQkFBQztvQkFBWTtvQkFBZTtvQkFBb0I7aUJBQXFCO2dCQUN0RixJQUFJQyxRQUFRO2dCQUVaLE1BQU1DLFdBQVdDOzJEQUFZO3dCQUMzQkYsUUFBUSxDQUFDQSxRQUFRLEtBQUtELFNBQVNJLE1BQU07d0JBQ3JDTCxnQkFBZ0JDLFFBQVEsQ0FBQ0MsTUFBTTtvQkFDakM7MERBQUc7Z0JBRUg7a0RBQU8sSUFBTUksY0FBY0g7O1lBQzdCO1FBQ0Y7cUNBQUc7UUFBQ0w7S0FBaUI7SUFFckIscUJBQ0UsOERBQUM5QjtRQUFJRCxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNIO29CQUNDQyxLQUFLd0I7b0JBQ0x2QixLQUFLd0IsaUJBQWlCOzs7Ozs7Ozs7OzswQkFJMUIsOERBQUN0QjtnQkFBSUQsV0FBVTs7b0JBQ1orQixpQ0FDQyw4REFBQzFCO3dCQUFlQyxTQUFTeUI7d0JBQWtCeEIsTUFBSzs7Ozs7a0RBRWhELDhEQUFDTjt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUN3QztnQ0FBS3hDLFdBQVU7MENBQWlDZ0M7Ozs7OzswQ0FDakQsOERBQUMvQjtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNDO3dDQUFJRCxXQUFVO3dDQUEwRHlDLE9BQU87NENBQUVDLGdCQUFnQjt3Q0FBTzs7Ozs7O2tEQUN6Ryw4REFBQ3pDO3dDQUFJRCxXQUFVO3dDQUEwRHlDLE9BQU87NENBQUVDLGdCQUFnQjt3Q0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUsvRyw4REFBQ3pDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUN3QztvQ0FBS3hDLFdBQVU7OENBQ2IrQixtQkFBbUIsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9sRDtNQXBETUQ7QUE0RE4sTUFBTWEsK0JBQWlCL0QsMkNBQUlBLENBQUM7UUFBQyxFQUFFZ0UsU0FBUyxFQUFFQyxZQUFZLEVBQUV2QixlQUFlLEVBQXVCO0lBQzVGLElBQUksQ0FBQ3NCLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQzNDO1FBQUlELFdBQVU7a0JBRWIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDSDtnQ0FDQ0MsS0FBS3dCO2dDQUNMdkIsS0FBSzZDLFVBQVVFLElBQUk7Ozs7Ozs7Ozs7O3NDQUl2Qiw4REFBQzdDOzRCQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7OEJBR2pCLDhEQUFDK0M7b0JBQUcvQyxXQUFVOzhCQUEyQjRDLFVBQVVFLElBQUk7Ozs7OztnQkFFdERGLFVBQVVJLEtBQUssa0JBQ2QsOERBQUN4QztvQkFBRVIsV0FBVTs4QkFDVjRDLFVBQVVJLEtBQUs7Ozs7Ozs4QkFJcEIsOERBQUMvQztvQkFBSUQsV0FBVTs7d0JBQ1o0QyxVQUFVSyxTQUFTLGtCQUNsQiw4REFBQ2hFLHVEQUFLQTs0QkFBQ2UsV0FBVTtzQ0FBaUM7Ozs7OztzQ0FJcEQsOERBQUNmLHVEQUFLQTs0QkFBQ2lFLFNBQVE7NEJBQVVsRCxXQUFVO3NDQUNoQzRDLFVBQVVPLE1BQU07Ozs7Ozs7Ozs7Ozs4QkFJckIsOERBQUNsRDtvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUN3Qzs7Z0NBQU1LO2dDQUFhOzs7Ozs7O3NDQUNwQiw4REFBQ0w7c0NBQUs7Ozs7OztzQ0FDTiw4REFBQ0E7NEJBQUt4QyxXQUFVO3NDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPM0M7TUEvQ00yQztBQXdETixNQUFNUyw2QkFBZXhFLDJDQUFJQSxDQUFDO1FBQUMsRUFBRWdFLFNBQVMsRUFBRVMsV0FBVyxFQUFFL0IsZUFBZSxFQUFxQjtJQUN2RixJQUFJLEVBQUNzQixzQkFBQUEsZ0NBQUFBLFVBQVdVLFlBQVksR0FBRSxPQUFPO0lBRXJDLHFCQUNFLDhEQUFDckQ7UUFBSUQsV0FBVyxRQUFzQyxPQUE5QnFELGNBQWMsU0FBUztrQkFFN0MsNEVBQUNwRDtZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNIO3dCQUNDQyxLQUFLd0I7d0JBQ0x2QixLQUFLNkMsVUFBVUUsSUFBSTs7Ozs7Ozs7Ozs7OEJBR3ZCLDhEQUFDN0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUN3QztvQ0FBS3hDLFdBQVU7OENBQXVCNEMsVUFBVUUsSUFBSTs7Ozs7OzhDQUNyRCw4REFBQ047b0NBQUt4QyxXQUFVOzhDQUFnQzs7Ozs7Ozs7Ozs7O3NDQUVsRCw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNRO2dDQUFFUixXQUFVOzBDQUNWNEMsVUFBVVUsWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9yQztNQTNCTUY7QUFvQ04sTUFBTUcsMEJBQVkzRSwyQ0FBSUEsS0FBQztRQUFDLEVBQUU0RSxhQUFhLEVBQUVDLFFBQVEsRUFBRWxDLGFBQWEsRUFBRW1DLFdBQVcsRUFBa0I7O0lBQzdGLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHcEYsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDcUYsVUFBVUMsWUFBWSxHQUFHdEYsK0NBQVFBLENBQUM7SUFDekMsTUFBTXVGLFdBQVdyRiw2Q0FBTUEsQ0FBbUI7SUFFMUMsMkRBQTJEO0lBQzNELE1BQU1zRixhQUFhdEYsNkNBQU1BLENBQUM7SUFDMUJzRixXQUFXQyxPQUFPLEdBQUdOO0lBRXJCLGdCQUFnQjtJQUNoQmxGLGdEQUFTQTsrQkFBQztZQUNSLE1BQU15RjttREFBYztvQkFDbEJKLFlBQVlLLE9BQU9DLFVBQVUsR0FBRztnQkFDbEM7O1lBQ0FGO1lBQ0FDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDO3VDQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKOztRQUNwRDs4QkFBRyxFQUFFO0lBRUwsTUFBTUssb0JBQW9CNUYsa0RBQVdBO29EQUFDO1lBQ3BDLE1BQU02RixpQkFBaUJSLFdBQVdDLE9BQU8sQ0FBQ1EsSUFBSTtZQUM5QyxJQUFJLENBQUNELGtCQUFrQmYsVUFBVTtZQUNqQ0QsY0FBY2dCO1lBQ2RaLGNBQWM7WUFDZEksV0FBV0MsT0FBTyxHQUFHO1FBQ3ZCO21EQUFHO1FBQUNSO1FBQVVEO0tBQWM7SUFFNUIsTUFBTWtCLGlCQUFpQi9GLGtEQUFXQTtpREFBQyxDQUFDZ0c7WUFDbEMsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO2dCQUNwQ0YsRUFBRUcsY0FBYztnQkFDaEJQO1lBQ0Y7UUFDRjtnREFBRztRQUFDQTtLQUFrQjtJQUV0QixNQUFNUSxvQkFBb0JwRyxrREFBV0E7b0RBQUMsQ0FBQ2dHO1lBQ3JDLE1BQU1LLFFBQVFMLEVBQUVNLE1BQU0sQ0FBQ0QsS0FBSztZQUM1QnBCLGNBQWNvQjtZQUNkaEIsV0FBV0MsT0FBTyxHQUFHZTtRQUN2QjttREFBRyxFQUFFO0lBRUwsTUFBTUUsb0JBQW9Cdkcsa0RBQVdBO29EQUFDLENBQUN3RztZQUNyQyxNQUFNQyxXQUFXekIsYUFBYXdCO1lBQzlCdkIsY0FBY3dCO1lBQ2RwQixXQUFXQyxPQUFPLEdBQUdtQjtZQUNyQiw0Q0FBNEM7WUFDNUMsSUFBSXJCLFNBQVNFLE9BQU8sRUFBRTtnQkFDcEJGLFNBQVNFLE9BQU8sQ0FBQ29CLEtBQUs7WUFDeEI7UUFDRjttREFBRztRQUFDMUI7S0FBVztJQUVmLHFCQUNFLDhEQUFDMUQ7UUFDQ0QsV0FBVyxxR0FDUixPQUQ2RzZELFdBQVcsYUFBYTs7MEJBR3hJLDhEQUFDNUQ7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNoQix1REFBS0E7Z0NBQ0pzRyxLQUFLdkI7Z0NBQ0xpQixPQUFPckI7Z0NBQ1A0QixVQUFVUjtnQ0FDVlMsWUFBWWQ7Z0NBQ1plLGFBQWEsV0FBd0MsT0FBN0JsRSxpQkFBaUIsYUFBWTtnQ0FDckRrQyxVQUFVQTtnQ0FDVnpELFdBQVcsR0FBd0c2RCxPQUFyR0EsV0FBVyxVQUFVLFNBQVEsMEVBQ3hDLE9BRGdIQSxXQUFXLG1CQUFtQjtnQ0FFakpwQixPQUFPb0IsV0FBVztvQ0FBRTZCLFVBQVU7Z0NBQU8sSUFBSSxDQUFDOzs7Ozs7MENBSTVDLDhEQUFDekY7Z0NBQUlELFdBQVU7O2tEQUViLDhEQUFDSixzREFBV0E7d0NBQ1YrRixlQUFlVDt3Q0FDZnpCLFVBQVVBOzs7Ozs7b0NBSVhBLDBCQUNDLDhEQUFDeEQ7d0NBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLckIsOERBQUNqQix5REFBTUE7d0JBQ0w2RyxTQUFTckI7d0JBQ1RkLFVBQVUsQ0FBQ0UsV0FBV2MsSUFBSSxNQUFNaEI7d0JBQ2hDekQsV0FBVyx1SUFDUixPQUQrSTZELFdBQVcsY0FBYztrQ0FHM0ssNEVBQUMzRSwwR0FBSUE7NEJBQUNjLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBS25CLENBQUM2RCwwQkFDQSw4REFBQzVEO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ3dDO2tDQUFLOzs7Ozs7b0JBQ0xrQiw2QkFDQyw4REFBQ2xCO3dCQUFLeEMsV0FBVTtrQ0FBc0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1sRTtNQXpHTXVEO0FBMkdOLE1BQU1zQyw4QkFBZ0JqSCxJQUFBQSwyQ0FBSUEsV0FBQztRQUFDLEVBQUVrSCxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsZUFBZSxFQUFFQyxXQUFXLEVBQUVDLGtCQUFrQixFQUFzQjtRQTREL0VKLGlCQUNEQSxrQkE4RDVCQSxrQkFBdUJBLGtCQStYc0JBLGtCQXFHVEE7O0lBOWxCeEMsTUFBTSxDQUFDNUQsVUFBVWlFLFlBQVksR0FBRzNILCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDNEgsU0FBU0MsV0FBVyxHQUFHN0gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDOEgsU0FBU0MsV0FBVyxHQUFHL0gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDdUQsa0JBQWtCeUUsb0JBQW9CLEdBQUdoSSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNrRixhQUFhK0MsZUFBZSxHQUFHakksK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUYsVUFBVUMsWUFBWSxHQUFHdEYsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDa0ksaUJBQWlCQyxtQkFBbUIsR0FBR25JLCtDQUFRQSxDQUFtQixFQUFFO0lBQzNFLE1BQU0sQ0FBQ29JLG9CQUFvQkMsc0JBQXNCLEdBQUdySSwrQ0FBUUEsQ0FBZ0I7SUFDNUUsTUFBTSxDQUFDc0ksWUFBWUMsY0FBYyxHQUFHdkksK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDb0UsV0FBV29FLGFBQWEsR0FBR3hJLCtDQUFRQSxDQUFNO0lBQ2hELE1BQU0sQ0FBQ3lJLGdCQUFnQkMsa0JBQWtCLEdBQUcxSSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUM4QyxpQkFBaUI2RixtQkFBbUIsR0FBRzNJLCtDQUFRQSxDQUFnQjtJQUN0RSxNQUFNLENBQUM0SSxpQkFBaUJDLG1CQUFtQixHQUFHN0ksK0NBQVFBLENBQUM7SUFDdkQsTUFBTThJLGlCQUFpQjVJLDZDQUFNQSxDQUFpQjtJQUM5QyxNQUFNNkksc0JBQXNCN0ksNkNBQU1BLENBQStCO0lBQ2pFLE1BQU04SSxzQkFBc0I5SSw2Q0FBTUEsQ0FBd0I7SUFDMUQsTUFBTStJLG1CQUFtQi9JLDZDQUFNQSxDQUF3QjtJQUV2RCxnQ0FBZ0M7SUFDaEMsTUFBTWdKLG1CQUFtQjtRQUN2QlIsa0JBQWtCO0lBQ3BCO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1TLHVCQUF1QjtRQUMzQixJQUFJLENBQUM3QixLQUFLdkcsRUFBRSxFQUFFO1FBRWQsSUFBSTtZQUNGd0gsY0FBYztZQUNkLE1BQU1sSSx1REFBV0EsQ0FBQytJLGtCQUFrQixDQUFDOUIsS0FBS3ZHLEVBQUU7WUFFNUMseUJBQXlCO1lBQ3pCNEcsWUFBWSxFQUFFO1lBRWQsY0FBYztZQUNkZSxrQkFBa0I7WUFFbEIscUJBQXFCO1lBQ3JCeEgseUNBQUtBLENBQUNtSSxPQUFPLENBQUMsMEJBQTBCO2dCQUN0Q0MsYUFBYTtnQkFDYkMsVUFBVTtZQUNaO1lBRUEsbUNBQW1DO1lBQ25DLElBQUk5QixhQUFhO2dCQUNmQTtZQUNGO1FBQ0YsRUFBRSxPQUFPK0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1Q3RJLHlDQUFLQSxDQUFDc0ksS0FBSyxDQUFDLHNCQUFzQjtnQkFDaENGLGFBQWE7Z0JBQ2JDLFVBQVU7WUFDWjtRQUNGLFNBQVU7WUFDUmhCLGNBQWM7UUFDaEI7SUFDRjtJQUVBLHlFQUF5RTtJQUN6RSxNQUFNbUIsb0JBQW9CeEosNkNBQU1BLEVBQUNvSCxrQkFBQUEsS0FBS2xELFNBQVMsY0FBZGtELHNDQUFBQSxnQkFBZ0JxQyxLQUFLO0lBQ3RELE1BQU1DLG1CQUFtQjFKLDZDQUFNQSxFQUFDb0gsbUJBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCx1Q0FBQUEsaUJBQWdCaEQsSUFBSTtJQUVwRCxpQ0FBaUM7SUFDakNyRSxnREFBU0E7bUNBQUM7WUFDUixNQUFNNEo7NkRBQW9CO29CQUN4QixJQUFJO3dCQUNGLHlCQUF5Qjt3QkFDekIsTUFBTUMsZ0JBQWdCLE1BQU14SixpRUFBZ0JBLENBQUN5SixnQkFBZ0IsQ0FBQ3pDLEtBQUswQyxXQUFXO3dCQUM5RXhCLGFBQWFzQjt3QkFFYix3QkFBd0I7d0JBQ3hCLE1BQU1HLFNBQVMsTUFBTTNKLGlFQUFnQkEsQ0FBQzRKLGtCQUFrQixDQUFDNUMsS0FBSzBDLFdBQVc7d0JBQ3pFN0IsbUJBQW1COEI7d0JBRW5CLGlFQUFpRTt3QkFDakUsTUFBTUUsZ0JBQWdCRixPQUFPRyxNQUFNO3VGQUFDQyxDQUFBQSxRQUNsQ0EsTUFBTUMsT0FBTyxLQUFLLGFBQ2xCRCxNQUFNRSxJQUFJLEtBQUssV0FDZkYsTUFBTUcsV0FBVzs7d0JBR25CLElBQUlMLGNBQWNyRyxNQUFNLEdBQUcsR0FBRzs0QkFDNUI2RSxtQkFBbUJ3QixhQUFhLENBQUMsRUFBRSxDQUFDTSxHQUFHO3dCQUN6QyxPQUFPOzRCQUNMLG1EQUFtRDs0QkFDbkQ5QixtQkFBbUJtQixjQUFjSCxLQUFLO3dCQUN4QztvQkFDRixFQUFFLE9BQU9ILE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO29CQUNsRDtnQkFDRjs7WUFFQUs7UUFDRjtrQ0FBRztRQUFDdkMsS0FBSzBDLFdBQVc7S0FBQztJQUVyQix3REFBd0Q7SUFDeEQvSixnREFBU0E7bUNBQUM7WUFDUixJQUFJeUgsc0JBQXNCUSxnQkFBZ0JwRSxNQUFNLEdBQUcsR0FBRztnQkFDcEQsTUFBTTRHLGtCQUFrQnhDLGdCQUFnQnlDLElBQUk7K0RBQzFDTixDQUFBQSxRQUFTQSxNQUFNdEosRUFBRSxLQUFLMkcsc0JBQXNCMkMsTUFBTUMsT0FBTyxLQUFLOztnQkFFaEVqQyxzQkFBc0JxQyxDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQkQsR0FBRyxLQUFJO1lBQ2hELE9BQU87Z0JBQ0xwQyxzQkFBc0I7WUFDeEI7UUFDRjtrQ0FBRztRQUFDWDtRQUFvQlE7S0FBZ0I7SUFFeEMsNEJBQTRCO0lBQzVCakksZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTXlGO3VEQUFjO29CQUNsQkosWUFBWUssT0FBT0MsVUFBVSxHQUFHO2dCQUNsQzs7WUFFQUY7WUFDQUMsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7WUFDbEM7MkNBQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7O1FBQ3BEO2tDQUFHLEVBQUU7SUFFTCxnQ0FBZ0M7SUFDaEN6RixnREFBU0E7bUNBQUM7Z0JBQ29CcUgsaUJBQ0RBO1lBRDNCb0Msa0JBQWtCakUsT0FBTyxJQUFHNkIsa0JBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCxzQ0FBQUEsZ0JBQWdCcUMsS0FBSztZQUNqREMsaUJBQWlCbkUsT0FBTyxJQUFHNkIsbUJBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCx1Q0FBQUEsaUJBQWdCaEQsSUFBSTtRQUNqRDtrQ0FBRztTQUFDZ0QsbUJBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCx1Q0FBQUEsaUJBQWdCcUMsS0FBSztTQUFFckMsbUJBQUFBLEtBQUtsRCxTQUFTLGNBQWRrRCx1Q0FBQUEsaUJBQWdCaEQsSUFBSTtLQUFDO0lBRWhEckUsZ0RBQVNBO21DQUFDO1lBQ1IsK0NBQStDO1lBQy9DZ0ksZUFBZTtZQUNmRCxvQkFBb0I7WUFDcEJELFdBQVc7WUFFWCxnREFBZ0Q7WUFDaEQsSUFBSWdCLG9CQUFvQnRELE9BQU8sRUFBRTtnQkFDL0JzRCxvQkFBb0J0RCxPQUFPLENBQUNtRixLQUFLO2dCQUNqQzdCLG9CQUFvQnRELE9BQU8sR0FBRztZQUNoQztZQUNBLElBQUl1RCxvQkFBb0J2RCxPQUFPLEVBQUU7Z0JBQy9Cb0YsYUFBYTdCLG9CQUFvQnZELE9BQU87Z0JBQ3hDdUQsb0JBQW9CdkQsT0FBTyxHQUFHO1lBQ2hDO1lBQ0EsSUFBSXdELGlCQUFpQnhELE9BQU8sRUFBRTtnQkFDNUJvRixhQUFhNUIsaUJBQWlCeEQsT0FBTztnQkFDckN3RCxpQkFBaUJ4RCxPQUFPLEdBQUc7WUFDN0I7WUFFQXFGO1lBRUE7MkNBQU87b0JBQ0wsdUNBQXVDO29CQUN2QyxJQUFJL0Isb0JBQW9CdEQsT0FBTyxFQUFFO3dCQUMvQnNELG9CQUFvQnRELE9BQU8sQ0FBQ21GLEtBQUs7b0JBQ25DO29CQUNBLDRCQUE0QjtvQkFDNUIsSUFBSTVCLG9CQUFvQnZELE9BQU8sRUFBRTt3QkFDL0JvRixhQUFhN0Isb0JBQW9CdkQsT0FBTztvQkFDMUM7b0JBQ0EseUJBQXlCO29CQUN6QixJQUFJd0QsaUJBQWlCeEQsT0FBTyxFQUFFO3dCQUM1Qm9GLGFBQWE1QixpQkFBaUJ4RCxPQUFPO29CQUN2QztnQkFDRjs7UUFDRjtrQ0FBRztRQUFDNkIsS0FBS3ZHLEVBQUU7S0FBQztJQUVaLHVEQUF1RDtJQUN2RCxNQUFNZ0ssb0JBQW9CN0ssNkNBQU1BO21EQUFhLEtBQVE7O0lBQ3JENkssa0JBQWtCdEYsT0FBTyxHQUFHO1lBQzFCcUQ7U0FBQUEsMEJBQUFBLGVBQWVyRCxPQUFPLGNBQXRCcUQsOENBQUFBLHdCQUF3QmtDLGNBQWMsQ0FBQztZQUFFQyxVQUFVO1FBQVM7SUFDOUQ7SUFFQSwrREFBK0Q7SUFDL0QsTUFBTUMsbUJBQW1CaEwsNkNBQU1BLENBQUN3RCxTQUFTSSxNQUFNO0lBQy9DN0QsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSXlELFNBQVNJLE1BQU0sR0FBR29ILGlCQUFpQnpGLE9BQU8sRUFBRTtnQkFDOUN5RixpQkFBaUJ6RixPQUFPLEdBQUcvQixTQUFTSSxNQUFNO2dCQUMxQyxNQUFNcUgsWUFBWUM7eURBQVc7NEJBQzNCTDt5QkFBQUEsNkJBQUFBLGtCQUFrQnRGLE9BQU8sY0FBekJzRixpREFBQUEsZ0NBQUFBO29CQUNGO3dEQUFHO2dCQUNIOytDQUFPLElBQU1GLGFBQWFNOztZQUM1QjtZQUNBRCxpQkFBaUJ6RixPQUFPLEdBQUcvQixTQUFTSSxNQUFNO1FBQzVDO2tDQUFHO1FBQUNKLFNBQVNJLE1BQU07S0FBQztJQUVwQiw0RUFBNEU7SUFDNUUsTUFBTXVILGlCQUFpQm5MLDZDQUFNQSxDQUFDZ0Y7SUFDOUJqRixnREFBU0E7bUNBQUM7WUFDUixJQUFJaUYsZUFBZSxDQUFDbUcsZUFBZTVGLE9BQU8sRUFBRTtnQkFDMUMseUJBQXlCO2dCQUN6QixNQUFNMEYsWUFBWUM7eURBQVc7NEJBQzNCTDt5QkFBQUEsNkJBQUFBLGtCQUFrQnRGLE9BQU8sY0FBekJzRixpREFBQUEsZ0NBQUFBO29CQUNGO3dEQUFHO2dCQUNITSxlQUFlNUYsT0FBTyxHQUFHUDtnQkFDekI7K0NBQU8sSUFBTTJGLGFBQWFNOztZQUM1QjtZQUNBRSxlQUFlNUYsT0FBTyxHQUFHUDtRQUMzQjtrQ0FBRztRQUFDQTtLQUFZO0lBRWhCLE1BQU00RixlQUFlO1FBQ25CLElBQUk7WUFDRmpELFdBQVc7WUFDWCxNQUFNeUQsV0FBVyxNQUFNakwsdURBQVdBLENBQUNrTCxlQUFlLENBQUNqRSxLQUFLdkcsRUFBRSxFQUFFO2dCQUFFeUssT0FBTztZQUFHO1lBQ3hFN0QsWUFBWTJELFNBQVNHLElBQUksQ0FBQ0MsT0FBTyxLQUFLLCtCQUErQjtRQUN2RSxFQUFFLE9BQU9sQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzVDLFNBQVU7WUFDUjNCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTlCLG9CQUFvQjVGLGtEQUFXQTt3REFBQyxPQUFPd0w7WUFDM0MsSUFBSSxDQUFDQSxZQUFZMUYsSUFBSSxNQUFNNkIsU0FBUztZQUVwQyxNQUFNOEQsZ0JBQWdCdEUsS0FBS3ZHLEVBQUUsRUFBRSwwQkFBMEI7WUFDekQsTUFBTThLLFNBQVMsUUFBbUIsT0FBWDNJLEtBQUs0SSxHQUFHO1lBQy9CL0QsV0FBVztZQUVYLElBQUk7Z0JBQ0YscUNBQXFDO2dCQUNyQyxNQUFNZ0UsY0FBdUI7b0JBQzNCaEwsSUFBSThLO29CQUNKOUosTUFBTTtvQkFDTkQsU0FBUzZKO29CQUNUSyxhQUFhO29CQUNiM0ksV0FBVyxJQUFJSCxPQUFPK0ksV0FBVztvQkFDakNDLFdBQVcsSUFBSWhKLE9BQU8rSSxXQUFXO2dCQUNuQztnQkFFQXRFO29FQUFZd0UsQ0FBQUEsT0FBUTsrQkFBSUE7NEJBQU1KO3lCQUFZOztnQkFFMUMsNkRBQTZEO2dCQUM3RDlELGVBQWU7Z0JBQ2ZELG9CQUFvQjtnQkFFcEIsTUFBTW9FLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssUUFBUSxNQUFNLGNBQWM7Z0JBQzdFOUMsUUFBUStDLEdBQUcsQ0FBQyxtQkFBaUMsT0FBZEosZUFBYztnQkFFN0MsTUFBTSxJQUFJSztvRUFBUUMsQ0FBQUEsVUFBV3RCLFdBQVdzQixTQUFTTjs7Z0JBRWpELHlEQUF5RDtnQkFDekQsSUFBSTlFLEtBQUt2RyxFQUFFLEtBQUs2SyxlQUFlO29CQUM3Qm5DLFFBQVErQyxHQUFHLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBRUEvQyxRQUFRK0MsR0FBRyxDQUFDLDRCQUE0QmxGLEtBQUt2RyxFQUFFLEVBQUU7b0JBQUU4QixTQUFTOEk7b0JBQWFnQixXQUFXO2dCQUFLO2dCQUV6RixzQ0FBc0M7Z0JBQ3RDLE1BQU1yQixXQUFXLE1BQU1qTCx1REFBV0EsQ0FBQ3VNLFdBQVcsQ0FBQ3RGLEtBQUt2RyxFQUFFLEVBQUU7b0JBQ3REOEIsU0FBUzhJO29CQUNUZ0IsV0FBVztnQkFDYjtnQkFFQWxELFFBQVErQyxHQUFHLENBQUMsOEJBQThCbEI7Z0JBRTFDLG9EQUFvRDtnQkFDcEQsSUFBSWhFLEtBQUt2RyxFQUFFLEtBQUs2SyxlQUFlO29CQUM3Qm5DLFFBQVErQyxHQUFHLENBQUM7b0JBQ1o7Z0JBQ0Y7Z0JBRUEseURBQXlEO2dCQUN6RCxJQUFJbEIsU0FBU3ZLLEVBQUUsRUFBRTtvQkFDZjRHO3dFQUFZd0UsQ0FBQUEsT0FBUUEsS0FBS1UsR0FBRztnRkFBQ0MsQ0FBQUEsTUFDM0JBLElBQUkvTCxFQUFFLEtBQUs4SyxTQUFTO3dDQUFFLEdBQUdpQixHQUFHO3dDQUFFL0wsSUFBSXVLLFNBQVN2SyxFQUFFO29DQUFDLElBQUkrTDs7O2dCQUV0RDtnQkFFQSwyQkFBMkI7Z0JBQzNCLE1BQU1DLGVBQWVuQjtZQUV2QixFQUFFLE9BQU9wQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtnQkFDekMscUVBQXFFO2dCQUNyRSxJQUFJbEMsS0FBS3ZHLEVBQUUsS0FBSzZLLGVBQWU7b0JBQzdCakU7d0VBQVl3RSxDQUFBQSxPQUFRQSxLQUFLL0IsTUFBTTtnRkFBQzBDLENBQUFBLE1BQU9BLElBQUkvTCxFQUFFLEtBQUs4Szs7O29CQUNsRG1CLE1BQU07Z0JBQ1I7WUFDRixTQUFVO2dCQUNSLDJEQUEyRDtnQkFDM0QsSUFBSTFGLEtBQUt2RyxFQUFFLEtBQUs2SyxlQUFlO29CQUM3QjdELFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO3VEQUFHO1FBQUNEO1FBQVNSLEtBQUt2RyxFQUFFO0tBQUM7SUFFckIsTUFBTWdNLGlCQUFpQixPQUFPRTtRQUM1QixNQUFNQyxlQUFlRCxrQkFBa0IzRixLQUFLdkcsRUFBRTtRQUM5QzBJLFFBQVErQyxHQUFHLENBQUMsNkJBQTZCVTtRQUV6QyxzREFBc0Q7UUFDdEQsSUFBSUQsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjtZQUNoRHhELFFBQVErQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsdURBQXVEO1FBQ3ZELDBEQUEwRDtRQUUxRCwrQ0FBK0M7UUFDL0MsSUFBSXpELG9CQUFvQnRELE9BQU8sRUFBRTtZQUMvQnNELG9CQUFvQnRELE9BQU8sQ0FBQ21GLEtBQUs7UUFDbkM7UUFDQSxJQUFJNUIsb0JBQW9CdkQsT0FBTyxFQUFFO1lBQy9Cb0YsYUFBYTdCLG9CQUFvQnZELE9BQU87UUFDMUM7UUFDQSxJQUFJd0QsaUJBQWlCeEQsT0FBTyxFQUFFO1lBQzVCb0YsYUFBYTVCLGlCQUFpQnhELE9BQU87UUFDdkM7UUFFQSxJQUFJMEgsMEJBQTBCO1FBRTlCLDJEQUEyRDtRQUMzRCxJQUFJQyxpQkFBaUI7UUFDckIsTUFBTUMseUJBQXlCLENBQUN4SztZQUM5QiwrQ0FBK0M7WUFDL0MsSUFBSW9LLGtCQUFrQjNGLEtBQUt2RyxFQUFFLEtBQUtrTSxnQkFBZ0I7Z0JBQ2hEO1lBQ0Y7WUFFQSxNQUFNbkIsTUFBTTVJLEtBQUs0SSxHQUFHO1lBQ3BCLElBQUk5QyxvQkFBb0J2RCxPQUFPLEVBQUU7Z0JBQy9Cb0YsYUFBYTdCLG9CQUFvQnZELE9BQU87WUFDMUM7WUFFQSxrRUFBa0U7WUFDbEUsSUFBSXFHLE1BQU1zQixpQkFBaUIsS0FBSztnQkFDOUJwRixvQkFBb0JuRjtnQkFDcEJ1SyxpQkFBaUJ0QjtZQUNuQixPQUFPO2dCQUNMLGlDQUFpQztnQkFDakM5QyxvQkFBb0J2RCxPQUFPLEdBQUcyRixXQUFXO29CQUN2QywrREFBK0Q7b0JBQy9ELElBQUksQ0FBQzZCLGtCQUFrQjNGLEtBQUt2RyxFQUFFLEtBQUtrTSxnQkFBZ0I7d0JBQ2pEakYsb0JBQW9CbkY7d0JBQ3BCdUssaUJBQWlCbEssS0FBSzRJLEdBQUc7b0JBQzNCO2dCQUNGLEdBQUc7WUFDTDtRQUNGO1FBRUEsTUFBTXdCLFlBQVksQ0FBQzdCO1lBQ2pCaEMsUUFBUStDLEdBQUcsQ0FBQyx1QkFBdUJmO1lBRW5DLGdEQUFnRDtZQUNoRCxJQUFJd0Isa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjtnQkFDaER4RCxRQUFRK0MsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxPQUFRZixLQUFLOEIsS0FBSztnQkFDaEIsS0FBSztvQkFDSDlELFFBQVErQyxHQUFHLENBQUM7b0JBQ1pXLDBCQUEwQjtvQkFDMUJuRixvQkFBb0I7b0JBQ3BCO2dCQUNGLEtBQUs7b0JBQ0htRiwyQkFBMkIxQixLQUFLQSxJQUFJO29CQUNwQzRCLHVCQUF1QkY7b0JBQ3ZCO2dCQUNGLEtBQUs7b0JBQ0gxRCxRQUFRK0MsR0FBRyxDQUFDLG9CQUFvQmYsS0FBS0EsSUFBSTtvQkFDekM7Z0JBQ0YsS0FBSztvQkFDSGhDLFFBQVErQyxHQUFHLENBQUMsZ0NBQWdDVztvQkFDNUMsc0NBQXNDO29CQUN0QyxJQUFJbkUsb0JBQW9CdkQsT0FBTyxFQUFFO3dCQUMvQm9GLGFBQWE3QixvQkFBb0J2RCxPQUFPO29CQUMxQztvQkFDQSx1REFBdUQ7b0JBQ3ZELElBQUl3RCxpQkFBaUJ4RCxPQUFPLEVBQUU7d0JBQzVCb0YsYUFBYTVCLGlCQUFpQnhELE9BQU87b0JBQ3ZDO29CQUNBLDZEQUE2RDtvQkFDN0QsSUFBSSxDQUFDd0gsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjs0QkFFM0N4Qjt3QkFETixNQUFNK0IsZUFBd0I7NEJBQzVCek0sSUFBSTBLLEVBQUFBLGFBQUFBLEtBQUtBLElBQUksY0FBVEEsaUNBQUFBLFdBQVdnQyxhQUFhLEtBQUksT0FBa0IsT0FBWHZLLEtBQUs0SSxHQUFHOzRCQUMvQy9KLE1BQU07NEJBQ05ELFNBQVNxTDs0QkFDVG5CLGFBQWE7NEJBQ2IzSSxXQUFXLElBQUlILE9BQU8rSSxXQUFXOzRCQUNqQ0MsV0FBVyxJQUFJaEosT0FBTytJLFdBQVc7d0JBQ25DO3dCQUVBdEUsWUFBWXdFLENBQUFBLE9BQVE7bUNBQUlBO2dDQUFNcUI7NkJBQWE7d0JBQzNDeEYsb0JBQW9CO3dCQUNwQkMsZUFBZTtvQkFDakI7b0JBQ0EsSUFBSWMsb0JBQW9CdEQsT0FBTyxFQUFFO3dCQUMvQnNELG9CQUFvQnRELE9BQU8sQ0FBQ21GLEtBQUs7b0JBQ25DO29CQUNBO2dCQUNGO29CQUNFbkIsUUFBUStDLEdBQUcsQ0FBQyxzQkFBc0JmLEtBQUs4QixLQUFLO1lBQ2hEO1FBQ0Y7UUFFQSxNQUFNRyxVQUFVLE9BQU9sRTtnQkFzQmhCQTtZQXJCTEMsUUFBUUQsS0FBSyxDQUFDLGlCQUFpQkE7WUFFL0IsaURBQWlEO1lBQ2pELElBQUlSLG9CQUFvQnZELE9BQU8sRUFBRTtnQkFDL0JvRixhQUFhN0Isb0JBQW9CdkQsT0FBTztZQUMxQztZQUNBLElBQUl3RCxpQkFBaUJ4RCxPQUFPLEVBQUU7Z0JBQzVCb0YsYUFBYTVCLGlCQUFpQnhELE9BQU87WUFDdkM7WUFFQSwyQ0FBMkM7WUFDM0MsSUFBSSxDQUFDd0gsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjtnQkFDakRoRixlQUFlO2dCQUNmRCxvQkFBb0I7WUFDdEI7WUFFQSxJQUFJZSxvQkFBb0J0RCxPQUFPLEVBQUU7Z0JBQy9Cc0Qsb0JBQW9CdEQsT0FBTyxDQUFDbUYsS0FBSztZQUNuQztZQUVBLDJHQUEyRztZQUMzRyxJQUFJLEdBQUNwQixpQkFBQUEsTUFBTTNHLE9BQU8sY0FBYjJHLHFDQUFBQSxlQUFlbUUsUUFBUSxDQUFDLGVBQWN6SSxlQUFnQixFQUFDK0gsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGNBQWEsR0FBSTtnQkFDekd4RCxRQUFRK0MsR0FBRyxDQUFDO2dCQUNaLElBQUk7b0JBQ0YsTUFBTTFCO2dCQUNSLEVBQUUsT0FBTzhDLGFBQWE7b0JBQ3BCbkUsUUFBUUQsS0FBSyxDQUFDLDhCQUE4Qm9FO2dCQUM1QywrREFBK0Q7Z0JBQ2pFO1lBQ0Y7UUFDRjtRQUVBLElBQUk7WUFDRix3RUFBd0U7WUFDeEUsSUFBSVgsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGdCQUFnQjtnQkFDaER4RCxRQUFRK0MsR0FBRyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSwrQkFBK0I7WUFDL0IsTUFBTXFCLGFBQWEsTUFBTXhOLHVEQUFXQSxDQUFDeU4sc0JBQXNCLENBQUNaLGNBQWNJLFdBQVdJO1lBQ3JGM0Usb0JBQW9CdEQsT0FBTyxHQUFHb0k7WUFFOUIsMkVBQTJFO1lBQzNFNUUsaUJBQWlCeEQsT0FBTyxHQUFHMkYsV0FBVztnQkFDcEMsZ0dBQWdHO2dCQUNoRyxJQUFJbEcsZUFBZTZELG9CQUFvQnRELE9BQU8sSUFBSyxFQUFDd0gsa0JBQWtCM0YsS0FBS3ZHLEVBQUUsS0FBS2tNLGNBQWEsR0FBSTtvQkFDakd4RCxRQUFRK0MsR0FBRyxDQUFDO29CQUNaa0IsUUFBUSxJQUFJSyxNQUFNO2dCQUNwQjtZQUNGLEdBQUc7UUFFTCxFQUFFLE9BQU92RSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELDJDQUEyQztZQUMzQyxJQUFJLENBQUN5RCxrQkFBa0IzRixLQUFLdkcsRUFBRSxLQUFLa00sZ0JBQWdCO2dCQUNqRGhGLGVBQWU7Z0JBQ2YrRSxNQUFNO1lBQ1I7UUFDRjtJQUNGO0lBRUEsSUFBSXBGLFNBQVM7UUFDWCxxQkFDRSw4REFBQ25HO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJckIsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNad00sTUFBTUMsSUFBSSxDQUFDO3dCQUFFbkssUUFBUTtvQkFBRSxHQUFHK0ksR0FBRyxDQUFDLENBQUNxQixHQUFHQyxrQkFDakMsOERBQUMxTTs0QkFBWUQsV0FBVTs7OENBQ3JCLDhEQUFDQztvQ0FBSUQsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVOzs7Ozs7c0RBQ2YsOERBQUNDOzRDQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7OzJCQUpUMk07Ozs7Ozs7Ozs7Ozs7Ozs7SUFXcEI7SUFFQSxxQkFDRSw4REFBQzFNO1FBQUlELFdBQVU7OzBCQUViLDhEQUFDQztnQkFBSUQsV0FBVyw4SUFDYixPQUQySjZELFdBQVcsYUFBYTs7a0NBRXBMLDhEQUFDNUQ7d0JBQUlELFdBQVU7OzRCQUVaNkQsWUFBWWtDLHdCQUNYLDhEQUFDaEgseURBQU1BO2dDQUNMbUUsU0FBUTtnQ0FDUjBKLE1BQUs7Z0NBQ0xoSCxTQUFTRztnQ0FDVC9GLFdBQVU7Z0NBQ1Y2TSxjQUFXOzBDQUVYLDRFQUFDMU4sMEdBQVNBO29DQUFDYSxXQUFVOzs7Ozs7Ozs7OzswQ0FLekIsOERBQUNDOzBDQUNDLDRFQUFDOEM7b0NBQUcvQyxXQUFVOzhDQUF5QjhGLEVBQUFBLG1CQUFBQSxLQUFLbEQsU0FBUyxjQUFka0QsdUNBQUFBLGlCQUFnQmhELElBQUksS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSW5FLDhEQUFDN0M7d0JBQUlELFdBQVU7OzRCQUVaNkQsWUFBWW1DLGlDQUNYLDhEQUFDakgseURBQU1BO2dDQUNMbUUsU0FBUTtnQ0FDUjBKLE1BQUs7Z0NBQ0xoSCxTQUFTSTtnQ0FDVGhHLFdBQVU7Z0NBQ1Y2TSxjQUFXOzBDQUVYLDRFQUFDek4sMEdBQUlBO29DQUFDWSxXQUFVOzs7Ozs7Ozs7OzswQ0FLcEIsOERBQUNqQix5REFBTUE7Z0NBQ0xtRSxTQUFRO2dDQUNSMEosTUFBSztnQ0FDTGhILFNBQVM4QjtnQ0FDVGpFLFVBQVVxRCxjQUFjNUUsU0FBU0ksTUFBTSxLQUFLO2dDQUM1Q3RDLFdBQVU7O2tEQUVWLDhEQUFDWCwwR0FBU0E7d0NBQUNXLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkMsOERBQUNDO2dCQUNDRCxXQUFVO2dCQUNWeUMsT0FBTztvQkFDTHFLLFlBQVlsRyxxQkFDUixnRUFBbUYsT0FBbkJBLG9CQUFtQixPQUNuRjtvQkFDSm1HLGdCQUFnQm5HLHFCQUFxQixVQUFVO29CQUMvQ29HLG9CQUFvQnBHLHFCQUFxQixXQUFXO29CQUNwRHFHLGtCQUFrQnJHLHFCQUFxQixjQUFjO2dCQUN2RDs7b0JBR0NoRSwyQkFDQyw4REFBQ0Q7d0JBQ0NDLFdBQVdBO3dCQUNYQyxjQUFjaUQsS0FBS2pELFlBQVk7d0JBQy9CdkIsaUJBQWlCQTs7Ozs7O29CQUtwQnNCLDJCQUNDLDhEQUFDUTt3QkFDQ1IsV0FBV0E7d0JBQ1hTLGFBQWFuQixTQUFTSSxNQUFNLEdBQUc7d0JBQy9CaEIsaUJBQWlCQTs7Ozs7O29CQUtwQlksU0FBU0ksTUFBTSxHQUFHLG1CQUNqQiw4REFBQ3JDO3dCQUFJRCxXQUFVOzs0QkFDWmtDLFNBQVNtSixHQUFHLENBQUMsQ0FBQ2hLLHdCQUNiLDhEQUFDRDtvQ0FFQ0MsU0FBU0E7b0NBQ1RDLGlCQUFpQkE7b0NBQ2pCQyxlQUFlNkcsaUJBQWlCbkUsT0FBTzttQ0FIbEM1QyxRQUFROUIsRUFBRTs7Ozs7NEJBUWxCbUUsNkJBQ0MsOERBQUM1QjtnQ0FDQ0Msa0JBQWtCQTtnQ0FDbEJULGlCQUFpQkE7Z0NBQ2pCQyxlQUFlNkcsaUJBQWlCbkUsT0FBTzs7Ozs7Ozs7Ozs7O2tDQU0vQyw4REFBQ2hFO3dCQUFJcUYsS0FBS2dDOzs7Ozs7Ozs7Ozs7MEJBSVosOERBQUMvRDtnQkFDQ0MsZUFBZWU7Z0JBQ2ZkLFVBQVU2QyxXQUFXNUM7Z0JBQ3JCbkMsZUFBZTZHLGlCQUFpQm5FLE9BQU87Z0JBQ3ZDUCxhQUFhQTs7Ozs7OzBCQUlmLDhEQUFDL0QsNkRBQWNBO2dCQUNidU4sUUFBUWpHO2dCQUNSa0csU0FBUyxJQUFNakcsa0JBQWtCO2dCQUNqQ2tHLFdBQVd6RjtnQkFDWHBHLGVBQWVxQixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdFLElBQUksT0FBSWdELG1CQUFBQSxLQUFLbEQsU0FBUyxjQUFka0QsdUNBQUFBLGlCQUFnQmhELElBQUk7Z0JBQ3RERCxjQUFjWCxTQUFTSSxNQUFNO2dCQUM3QitLLFdBQVd2Rzs7Ozs7Ozs7Ozs7O0FBSW5COztBQUVBakIsY0FBY3lILFdBQVcsR0FBRztBQUVIIiwic291cmNlcyI6WyJFOlxcYmVzc3RpZWt1XFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxjaGF0XFxjaGF0LWludGVyZmFjZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlQ2FsbGJhY2ssIG1lbW8gfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IENoYXQsIE1lc3NhZ2UsIFNTRUV2ZW50IH0gZnJvbSAnQC90eXBlcy9jaGF0JztcclxuaW1wb3J0IHsgQ2hhcmFjdGVyQXNzZXQgfSBmcm9tICdAL3R5cGVzL2NoYXJhY3Rlcic7XHJcbmltcG9ydCB7IGNoYXRTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9jaGF0JztcclxuaW1wb3J0IHsgY2hhcmFjdGVyU2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvY2hhcmFjdGVyJztcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0JztcclxuXHJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJztcclxuaW1wb3J0IHsgU2VuZCwgQXJyb3dMZWZ0LCBJbmZvLCBSb3RhdGVDY3csIE1heGltaXplMiwgTWluaW1pemUyIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2VUb05vdyB9IGZyb20gJ2RhdGUtZm5zJztcclxuaW1wb3J0IHsgaWQgfSBmcm9tICdkYXRlLWZucy9sb2NhbGUnO1xyXG5pbXBvcnQgUmVhY3RNYXJrZG93biBmcm9tICdyZWFjdC1tYXJrZG93bic7XHJcbmltcG9ydCByZW1hcmtHZm0gZnJvbSAncmVtYXJrLWdmbSc7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJztcclxuaW1wb3J0IHsgUmVzZXRDaGF0TW9kYWwgfSBmcm9tICcuL3Jlc2V0LWNoYXQtbW9kYWwnO1xyXG5pbXBvcnQgeyBFbW9qaVBpY2tlciB9IGZyb20gJy4vZW1vamktcGlja2VyJztcclxuXHJcbi8vIFNpbXBsZSBhdmF0YXIgY29tcG9uZW50IHRoYXQgYWx3YXlzIHVzZXMgc3RhdGljIGltYWdlXHJcbmNvbnN0IFNpbXBsZUF2YXRhciA9ICh7IHNyYywgYWx0LCBjbGFzc05hbWUgfToge1xyXG4gIHNyYzogc3RyaW5nIHwgbnVsbDtcclxuICBhbHQ6IHN0cmluZztcclxuICBjbGFzc05hbWU/OiBzdHJpbmc7XHJcbn0pID0+IHtcclxuICBpZiAoIXNyYykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BiZy1iZXN0aWVrdS1wcmltYXJ5LzEwIGZsZXggc2l6ZS1mdWxsIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWZ1bGwgdGV4dC14cyB0ZXh0LWJlc3RpZWt1LXByaW1hcnkgJHtjbGFzc05hbWUgfHwgJyd9YH0+XHJcbiAgICAgICAge2FsdD8uY2hhckF0KDApPy50b1VwcGVyQ2FzZSgpIHx8ICdDJ31cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxpbWdcclxuICAgICAgc3JjPXtzcmN9XHJcbiAgICAgIGFsdD17YWx0fVxyXG4gICAgICBjbGFzc05hbWU9e2Bhc3BlY3Qtc3F1YXJlIHNpemUtZnVsbCBvYmplY3QtY292ZXIgJHtjbGFzc05hbWUgfHwgJyd9YH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmludGVyZmFjZSBDaGF0SW50ZXJmYWNlUHJvcHMge1xyXG4gIGNoYXQ6IENoYXQ7XHJcbiAgb25CYWNrPzogKCkgPT4gdm9pZDtcclxuICBvblRvZ2dsZVByb2ZpbGU/OiAoKSA9PiB2b2lkO1xyXG4gIG9uQ2hhdFJlc2V0PzogKCkgPT4gdm9pZDtcclxuICBzZWxlY3RlZEJhY2tncm91bmQ/OiBzdHJpbmcgfCBudWxsO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgTWVzc2FnZUNvbnRlbnRQcm9wcyB7XHJcbiAgY29udGVudDogc3RyaW5nO1xyXG4gIHJvbGU6ICd1c2VyJyB8ICdhc3Npc3RhbnQnO1xyXG59XHJcblxyXG5jb25zdCBNZXNzYWdlQ29udGVudCA9IG1lbW8oKHsgY29udGVudCwgcm9sZSB9OiBNZXNzYWdlQ29udGVudFByb3BzKSA9PiB7XHJcbiAgaWYgKHJvbGUgPT09ICd1c2VyJykge1xyXG4gICAgLy8gRm9yIHVzZXIgbWVzc2FnZXMsIGp1c3QgZGlzcGxheSBhcyBwbGFpbiB0ZXh0IHdpdGggbGluZSBicmVha3NcclxuICAgIHJldHVybiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHdoaXRlc3BhY2UtcHJlLXdyYXAgbGVhZGluZy1yZWxheGVkXCI+e2NvbnRlbnR9PC9wPjtcclxuICB9XHJcblxyXG4gIC8vIEZvciBBSSBtZXNzYWdlcywgcmVuZGVyIGFzIE1hcmtkb3duXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWQgcHJvc2UgcHJvc2Utc20gbWF4LXctbm9uZSBkYXJrOnByb3NlLWludmVydFwiPlxyXG4gICAgICA8UmVhY3RNYXJrZG93blxyXG4gICAgICAgIHJlbWFya1BsdWdpbnM9e1tyZW1hcmtHZm1dfVxyXG4gICAgICAgIGNvbXBvbmVudHM9e3tcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBwYXJhZ3JhcGggc3R5bGluZ1xyXG4gICAgICAgICAgcDogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0yIGxhc3Q6bWItMCBsZWFkaW5nLXJlbGF4ZWRcIj57Y2hpbGRyZW59PC9wPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBlbXBoYXNpcyAoaXRhbGljKVxyXG4gICAgICAgICAgZW06ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPGVtIGNsYXNzTmFtZT1cIml0YWxpYyB0ZXh0LWluaGVyaXRcIj57Y2hpbGRyZW59PC9lbT5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgc3Ryb25nIChib2xkKVxyXG4gICAgICAgICAgc3Ryb25nOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxzdHJvbmcgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWluaGVyaXRcIj57Y2hpbGRyZW59PC9zdHJvbmc+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIGNvZGUgYmxvY2tzXHJcbiAgICAgICAgICBjb2RlOiAoeyBjaGlsZHJlbiwgY2xhc3NOYW1lIH0pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgaXNJbmxpbmUgPSAhY2xhc3NOYW1lO1xyXG4gICAgICAgICAgICBpZiAoaXNJbmxpbmUpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPVwiYmctbXV0ZWQgcHgtMS41IHB5LTAuNSByb3VuZGVkIHRleHQteHMgZm9udC1tb25vXCI+XHJcbiAgICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgICAgIDwvY29kZT5cclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPVwiYmxvY2sgYmctbXV0ZWQgcC0zIHJvdW5kZWQtbGcgdGV4dC14cyBmb250LW1vbm8gb3ZlcmZsb3cteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgICAgPC9jb2RlPlxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBsaXN0c1xyXG4gICAgICAgICAgdWw6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtZGlzYyBsaXN0LWluc2lkZSBtYi0yIHNwYWNlLXktMVwiPntjaGlsZHJlbn08L3VsPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIG9sOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxvbCBjbGFzc05hbWU9XCJsaXN0LWRlY2ltYWwgbGlzdC1pbnNpZGUgbWItMiBzcGFjZS15LTFcIj57Y2hpbGRyZW59PC9vbD5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICBsaTogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwidGV4dC1zbVwiPntjaGlsZHJlbn08L2xpPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBibG9ja3F1b3Rlc1xyXG4gICAgICAgICAgYmxvY2txdW90ZTogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8YmxvY2txdW90ZSBjbGFzc05hbWU9XCJib3JkZXItbC00IGJvcmRlci1tdXRlZCBwbC00IGl0YWxpYyBteS0yXCI+XHJcbiAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICA8L2Jsb2NrcXVvdGU+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7Y29udGVudH1cclxuICAgICAgPC9SZWFjdE1hcmtkb3duPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5pbnRlcmZhY2UgTWVzc2FnZUl0ZW1Qcm9wcyB7XHJcbiAgbWVzc2FnZTogTWVzc2FnZTtcclxuICBwcm9maWxlSW1hZ2VVcmw6IHN0cmluZyB8IG51bGw7XHJcbiAgY2hhcmFjdGVyTmFtZT86IHN0cmluZztcclxufVxyXG5cclxuY29uc3QgTWVzc2FnZUl0ZW0gPSBtZW1vKCh7IG1lc3NhZ2UsIHByb2ZpbGVJbWFnZVVybCwgY2hhcmFjdGVyTmFtZSB9OiBNZXNzYWdlSXRlbVByb3BzKSA9PiB7XHJcbiAgY29uc3QgZm9ybWF0VGltZSA9IHVzZUNhbGxiYWNrKChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHJldHVybiBmb3JtYXREaXN0YW5jZVRvTm93KG5ldyBEYXRlKGRhdGVTdHJpbmcpLCB7IGFkZFN1ZmZpeDogdHJ1ZSwgbG9jYWxlOiBpZCB9KTtcclxuICAgIH0gY2F0Y2gge1xyXG4gICAgICByZXR1cm4gJyc7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWVuZCBnYXAtMiAke21lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfVxyXG4gICAgPlxyXG4gICAgICB7LyogQ2hhcmFjdGVyIEF2YXRhciBmb3IgYXNzaXN0YW50IG1lc3NhZ2VzICovfVxyXG4gICAgICB7bWVzc2FnZS5yb2xlID09PSAnYXNzaXN0YW50JyAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IG1iLTEgcmVsYXRpdmUgZmxleCBzaHJpbmstMCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1mdWxsXCI+XHJcbiAgICAgICAgICA8U2ltcGxlQXZhdGFyXHJcbiAgICAgICAgICAgIHNyYz17cHJvZmlsZUltYWdlVXJsfVxyXG4gICAgICAgICAgICBhbHQ9e2NoYXJhY3Rlck5hbWUgfHwgJ0NoYXJhY3Rlcid9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT17YG1heC13LVs3MCVdIHJvdW5kZWQtMnhsIHAtNCBzaGFkb3ctc20gJHttZXNzYWdlLnJvbGUgPT09ICd1c2VyJ1xyXG4gICAgICAgICAgICA/ICdiZy1iZXN0aWVrdS1wcmltYXJ5IHRleHQtd2hpdGUgcm91bmRlZC1ici1tZCdcclxuICAgICAgICAgICAgOiAnYmctd2hpdGUgZGFyazpiZy1tdXRlZCBib3JkZXIgcm91bmRlZC1ibC1tZCdcclxuICAgICAgICAgIH1gfVxyXG4gICAgICA+XHJcbiAgICAgICAgPE1lc3NhZ2VDb250ZW50IGNvbnRlbnQ9e21lc3NhZ2UuY29udGVudH0gcm9sZT17bWVzc2FnZS5yb2xlfSAvPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG10LTIgJHttZXNzYWdlLnJvbGUgPT09ICd1c2VyJyA/ICd0ZXh0LXdoaXRlLzcwJyA6ICd0ZXh0LW11dGVkLWZvcmVncm91bmQnXHJcbiAgICAgICAgICB9YH0+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XHJcbiAgICAgICAgICAgIHtmb3JtYXRUaW1lKG1lc3NhZ2UuY3JlYXRlZEF0KX1cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICAgIHttZXNzYWdlLnJvbGUgPT09ICd1c2VyJyAmJiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTEgYmctd2hpdGUvNzAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC0xIGJnLXdoaXRlLzcwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5pbnRlcmZhY2UgU3RyZWFtaW5nTWVzc2FnZVByb3BzIHtcclxuICBzdHJlYW1pbmdNZXNzYWdlOiBzdHJpbmc7XHJcbiAgcHJvZmlsZUltYWdlVXJsOiBzdHJpbmcgfCBudWxsO1xyXG4gIGNoYXJhY3Rlck5hbWU/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IFN0cmVhbWluZ01lc3NhZ2UgPSBtZW1vKCh7IHN0cmVhbWluZ01lc3NhZ2UsIHByb2ZpbGVJbWFnZVVybCwgY2hhcmFjdGVyTmFtZSB9OiBTdHJlYW1pbmdNZXNzYWdlUHJvcHMpID0+IHtcclxuICBjb25zdCBbdGhpbmtpbmdUZXh0LCBzZXRUaGlua2luZ1RleHRdID0gUmVhY3QudXNlU3RhdGUoJ0JlcnBpa2lyJyk7XHJcblxyXG4gIC8vIEN5Y2xlIHRocm91Z2ggdGhpbmtpbmcgbWVzc2FnZXMgd2hlbiBub3Qgc3RyZWFtaW5nIGFjdHVhbCBjb250ZW50XHJcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghc3RyZWFtaW5nTWVzc2FnZSkge1xyXG4gICAgICBjb25zdCBtZXNzYWdlcyA9IFsnQmVycGlraXInLCAnTWVyZW51bmdrYW4nLCAnTWVtcGVydGltYmFuZ2thbicsICdNZW1pa2lya2FuIGphd2FiYW4nXTtcclxuICAgICAgbGV0IGluZGV4ID0gMDtcclxuXHJcbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICAgIGluZGV4ID0gKGluZGV4ICsgMSkgJSBtZXNzYWdlcy5sZW5ndGg7XHJcbiAgICAgICAgc2V0VGhpbmtpbmdUZXh0KG1lc3NhZ2VzW2luZGV4XSk7XHJcbiAgICAgIH0sIDIwMDApO1xyXG5cclxuICAgICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xyXG4gICAgfVxyXG4gIH0sIFtzdHJlYW1pbmdNZXNzYWdlXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIGdhcC0yIGp1c3RpZnktc3RhcnRcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IG1iLTEgcmVsYXRpdmUgZmxleCBzaHJpbmstMCBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1mdWxsXCI+XHJcbiAgICAgICAgPFNpbXBsZUF2YXRhclxyXG4gICAgICAgICAgc3JjPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICBhbHQ9e2NoYXJhY3Rlck5hbWUgfHwgJ0NoYXJhY3Rlcid9XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LVs3MCVdIHJvdW5kZWQtMnhsIHJvdW5kZWQtYmwtbWQgcC00IGJnLXdoaXRlIGRhcms6YmctbXV0ZWQgYm9yZGVyIHNoYWRvdy1zbVwiPlxyXG4gICAgICAgIHtzdHJlYW1pbmdNZXNzYWdlID8gKFxyXG4gICAgICAgICAgPE1lc3NhZ2VDb250ZW50IGNvbnRlbnQ9e3N0cmVhbWluZ01lc3NhZ2V9IHJvbGU9XCJhc3Npc3RhbnRcIiAvPlxyXG4gICAgICAgICkgOiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e3RoaW5raW5nVGV4dH08L3NwYW4+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTEgbWwtMlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1iZXN0aWVrdS1wcmltYXJ5IHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiIC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJlc3RpZWt1LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjFzJyB9fSAvPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1iZXN0aWVrdS1wcmltYXJ5IHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMC4ycycgfX0gLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtMSBiZy1iZXN0aWVrdS1wcmltYXJ5IHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAgICB7c3RyZWFtaW5nTWVzc2FnZSA/ICdNZW5nZXRpay4uLicgOiAnU2VkYW5nIG1lbWlraXJrYW4gcmVzcG9ucy4uLid9XHJcbiAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuaW50ZXJmYWNlIFdlbGNvbWVNZXNzYWdlUHJvcHMge1xyXG4gIGNoYXJhY3RlcjogYW55O1xyXG4gIG1lc3NhZ2VDb3VudDogbnVtYmVyO1xyXG4gIHByb2ZpbGVJbWFnZVVybDogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuY29uc3QgV2VsY29tZU1lc3NhZ2UgPSBtZW1vKCh7IGNoYXJhY3RlciwgbWVzc2FnZUNvdW50LCBwcm9maWxlSW1hZ2VVcmwgfTogV2VsY29tZU1lc3NhZ2VQcm9wcykgPT4ge1xyXG4gIGlmICghY2hhcmFjdGVyKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktOCBweC00IHNwYWNlLXktNlwiPlxyXG4gICAgICB7LyogQ2hhcmFjdGVyIFByb2ZpbGUgU2VjdGlvbiAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWItNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgbXgtYXV0byByaW5nLTQgcmluZy1iZXN0aWVrdS1wcmltYXJ5LzIwIHJlbGF0aXZlIGZsZXggc2hyaW5rLTAgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgICA8U2ltcGxlQXZhdGFyXHJcbiAgICAgICAgICAgICAgc3JjPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICAgICAgYWx0PXtjaGFyYWN0ZXIubmFtZX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgey8qIE9ubGluZSBpbmRpY2F0b3IgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xIHJpZ2h0LTEvMiB0cmFuc2Zvcm0gdHJhbnNsYXRlLXgtNiB3LTQgaC00IGJnLWdyZWVuLTUwMCBib3JkZXItMiBib3JkZXItYmFja2dyb3VuZCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBtYi0yXCI+e2NoYXJhY3Rlci5uYW1lfTwvaDI+XHJcblxyXG4gICAgICAgIHtjaGFyYWN0ZXIudGl0bGUgJiYgKFxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTMgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAge2NoYXJhY3Rlci50aXRsZX1cclxuICAgICAgICAgIDwvcD5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIG1iLTRcIj5cclxuICAgICAgICAgIHtjaGFyYWN0ZXIuc3RvcnlNb2RlICYmIChcclxuICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImJnLWJlc3RpZWt1LXByaW1hcnkgdGV4dC13aGl0ZVwiPlxyXG4gICAgICAgICAgICAgIE1vZGUgQ2VyaXRhXHJcbiAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwiY2FwaXRhbGl6ZVwiPlxyXG4gICAgICAgICAgICB7Y2hhcmFjdGVyLnN0YXR1c31cclxuICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTQgdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgIDxzcGFuPnttZXNzYWdlQ291bnR9IHBlc2FuPC9zcGFuPlxyXG4gICAgICAgICAgPHNwYW4+4oCiPC9zcGFuPlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDBcIj5PbmxpbmU8L3NwYW4+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuXHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbi8vIEtvbXBvbmVuIHRlcnBpc2FoIHVudHVrIE9wZW5pbmcgU2NlbmUgeWFuZyBzZWxhbHUgbXVuY3VsXHJcbmludGVyZmFjZSBPcGVuaW5nU2NlbmVQcm9wcyB7XHJcbiAgY2hhcmFjdGVyOiBhbnk7XHJcbiAgaGFzTWVzc2FnZXM6IGJvb2xlYW47XHJcbiAgcHJvZmlsZUltYWdlVXJsOiBzdHJpbmcgfCBudWxsO1xyXG59XHJcblxyXG5jb25zdCBPcGVuaW5nU2NlbmUgPSBtZW1vKCh7IGNoYXJhY3RlciwgaGFzTWVzc2FnZXMsIHByb2ZpbGVJbWFnZVVybCB9OiBPcGVuaW5nU2NlbmVQcm9wcykgPT4ge1xyXG4gIGlmICghY2hhcmFjdGVyPy5vcGVuaW5nU2NlbmUpIHJldHVybiBudWxsO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9e2BweC00ICR7aGFzTWVzc2FnZXMgPyAncGItNCcgOiAncHQtNCd9YH0+XHJcbiAgICAgIHsvKiBPcGVuaW5nIHNjZW5lIGFzIGNoYXJhY3RlciBtZXNzYWdlICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zIG1heC13LTR4bFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBmbGV4LXNocmluay0wIHJlbGF0aXZlIGZsZXggc2hyaW5rLTAgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgPFNpbXBsZUF2YXRhclxyXG4gICAgICAgICAgICBzcmM9e3Byb2ZpbGVJbWFnZVVybH1cclxuICAgICAgICAgICAgYWx0PXtjaGFyYWN0ZXIubmFtZX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0xXCI+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj57Y2hhcmFjdGVyLm5hbWV9PC9zcGFuPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkFkZWdhbiBQZW1idWthPC9zcGFuPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctbXV0ZWQgcm91bmRlZC0yeGwgcm91bmRlZC10bC1tZCBwLTQgc2hhZG93LXNtIGJvcmRlclwiPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGxlYWRpbmctcmVsYXhlZFwiPlxyXG4gICAgICAgICAgICAgIHtjaGFyYWN0ZXIub3BlbmluZ1NjZW5lfVxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbmludGVyZmFjZSBDaGF0SW5wdXRQcm9wcyB7XHJcbiAgb25TZW5kTWVzc2FnZTogKG1lc3NhZ2U6IHN0cmluZykgPT4gdm9pZDtcclxuICBkaXNhYmxlZDogYm9vbGVhbjtcclxuICBjaGFyYWN0ZXJOYW1lPzogc3RyaW5nO1xyXG4gIGlzU3RyZWFtaW5nOiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBDaGF0SW5wdXQgPSBtZW1vKCh7IG9uU2VuZE1lc3NhZ2UsIGRpc2FibGVkLCBjaGFyYWN0ZXJOYW1lLCBpc1N0cmVhbWluZyB9OiBDaGF0SW5wdXRQcm9wcykgPT4ge1xyXG4gIGNvbnN0IFtuZXdNZXNzYWdlLCBzZXROZXdNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBpbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcclxuXHJcbiAgLy8gVXNlIHJlZiB0byBzdG9yZSBjdXJyZW50IG1lc3NhZ2UgdG8gYXZvaWQgc3RhbGUgY2xvc3VyZXNcclxuICBjb25zdCBtZXNzYWdlUmVmID0gdXNlUmVmKCcnKTtcclxuICBtZXNzYWdlUmVmLmN1cnJlbnQgPSBuZXdNZXNzYWdlO1xyXG5cclxuICAvLyBEZXRlY3QgbW9iaWxlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrTW9iaWxlID0gKCkgPT4ge1xyXG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IDc2OCk7XHJcbiAgICB9O1xyXG4gICAgY2hlY2tNb2JpbGUoKTtcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrTW9iaWxlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgY29uc3QgY3VycmVudE1lc3NhZ2UgPSBtZXNzYWdlUmVmLmN1cnJlbnQudHJpbSgpO1xyXG4gICAgaWYgKCFjdXJyZW50TWVzc2FnZSB8fCBkaXNhYmxlZCkgcmV0dXJuO1xyXG4gICAgb25TZW5kTWVzc2FnZShjdXJyZW50TWVzc2FnZSk7XHJcbiAgICBzZXROZXdNZXNzYWdlKCcnKTtcclxuICAgIG1lc3NhZ2VSZWYuY3VycmVudCA9ICcnO1xyXG4gIH0sIFtkaXNhYmxlZCwgb25TZW5kTWVzc2FnZV0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVLZXlQcmVzcyA9IHVzZUNhbGxiYWNrKChlOiBSZWFjdC5LZXlib2FyZEV2ZW50KSA9PiB7XHJcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcclxuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICBoYW5kbGVTZW5kTWVzc2FnZSgpO1xyXG4gICAgfVxyXG4gIH0sIFtoYW5kbGVTZW5kTWVzc2FnZV0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IHVzZUNhbGxiYWNrKChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgY29uc3QgdmFsdWUgPSBlLnRhcmdldC52YWx1ZTtcclxuICAgIHNldE5ld01lc3NhZ2UodmFsdWUpO1xyXG4gICAgbWVzc2FnZVJlZi5jdXJyZW50ID0gdmFsdWU7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVFbW9qaVNlbGVjdCA9IHVzZUNhbGxiYWNrKChlbW9qaTogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBuZXdWYWx1ZSA9IG5ld01lc3NhZ2UgKyBlbW9qaTtcclxuICAgIHNldE5ld01lc3NhZ2UobmV3VmFsdWUpO1xyXG4gICAgbWVzc2FnZVJlZi5jdXJyZW50ID0gbmV3VmFsdWU7XHJcbiAgICAvLyBGb2N1cyBiYWNrIHRvIGlucHV0IGFmdGVyIGVtb2ppIHNlbGVjdGlvblxyXG4gICAgaWYgKGlucHV0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgaW5wdXRSZWYuY3VycmVudC5mb2N1cygpO1xyXG4gICAgfVxyXG4gIH0sIFtuZXdNZXNzYWdlXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2XHJcbiAgICAgIGNsYXNzTmFtZT17YGZsZXgtc2hyaW5rLTAgYm9yZGVyLXQgYmctYmFja2dyb3VuZC85NSBiYWNrZHJvcC1ibHVyIHN1cHBvcnRzLVtiYWNrZHJvcC1maWx0ZXJdOmJnLWJhY2tncm91bmQvNjAgJHtpc01vYmlsZSA/ICdwLTMgcGItNicgOiAncC00J1xyXG4gICAgICAgIH1gfVxyXG4gICAgPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIHNwYWNlLXgtMlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgcmVmPXtpbnB1dFJlZn1cclxuICAgICAgICAgICAgdmFsdWU9e25ld01lc3NhZ2V9XHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgb25LZXlQcmVzcz17aGFuZGxlS2V5UHJlc3N9XHJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtgTWVzc2FnZSAke2NoYXJhY3Rlck5hbWUgfHwgJ2NoYXJhY3Rlcid9Li4uYH1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2Ake2lzTW9iaWxlID8gJ3ByLTIwJyA6ICdwci0yNCd9IHJvdW5kZWQtMnhsIGJvcmRlci0yIGZvY3VzOmJvcmRlci1iZXN0aWVrdS1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzICR7aXNNb2JpbGUgPyAncHktMyB0ZXh0LWJhc2UnIDogJ3B5LTMnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgIHN0eWxlPXtpc01vYmlsZSA/IHsgZm9udFNpemU6ICcxNnB4JyB9IDoge319XHJcbiAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgIHsvKiBJbnB1dCBhY3Rpb25zIGNvbnRhaW5lciAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgIHsvKiBFbW9qaSBQaWNrZXIgKi99XHJcbiAgICAgICAgICAgIDxFbW9qaVBpY2tlclxyXG4gICAgICAgICAgICAgIG9uRW1vamlTZWxlY3Q9e2hhbmRsZUVtb2ppU2VsZWN0fVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIHsvKiBMb2FkaW5nIGluZGljYXRvciAqL31cclxuICAgICAgICAgICAge2Rpc2FibGVkICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYm9yZGVyLTIgYm9yZGVyLWJlc3RpZWt1LXByaW1hcnkgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBtbC0xXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlU2VuZE1lc3NhZ2V9XHJcbiAgICAgICAgICBkaXNhYmxlZD17IW5ld01lc3NhZ2UudHJpbSgpIHx8IGRpc2FibGVkfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgYmctYmVzdGlla3UtcHJpbWFyeSBob3ZlcjpiZy1iZXN0aWVrdS1wcmltYXJ5LWRhcmsgcm91bmRlZC0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNjYWxlLTEwNSBkaXNhYmxlZDpob3ZlcjpzY2FsZS0xMDAgJHtpc01vYmlsZSA/ICdweC00IHB5LTMnIDogJ3B4LTYgcHktMydcclxuICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPFNlbmQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFF1aWNrIGFjdGlvbnMgb3Igc3VnZ2VzdGlvbnMgLSBIaWRlIG9uIG1vYmlsZSB0byBzYXZlIHNwYWNlICovfVxyXG4gICAgICB7IWlzTW9iaWxlICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC0yIHRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICA8c3Bhbj5UZWthbiBFbnRlciB1bnR1ayBtZW5naXJpbSwgU2hpZnQrRW50ZXIgdW50dWsgYmFyaXMgYmFydTwvc3Bhbj5cclxuICAgICAgICAgIHtpc1N0cmVhbWluZyAmJiAoXHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmVzdGlla3UtcHJpbWFyeSBhbmltYXRlLXB1bHNlXCI+QUkgc2VkYW5nIG1lcmVzcG9ucy4uLjwvc3Bhbj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbmNvbnN0IENoYXRJbnRlcmZhY2UgPSBtZW1vKCh7IGNoYXQsIG9uQmFjaywgb25Ub2dnbGVQcm9maWxlLCBvbkNoYXRSZXNldCwgc2VsZWN0ZWRCYWNrZ3JvdW5kIH06IENoYXRJbnRlcmZhY2VQcm9wcykgPT4ge1xyXG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8TWVzc2FnZVtdPihbXSk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3NlbmRpbmcsIHNldFNlbmRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzdHJlYW1pbmdNZXNzYWdlLCBzZXRTdHJlYW1pbmdNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBbaXNTdHJlYW1pbmcsIHNldElzU3RyZWFtaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY2hhcmFjdGVyQXNzZXRzLCBzZXRDaGFyYWN0ZXJBc3NldHNdID0gdXNlU3RhdGU8Q2hhcmFjdGVyQXNzZXRbXT4oW10pO1xyXG4gIGNvbnN0IFtiYWNrZ3JvdW5kQXNzZXRVcmwsIHNldEJhY2tncm91bmRBc3NldFVybF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaXNEZWxldGluZywgc2V0SXNEZWxldGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2NoYXJhY3Rlciwgc2V0Q2hhcmFjdGVyXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XHJcbiAgY29uc3QgW3Nob3dSZXNldE1vZGFsLCBzZXRTaG93UmVzZXRNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Byb2ZpbGVJbWFnZVVybCwgc2V0UHJvZmlsZUltYWdlVXJsXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpc0ltbWVyc2l2ZU1vZGUsIHNldElzSW1tZXJzaXZlTW9kZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgbWVzc2FnZXNFbmRSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IHN0cmVhbUNvbm5lY3Rpb25SZWYgPSB1c2VSZWY8eyBjbG9zZTogKCkgPT4gdm9pZCB9IHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3Qgc3RyZWFtaW5nVGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHN0cmVhbVRpbWVvdXRSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gU2hvdyByZXNldCBjb25maXJtYXRpb24gbW9kYWxcclxuICBjb25zdCBoYW5kbGVSZXNldENsaWNrID0gKCkgPT4ge1xyXG4gICAgc2V0U2hvd1Jlc2V0TW9kYWwodHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gRGVsZXRlIGNoYXQgbWVzc2FnZXMgZnVuY3Rpb25cclxuICBjb25zdCBoYW5kbGVEZWxldGVNZXNzYWdlcyA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmICghY2hhdC5pZCkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHNldElzRGVsZXRpbmcodHJ1ZSk7XHJcbiAgICAgIGF3YWl0IGNoYXRTZXJ2aWNlLmRlbGV0ZUNoYXRNZXNzYWdlcyhjaGF0LmlkKTtcclxuXHJcbiAgICAgIC8vIENsZWFyIG1lc3NhZ2VzIGxvY2FsbHlcclxuICAgICAgc2V0TWVzc2FnZXMoW10pO1xyXG5cclxuICAgICAgLy8gQ2xvc2UgbW9kYWxcclxuICAgICAgc2V0U2hvd1Jlc2V0TW9kYWwoZmFsc2UpO1xyXG5cclxuICAgICAgLy8gU2hvdyBzdWNjZXNzIHRvYXN0XHJcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJDaGF0IGJlcmhhc2lsIGRpcmVzZXQhXCIsIHtcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTZW11YSBwZXNhbiB0ZWxhaCBkaWhhcHVzLiBBbmRhIGJpc2EgbWVtdWxhaSBwZXJjYWthcGFuIGJhcnUuXCIsXHJcbiAgICAgICAgZHVyYXRpb246IDQwMDAsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gQ2FsbCBwYXJlbnQgY2FsbGJhY2sgaWYgcHJvdmlkZWRcclxuICAgICAgaWYgKG9uQ2hhdFJlc2V0KSB7XHJcbiAgICAgICAgb25DaGF0UmVzZXQoKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGRlbGV0ZSBtZXNzYWdlczonLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0LmVycm9yKFwiR2FnYWwgbWVyZXNldCBjaGF0XCIsIHtcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJUZXJqYWRpIGtlc2FsYWhhbiBzYWF0IG1lbmdoYXB1cyBwZXNhbi4gU2lsYWthbiBjb2JhIGxhZ2kuXCIsXHJcbiAgICAgICAgZHVyYXRpb246IDQwMDAsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNEZWxldGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gU3RhYmxlIHJlZmVyZW5jZXMgZm9yIGNoYXJhY3RlciBkYXRhIHRvIHByZXZlbnQgdW5uZWNlc3NhcnkgcmUtcmVuZGVyc1xyXG4gIGNvbnN0IGNoYXJhY3RlckltYWdlUmVmID0gdXNlUmVmKGNoYXQuY2hhcmFjdGVyPy5pbWFnZSk7XHJcbiAgY29uc3QgY2hhcmFjdGVyTmFtZVJlZiA9IHVzZVJlZihjaGF0LmNoYXJhY3Rlcj8ubmFtZSk7XHJcblxyXG4gIC8vIExvYWQgY2hhcmFjdGVyIGRhdGEgYW5kIGFzc2V0c1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBsb2FkQ2hhcmFjdGVyRGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBMb2FkIGNoYXJhY3RlciBkZXRhaWxzXHJcbiAgICAgICAgY29uc3QgY2hhcmFjdGVyRGF0YSA9IGF3YWl0IGNoYXJhY3RlclNlcnZpY2UuZ2V0Q2hhcmFjdGVyQnlJZChjaGF0LmNoYXJhY3RlcklkKTtcclxuICAgICAgICBzZXRDaGFyYWN0ZXIoY2hhcmFjdGVyRGF0YSk7XHJcblxyXG4gICAgICAgIC8vIExvYWQgY2hhcmFjdGVyIGFzc2V0c1xyXG4gICAgICAgIGNvbnN0IGFzc2V0cyA9IGF3YWl0IGNoYXJhY3RlclNlcnZpY2UuZ2V0Q2hhcmFjdGVyQXNzZXRzKGNoYXQuY2hhcmFjdGVySWQpO1xyXG4gICAgICAgIHNldENoYXJhY3RlckFzc2V0cyhhc3NldHMpO1xyXG5cclxuICAgICAgICAvLyBHZXQgcHJvZmlsZSBpbWFnZSBmcm9tIGFzc2V0cyAocHJlZmVyIHN0YXRpYyBpbWFnZSBvdmVyIHZpZGVvKVxyXG4gICAgICAgIGNvbnN0IHByb2ZpbGVBc3NldHMgPSBhc3NldHMuZmlsdGVyKGFzc2V0ID0+XHJcbiAgICAgICAgICBhc3NldC5wdXJwb3NlID09PSAncHJvZmlsZScgJiZcclxuICAgICAgICAgIGFzc2V0LnR5cGUgPT09ICdpbWFnZScgJiZcclxuICAgICAgICAgIGFzc2V0LmlzUHVibGlzaGVkXHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgaWYgKHByb2ZpbGVBc3NldHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgc2V0UHJvZmlsZUltYWdlVXJsKHByb2ZpbGVBc3NldHNbMF0udXJsKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgLy8gRmFsbGJhY2sgdG8gY2hhcmFjdGVyLmltYWdlIGlmIG5vIHByb2ZpbGUgYXNzZXRzXHJcbiAgICAgICAgICBzZXRQcm9maWxlSW1hZ2VVcmwoY2hhcmFjdGVyRGF0YS5pbWFnZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGNoYXJhY3RlciBkYXRhOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBsb2FkQ2hhcmFjdGVyRGF0YSgpO1xyXG4gIH0sIFtjaGF0LmNoYXJhY3RlcklkXSk7XHJcblxyXG4gIC8vIFVwZGF0ZSBiYWNrZ3JvdW5kIFVSTCB3aGVuIHNlbGVjdGVkQmFja2dyb3VuZCBjaGFuZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzZWxlY3RlZEJhY2tncm91bmQgJiYgY2hhcmFjdGVyQXNzZXRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgY29uc3QgYmFja2dyb3VuZEFzc2V0ID0gY2hhcmFjdGVyQXNzZXRzLmZpbmQoXHJcbiAgICAgICAgYXNzZXQgPT4gYXNzZXQuaWQgPT09IHNlbGVjdGVkQmFja2dyb3VuZCAmJiBhc3NldC5wdXJwb3NlID09PSAnYmFja2dyb3VuZCdcclxuICAgICAgKTtcclxuICAgICAgc2V0QmFja2dyb3VuZEFzc2V0VXJsKGJhY2tncm91bmRBc3NldD8udXJsIHx8IG51bGwpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0QmFja2dyb3VuZEFzc2V0VXJsKG51bGwpO1xyXG4gICAgfVxyXG4gIH0sIFtzZWxlY3RlZEJhY2tncm91bmQsIGNoYXJhY3RlckFzc2V0c10pO1xyXG5cclxuICAvLyBEZXRlY3QgbW9iaWxlIHNjcmVlbiBzaXplXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrTW9iaWxlID0gKCkgPT4ge1xyXG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IDc2OCk7XHJcbiAgICB9O1xyXG5cclxuICAgIGNoZWNrTW9iaWxlKCk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBVcGRhdGUgcmVmcyB3aGVuIGNoYXQgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjaGFyYWN0ZXJJbWFnZVJlZi5jdXJyZW50ID0gY2hhdC5jaGFyYWN0ZXI/LmltYWdlO1xyXG4gICAgY2hhcmFjdGVyTmFtZVJlZi5jdXJyZW50ID0gY2hhdC5jaGFyYWN0ZXI/Lm5hbWU7XHJcbiAgfSwgW2NoYXQuY2hhcmFjdGVyPy5pbWFnZSwgY2hhdC5jaGFyYWN0ZXI/Lm5hbWVdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIFJlc2V0IGFsbCBzdHJlYW1pbmcgc3RhdGVzIHdoZW4gY2hhdCBjaGFuZ2VzXHJcbiAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XHJcbiAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKCcnKTtcclxuICAgIHNldFNlbmRpbmcoZmFsc2UpO1xyXG5cclxuICAgIC8vIENsZWFudXAgYW55IGV4aXN0aW5nIGNvbm5lY3Rpb25zIGFuZCB0aW1lb3V0c1xyXG4gICAgaWYgKHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQuY2xvc2UoKTtcclxuICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50ID0gbnVsbDtcclxuICAgIH1cclxuICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCA9IG51bGw7XHJcbiAgICB9XHJcbiAgICBpZiAoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNsZWFyVGltZW91dChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICBzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQgPSBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGxvYWRNZXNzYWdlcygpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIC8vIENsZWFudXAgc3RyZWFtIGNvbm5lY3Rpb24gb24gdW5tb3VudFxyXG4gICAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50LmNsb3NlKCk7XHJcbiAgICAgIH1cclxuICAgICAgLy8gQ2xlYW51cCBzdHJlYW1pbmcgdGltZW91dFxyXG4gICAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgICAgLy8gQ2xlYW51cCBzdHJlYW0gdGltZW91dFxyXG4gICAgICBpZiAoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW2NoYXQuaWRdKTtcclxuXHJcbiAgLy8gU3RhYmxlIHNjcm9sbCBmdW5jdGlvbiB0aGF0IGRvZXNuJ3QgY2F1c2UgcmUtcmVuZGVyc1xyXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tUmVmID0gdXNlUmVmPCgpID0+IHZvaWQ+KCgpID0+IHsgfSk7XHJcbiAgc2Nyb2xsVG9Cb3R0b21SZWYuY3VycmVudCA9ICgpID0+IHtcclxuICAgIG1lc3NhZ2VzRW5kUmVmLmN1cnJlbnQ/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6ICdzbW9vdGgnIH0pO1xyXG4gIH07XHJcblxyXG4gIC8vIE9ubHkgc2Nyb2xsIHdoZW4gbWVzc2FnZXMgY291bnQgY2hhbmdlcywgbm90IG9uIGV2ZXJ5IHJlbmRlclxyXG4gIGNvbnN0IG1lc3NhZ2VzQ291bnRSZWYgPSB1c2VSZWYobWVzc2FnZXMubGVuZ3RoKTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKG1lc3NhZ2VzLmxlbmd0aCA+IG1lc3NhZ2VzQ291bnRSZWYuY3VycmVudCkge1xyXG4gICAgICBtZXNzYWdlc0NvdW50UmVmLmN1cnJlbnQgPSBtZXNzYWdlcy5sZW5ndGg7XHJcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNjcm9sbFRvQm90dG9tUmVmLmN1cnJlbnQ/LigpO1xyXG4gICAgICB9LCAxMDApO1xyXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XHJcbiAgICB9XHJcbiAgICBtZXNzYWdlc0NvdW50UmVmLmN1cnJlbnQgPSBtZXNzYWdlcy5sZW5ndGg7XHJcbiAgfSwgW21lc3NhZ2VzLmxlbmd0aF0pO1xyXG5cclxuICAvLyBTZXBhcmF0ZSBlZmZlY3QgZm9yIHN0cmVhbWluZyAtIG9ubHkgc2Nyb2xsIHdoZW4gc3RyZWFtaW5nIHN0YXJ0cyBvciBlbmRzXHJcbiAgY29uc3QgaXNTdHJlYW1pbmdSZWYgPSB1c2VSZWYoaXNTdHJlYW1pbmcpO1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaXNTdHJlYW1pbmcgJiYgIWlzU3RyZWFtaW5nUmVmLmN1cnJlbnQpIHtcclxuICAgICAgLy8gU3RyZWFtaW5nIGp1c3Qgc3RhcnRlZFxyXG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBzY3JvbGxUb0JvdHRvbVJlZi5jdXJyZW50Py4oKTtcclxuICAgICAgfSwgMjAwKTtcclxuICAgICAgaXNTdHJlYW1pbmdSZWYuY3VycmVudCA9IGlzU3RyZWFtaW5nO1xyXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XHJcbiAgICB9XHJcbiAgICBpc1N0cmVhbWluZ1JlZi5jdXJyZW50ID0gaXNTdHJlYW1pbmc7XHJcbiAgfSwgW2lzU3RyZWFtaW5nXSk7XHJcblxyXG4gIGNvbnN0IGxvYWRNZXNzYWdlcyA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2hhdFNlcnZpY2UuZ2V0Q2hhdE1lc3NhZ2VzKGNoYXQuaWQsIHsgbGltaXQ6IDUwIH0pO1xyXG4gICAgICBzZXRNZXNzYWdlcyhyZXNwb25zZS5kYXRhLnJldmVyc2UoKSk7IC8vIFJldmVyc2UgdG8gc2hvdyBvbGRlc3QgZmlyc3RcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIG1lc3NhZ2VzOicsIGVycm9yKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gdXNlQ2FsbGJhY2soYXN5bmMgKG1lc3NhZ2VUZXh0OiBzdHJpbmcpID0+IHtcclxuICAgIGlmICghbWVzc2FnZVRleHQudHJpbSgpIHx8IHNlbmRpbmcpIHJldHVybjtcclxuXHJcbiAgICBjb25zdCBjdXJyZW50Q2hhdElkID0gY2hhdC5pZDsgLy8gQ2FwdHVyZSBjdXJyZW50IGNoYXQgSURcclxuICAgIGNvbnN0IHRlbXBJZCA9IGB0ZW1wLSR7RGF0ZS5ub3coKX1gO1xyXG4gICAgc2V0U2VuZGluZyh0cnVlKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBBZGQgdXNlciBtZXNzYWdlIHRvIFVJIGltbWVkaWF0ZWx5XHJcbiAgICAgIGNvbnN0IHVzZXJNZXNzYWdlOiBNZXNzYWdlID0ge1xyXG4gICAgICAgIGlkOiB0ZW1wSWQsXHJcbiAgICAgICAgcm9sZTogJ3VzZXInLFxyXG4gICAgICAgIGNvbnRlbnQ6IG1lc3NhZ2VUZXh0LFxyXG4gICAgICAgIGNvbnRlbnRUeXBlOiAndGV4dCcsXHJcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xyXG5cclxuICAgICAgLy8gU2hvdyBBSSB0aGlua2luZyBmb3IgMy05IHNlY29uZHMgYmVmb3JlIHNlbmRpbmcgdG8gYmFja2VuZFxyXG4gICAgICBzZXRJc1N0cmVhbWluZyh0cnVlKTtcclxuICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcblxyXG4gICAgICBjb25zdCB0aGlua2luZ0RlbGF5ID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNjAwMCkgKyAzMDAwOyAvLyAzMDAwLTkwMDBtc1xyXG4gICAgICBjb25zb2xlLmxvZyhgQUkgdGhpbmtpbmcgZm9yICR7dGhpbmtpbmdEZWxheX1tcyBiZWZvcmUgcHJvY2Vzc2luZy4uLmApO1xyXG5cclxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIHRoaW5raW5nRGVsYXkpKTtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIGNoYXQgaGFzbid0IGNoYW5nZWQgZHVyaW5nIHRoZSB0aGlua2luZyBkZWxheVxyXG4gICAgICBpZiAoY2hhdC5pZCAhPT0gY3VycmVudENoYXRJZCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDaGF0IGNoYW5nZWQgZHVyaW5nIHRoaW5raW5nIGRlbGF5LCBhYm9ydGluZyBtZXNzYWdlIHNlbmQnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdTZW5kaW5nIG1lc3NhZ2UgdG8gY2hhdDonLCBjaGF0LmlkLCB7IG1lc3NhZ2U6IG1lc3NhZ2VUZXh0LCBzdHJlYW1pbmc6IHRydWUgfSk7XHJcblxyXG4gICAgICAvLyBTZW5kIG1lc3NhZ2Ugd2l0aCBzdHJlYW1pbmcgZW5hYmxlZFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNoYXRTZXJ2aWNlLnNlbmRNZXNzYWdlKGNoYXQuaWQsIHtcclxuICAgICAgICBtZXNzYWdlOiBtZXNzYWdlVGV4dCxcclxuICAgICAgICBzdHJlYW1pbmc6IHRydWUsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ01lc3NhZ2Ugc2VudCBzdWNjZXNzZnVsbHk6JywgcmVzcG9uc2UpO1xyXG5cclxuICAgICAgLy8gQ2hlY2sgYWdhaW4gaWYgY2hhdCBoYXNuJ3QgY2hhbmdlZCBhZnRlciBBUEkgY2FsbFxyXG4gICAgICBpZiAoY2hhdC5pZCAhPT0gY3VycmVudENoYXRJZCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDaGF0IGNoYW5nZWQgYWZ0ZXIgbWVzc2FnZSBzZW5kLCBhYm9ydGluZyBzdHJlYW1pbmcnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFVwZGF0ZSB0aGUgdGVtcG9yYXJ5IG1lc3NhZ2Ugd2l0aCByZWFsIElEIGlmIGF2YWlsYWJsZVxyXG4gICAgICBpZiAocmVzcG9uc2UuaWQpIHtcclxuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxyXG4gICAgICAgICAgbXNnLmlkID09PSB0ZW1wSWQgPyB7IC4uLm1zZywgaWQ6IHJlc3BvbnNlLmlkIH0gOiBtc2dcclxuICAgICAgICApKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU3RhcnQgc3RyZWFtaW5nIHJlc3BvbnNlXHJcbiAgICAgIGF3YWl0IHN0YXJ0U3RyZWFtaW5nKGN1cnJlbnRDaGF0SWQpO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzZW5kIG1lc3NhZ2U6JywgZXJyb3IpO1xyXG4gICAgICAvLyBPbmx5IHNob3cgZXJyb3IgYW5kIHJlbW92ZSBtZXNzYWdlIGlmIHdlJ3JlIHN0aWxsIG9uIHRoZSBzYW1lIGNoYXRcclxuICAgICAgaWYgKGNoYXQuaWQgPT09IGN1cnJlbnRDaGF0SWQpIHtcclxuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYuZmlsdGVyKG1zZyA9PiBtc2cuaWQgIT09IHRlbXBJZCkpO1xyXG4gICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gc2VuZCBtZXNzYWdlLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xyXG4gICAgICB9XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICAvLyBPbmx5IHJlc2V0IHNlbmRpbmcgc3RhdGUgaWYgd2UncmUgc3RpbGwgb24gdGhlIHNhbWUgY2hhdFxyXG4gICAgICBpZiAoY2hhdC5pZCA9PT0gY3VycmVudENoYXRJZCkge1xyXG4gICAgICAgIHNldFNlbmRpbmcoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSwgW3NlbmRpbmcsIGNoYXQuaWRdKTtcclxuXHJcbiAgY29uc3Qgc3RhcnRTdHJlYW1pbmcgPSBhc3luYyAoZXhwZWN0ZWRDaGF0SWQ/OiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IHRhcmdldENoYXRJZCA9IGV4cGVjdGVkQ2hhdElkIHx8IGNoYXQuaWQ7XHJcbiAgICBjb25zb2xlLmxvZygnU3RhcnRpbmcgc3RyZWFtIGZvciBjaGF0OicsIHRhcmdldENoYXRJZCk7XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgY2hhdCBoYXNuJ3QgY2hhbmdlZCBiZWZvcmUgc3RhcnRpbmcgc3RyZWFtXHJcbiAgICBpZiAoZXhwZWN0ZWRDaGF0SWQgJiYgY2hhdC5pZCAhPT0gZXhwZWN0ZWRDaGF0SWQpIHtcclxuICAgICAgY29uc29sZS5sb2coJ0NoYXQgY2hhbmdlZCBiZWZvcmUgc3RyZWFtaW5nIHN0YXJ0ZWQsIGFib3J0aW5nJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBpc1N0cmVhbWluZyBhbHJlYWR5IHNldCB0byB0cnVlIGluIGhhbmRsZVNlbmRNZXNzYWdlXHJcbiAgICAvLyBzZXRTdHJlYW1pbmdNZXNzYWdlKCcnKTsgLy8gS2VlcCBjdXJyZW50IHRoaW5raW5nIHN0YXRlXHJcblxyXG4gICAgLy8gQ2xvc2UgZXhpc3RpbmcgY29ubmVjdGlvbiBhbmQgY2xlYXIgdGltZW91dHNcclxuICAgIGlmIChzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50LmNsb3NlKCk7XHJcbiAgICB9XHJcbiAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgfVxyXG4gICAgaWYgKHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgIH1cclxuXHJcbiAgICBsZXQgY3VycmVudFN0cmVhbWluZ01lc3NhZ2UgPSAnJztcclxuXHJcbiAgICAvLyBNb3JlIGFnZ3Jlc3NpdmUgdGhyb3R0bGluZyBmb3Igc3RyZWFtaW5nIG1lc3NhZ2UgdXBkYXRlc1xyXG4gICAgbGV0IGxhc3RVcGRhdGVUaW1lID0gMDtcclxuICAgIGNvbnN0IHVwZGF0ZVN0cmVhbWluZ01lc3NhZ2UgPSAobWVzc2FnZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIC8vIENoZWNrIGlmIGNoYXQgaGFzbid0IGNoYW5nZWQgYmVmb3JlIHVwZGF0aW5nXHJcbiAgICAgIGlmIChleHBlY3RlZENoYXRJZCAmJiBjaGF0LmlkICE9PSBleHBlY3RlZENoYXRJZCkge1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcclxuICAgICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBJbW1lZGlhdGUgdXBkYXRlIGlmIGl0J3MgYmVlbiBtb3JlIHRoYW4gMTAwbXMgc2luY2UgbGFzdCB1cGRhdGVcclxuICAgICAgaWYgKG5vdyAtIGxhc3RVcGRhdGVUaW1lID4gMTAwKSB7XHJcbiAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZShtZXNzYWdlKTtcclxuICAgICAgICBsYXN0VXBkYXRlVGltZSA9IG5vdztcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBPdGhlcndpc2UsIHRocm90dGxlIHRoZSB1cGRhdGVcclxuICAgICAgICBzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgIC8vIENoZWNrIGFnYWluIGlmIGNoYXQgaGFzbid0IGNoYW5nZWQgYmVmb3JlIHRoZSBkZWxheWVkIHVwZGF0ZVxyXG4gICAgICAgICAgaWYgKCFleHBlY3RlZENoYXRJZCB8fCBjaGF0LmlkID09PSBleHBlY3RlZENoYXRJZCkge1xyXG4gICAgICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKG1lc3NhZ2UpO1xyXG4gICAgICAgICAgICBsYXN0VXBkYXRlVGltZSA9IERhdGUubm93KCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSwgMTAwKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBvbk1lc3NhZ2UgPSAoZGF0YTogU1NFRXZlbnQpID0+IHtcclxuICAgICAgY29uc29sZS5sb2coJ1JlY2VpdmVkIFNTRSBldmVudDonLCBkYXRhKTtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIGNoYXQgaGFzbid0IGNoYW5nZWQgZHVyaW5nIHN0cmVhbWluZ1xyXG4gICAgICBpZiAoZXhwZWN0ZWRDaGF0SWQgJiYgY2hhdC5pZCAhPT0gZXhwZWN0ZWRDaGF0SWQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBjaGFuZ2VkIGR1cmluZyBzdHJlYW1pbmcsIGlnbm9yaW5nIFNTRSBldmVudCcpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgc3dpdGNoIChkYXRhLmV2ZW50KSB7XHJcbiAgICAgICAgY2FzZSAnc3RhcnQnOlxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSBzdGFydGVkJyk7XHJcbiAgICAgICAgICBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSA9ICcnO1xyXG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlICd0b2tlbic6XHJcbiAgICAgICAgICBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSArPSBkYXRhLmRhdGE7XHJcbiAgICAgICAgICB1cGRhdGVTdHJlYW1pbmdNZXNzYWdlKGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ21ldGFkYXRhJzpcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gbWV0YWRhdGE6JywgZGF0YS5kYXRhKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ2VuZCc6XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnU3RyZWFtIGVuZGVkLCBmaW5hbCBtZXNzYWdlOicsIGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlKTtcclxuICAgICAgICAgIC8vIENsZWFyIGFueSBwZW5kaW5nIHN0cmVhbWluZyB1cGRhdGVzXHJcbiAgICAgICAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgLy8gQ2xlYXIgc3RyZWFtIHRpbWVvdXQgdG8gcHJldmVudCBmYWxzZSB0aW1lb3V0IGVycm9yc1xyXG4gICAgICAgICAgaWYgKHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIC8vIEZpbmFsaXplIHRoZSBzdHJlYW1pbmcgbWVzc2FnZSBvbmx5IGlmIGNoYXQgaGFzbid0IGNoYW5nZWRcclxuICAgICAgICAgIGlmICghZXhwZWN0ZWRDaGF0SWQgfHwgY2hhdC5pZCA9PT0gZXhwZWN0ZWRDaGF0SWQpIHtcclxuICAgICAgICAgICAgY29uc3QgZmluYWxNZXNzYWdlOiBNZXNzYWdlID0ge1xyXG4gICAgICAgICAgICAgIGlkOiBkYXRhLmRhdGE/LmNoYXRNZXNzYWdlSWQgfHwgYG1zZy0ke0RhdGUubm93KCl9YCxcclxuICAgICAgICAgICAgICByb2xlOiAnYXNzaXN0YW50JyxcclxuICAgICAgICAgICAgICBjb250ZW50OiBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSxcclxuICAgICAgICAgICAgICBjb250ZW50VHlwZTogJ3RleHQnLFxyXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgZmluYWxNZXNzYWdlXSk7XHJcbiAgICAgICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UoJycpO1xyXG4gICAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVbmtub3duIFNTRSBldmVudDonLCBkYXRhLmV2ZW50KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBvbkVycm9yID0gYXN5bmMgKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgY29uc29sZS5lcnJvcignU3RyZWFtIEVycm9yOicsIGVycm9yKTtcclxuXHJcbiAgICAgIC8vIENsZWFyIHRpbWVvdXRzIGZpcnN0IHRvIHByZXZlbnQgZnVydGhlciBlcnJvcnNcclxuICAgICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gT25seSB1cGRhdGUgc3RhdGUgaWYgY2hhdCBoYXNuJ3QgY2hhbmdlZFxyXG4gICAgICBpZiAoIWV4cGVjdGVkQ2hhdElkIHx8IGNoYXQuaWQgPT09IGV4cGVjdGVkQ2hhdElkKSB7XHJcbiAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UoJycpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50LmNsb3NlKCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIE9ubHkgcmVsb2FkIG1lc3NhZ2VzIGlmIGl0J3Mgbm90IGEgdGltZW91dCBlcnJvciwgc3RyZWFtaW5nIHdhcyBhY3R1YWxseSBhY3RpdmUsIGFuZCBjaGF0IGhhc24ndCBjaGFuZ2VkXHJcbiAgICAgIGlmICghZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ3RpbWVvdXQnKSAmJiBpc1N0cmVhbWluZyAmJiAoIWV4cGVjdGVkQ2hhdElkIHx8IGNoYXQuaWQgPT09IGV4cGVjdGVkQ2hhdElkKSkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIHRvIHJlbG9hZCBtZXNzYWdlcyBhcyBmYWxsYmFjay4uLicpO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBhd2FpdCBsb2FkTWVzc2FnZXMoKTtcclxuICAgICAgICB9IGNhdGNoIChyZWxvYWRFcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHJlbG9hZCBtZXNzYWdlczonLCByZWxvYWRFcnJvcik7XHJcbiAgICAgICAgICAvLyBEb24ndCBzaG93IGFsZXJ0IGZvciB0aW1lb3V0IGVycm9ycyB0byBhdm9pZCBkaXNydXB0aW5nIHVzZXJcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQ2hlY2sgb25lIG1vcmUgdGltZSBpZiBjaGF0IGhhc24ndCBjaGFuZ2VkIGJlZm9yZSBjcmVhdGluZyBjb25uZWN0aW9uXHJcbiAgICAgIGlmIChleHBlY3RlZENoYXRJZCAmJiBjaGF0LmlkICE9PSBleHBlY3RlZENoYXRJZCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdDaGF0IGNoYW5nZWQgYmVmb3JlIGNyZWF0aW5nIHN0cmVhbSBjb25uZWN0aW9uLCBhYm9ydGluZycpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQ3JlYXRlIG5ldyBzdHJlYW0gY29ubmVjdGlvblxyXG4gICAgICBjb25zdCBjb25uZWN0aW9uID0gYXdhaXQgY2hhdFNlcnZpY2UuY3JlYXRlU3RyZWFtQ29ubmVjdGlvbih0YXJnZXRDaGF0SWQsIG9uTWVzc2FnZSwgb25FcnJvcik7XHJcbiAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCA9IGNvbm5lY3Rpb247XHJcblxyXG4gICAgICAvLyBTZXQgdGltZW91dCBmb3Igc3RyZWFtaW5nICgzMCBzZWNvbmRzKSAtIGJ1dCBzdG9yZSByZWZlcmVuY2UgdG8gY2xlYXIgaXRcclxuICAgICAgc3RyZWFtVGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgLy8gT25seSB0cmlnZ2VyIHRpbWVvdXQgaWYgc3RyZWFtaW5nIGlzIHN0aWxsIGFjdGl2ZSwgY29ubmVjdGlvbiBleGlzdHMsIGFuZCBjaGF0IGhhc24ndCBjaGFuZ2VkXHJcbiAgICAgICAgaWYgKGlzU3RyZWFtaW5nICYmIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCAmJiAoIWV4cGVjdGVkQ2hhdElkIHx8IGNoYXQuaWQgPT09IGV4cGVjdGVkQ2hhdElkKSkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSB0aW1lb3V0LCBmYWxsaW5nIGJhY2sgdG8gbWVzc2FnZSByZWxvYWQnKTtcclxuICAgICAgICAgIG9uRXJyb3IobmV3IEVycm9yKCdTdHJlYW0gdGltZW91dCcpKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0sIDMwMDAwKTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIHN0cmVhbSBjb25uZWN0aW9uOicsIGVycm9yKTtcclxuICAgICAgLy8gT25seSB1cGRhdGUgc3RhdGUgaWYgY2hhdCBoYXNuJ3QgY2hhbmdlZFxyXG4gICAgICBpZiAoIWV4cGVjdGVkQ2hhdElkIHx8IGNoYXQuaWQgPT09IGV4cGVjdGVkQ2hhdElkKSB7XHJcbiAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gZXN0YWJsaXNoIGNvbm5lY3Rpb24gZm9yIEFJIHJlc3BvbnNlLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMjAgYm9yZGVyLWIgcC00IGFuaW1hdGUtcHVsc2UgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLW11dGVkIHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTMyXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZCByb3VuZGVkIHctMjBcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNCBzcGFjZS15LTQgb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogMyB9KS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zIGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctbXV0ZWQgcm91bmRlZC1mdWxsXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0zLzRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTEvMlwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICB7LyogQ2hhdCBIZWFkZXIgLSBTaW1wbGlmaWVkICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXgtc2hyaW5rLTAgYm9yZGVyLWIgYmctYmFja2dyb3VuZC85NSBiYWNrZHJvcC1ibHVyIHN1cHBvcnRzLVtiYWNrZHJvcC1maWx0ZXJdOmJnLWJhY2tncm91bmQvNjAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHctZnVsbCAke2lzTW9iaWxlID8gJ2gtMTYgcC0zJyA6ICdoLTIwIHAtNCdcclxuICAgICAgICB9YH0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cclxuICAgICAgICAgIHsvKiBNb2JpbGUgYmFjayBidXR0b24gKi99XHJcbiAgICAgICAgICB7aXNNb2JpbGUgJiYgb25CYWNrICYmIChcclxuICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uQmFja31cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpiZy1tdXRlZC81MCBtZDpoaWRkZW5cIlxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJCYWNrIHRvIGNoYXQgbGlzdFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgey8qIFNpbXBsZSB0aXRsZSAqL31cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtbGdcIj57Y2hhdC5jaGFyYWN0ZXI/Lm5hbWUgfHwgJ0NoYXQnfTwvaDI+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgey8qIE1vYmlsZSBwcm9maWxlIHRvZ2dsZSBidXR0b24gKi99XHJcbiAgICAgICAgICB7aXNNb2JpbGUgJiYgb25Ub2dnbGVQcm9maWxlICYmIChcclxuICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlUHJvZmlsZX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpiZy1tdXRlZC81MCBtZDpoaWRkZW5cIlxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJWaWV3IGNoYXJhY3RlciBwcm9maWxlXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxJbmZvIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgey8qIFJlc2V0IENoYXQgQnV0dG9uICovfVxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXNldENsaWNrfVxyXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNEZWxldGluZyB8fCBtZXNzYWdlcy5sZW5ndGggPT09IDB9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDAgaG92ZXI6YmctcmVkLTUwIGRhcms6aG92ZXI6YmctcmVkLTk1MCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxSb3RhdGVDY3cgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgIFJlc2V0XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogUGVzYW4gLSBTY3JvbGxhYmxlICovfVxyXG4gICAgICA8ZGl2XHJcbiAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBvdmVyZmxvdy14LWhpZGRlbiByZWxhdGl2ZVwiXHJcbiAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgIGJhY2tncm91bmQ6IGJhY2tncm91bmRBc3NldFVybFxyXG4gICAgICAgICAgICA/IGBsaW5lYXItZ3JhZGllbnQocmdiYSgwLCAwLCAwLCAwLjYpLCByZ2JhKDAsIDAsIDAsIDAuOCkpLCB1cmwoJHtiYWNrZ3JvdW5kQXNzZXRVcmx9KWBcclxuICAgICAgICAgICAgOiAnbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgaHNsKHZhcigtLWJhY2tncm91bmQpKSwgaHNsKHZhcigtLW11dGVkKSkvMC4yKScsXHJcbiAgICAgICAgICBiYWNrZ3JvdW5kU2l6ZTogYmFja2dyb3VuZEFzc2V0VXJsID8gJ2NvdmVyJyA6ICdhdXRvJyxcclxuICAgICAgICAgIGJhY2tncm91bmRQb3NpdGlvbjogYmFja2dyb3VuZEFzc2V0VXJsID8gJ2NlbnRlcicgOiAnYXV0bycsXHJcbiAgICAgICAgICBiYWNrZ3JvdW5kUmVwZWF0OiBiYWNrZ3JvdW5kQXNzZXRVcmwgPyAnbm8tcmVwZWF0JyA6ICdhdXRvJ1xyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICB7LyogV2VsY29tZSBNZXNzYWdlIHdpdGggUHJvZmlsZSAtIEFsd2F5cyBzaG93IHdoZW4gY2hhcmFjdGVyIGlzIGxvYWRlZCAqL31cclxuICAgICAgICB7Y2hhcmFjdGVyICYmIChcclxuICAgICAgICAgIDxXZWxjb21lTWVzc2FnZVxyXG4gICAgICAgICAgICBjaGFyYWN0ZXI9e2NoYXJhY3Rlcn1cclxuICAgICAgICAgICAgbWVzc2FnZUNvdW50PXtjaGF0Lm1lc3NhZ2VDb3VudH1cclxuICAgICAgICAgICAgcHJvZmlsZUltYWdlVXJsPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBPcGVuaW5nIFNjZW5lIC0gQWx3YXlzIHNob3cgYWZ0ZXIgcHJvZmlsZSAqL31cclxuICAgICAgICB7Y2hhcmFjdGVyICYmIChcclxuICAgICAgICAgIDxPcGVuaW5nU2NlbmVcclxuICAgICAgICAgICAgY2hhcmFjdGVyPXtjaGFyYWN0ZXJ9XHJcbiAgICAgICAgICAgIGhhc01lc3NhZ2VzPXttZXNzYWdlcy5sZW5ndGggPiAwfVxyXG4gICAgICAgICAgICBwcm9maWxlSW1hZ2VVcmw9e3Byb2ZpbGVJbWFnZVVybH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIE1lc3NhZ2VzICovfVxyXG4gICAgICAgIHttZXNzYWdlcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBwYi00IHNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXHJcbiAgICAgICAgICAgICAgPE1lc3NhZ2VJdGVtXHJcbiAgICAgICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XHJcbiAgICAgICAgICAgICAgICBtZXNzYWdlPXttZXNzYWdlfVxyXG4gICAgICAgICAgICAgICAgcHJvZmlsZUltYWdlVXJsPXtwcm9maWxlSW1hZ2VVcmx9XHJcbiAgICAgICAgICAgICAgICBjaGFyYWN0ZXJOYW1lPXtjaGFyYWN0ZXJOYW1lUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKSl9XHJcblxyXG4gICAgICAgICAgICB7LyogU3RyZWFtaW5nIE1lc3NhZ2UgKi99XHJcbiAgICAgICAgICAgIHtpc1N0cmVhbWluZyAmJiAoXHJcbiAgICAgICAgICAgICAgPFN0cmVhbWluZ01lc3NhZ2VcclxuICAgICAgICAgICAgICAgIHN0cmVhbWluZ01lc3NhZ2U9e3N0cmVhbWluZ01lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgICBwcm9maWxlSW1hZ2VVcmw9e3Byb2ZpbGVJbWFnZVVybH1cclxuICAgICAgICAgICAgICAgIGNoYXJhY3Rlck5hbWU9e2NoYXJhY3Rlck5hbWVSZWYuY3VycmVudH1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBNZXNzYWdlIElucHV0IC0gRml4ZWQgKi99XHJcbiAgICAgIDxDaGF0SW5wdXRcclxuICAgICAgICBvblNlbmRNZXNzYWdlPXtoYW5kbGVTZW5kTWVzc2FnZX1cclxuICAgICAgICBkaXNhYmxlZD17c2VuZGluZyB8fCBpc1N0cmVhbWluZ31cclxuICAgICAgICBjaGFyYWN0ZXJOYW1lPXtjaGFyYWN0ZXJOYW1lUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgaXNTdHJlYW1pbmc9e2lzU3RyZWFtaW5nfVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIFJlc2V0IENoYXQgTW9kYWwgKi99XHJcbiAgICAgIDxSZXNldENoYXRNb2RhbFxyXG4gICAgICAgIGlzT3Blbj17c2hvd1Jlc2V0TW9kYWx9XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd1Jlc2V0TW9kYWwoZmFsc2UpfVxyXG4gICAgICAgIG9uQ29uZmlybT17aGFuZGxlRGVsZXRlTWVzc2FnZXN9XHJcbiAgICAgICAgY2hhcmFjdGVyTmFtZT17Y2hhcmFjdGVyPy5uYW1lIHx8IGNoYXQuY2hhcmFjdGVyPy5uYW1lfVxyXG4gICAgICAgIG1lc3NhZ2VDb3VudD17bWVzc2FnZXMubGVuZ3RofVxyXG4gICAgICAgIGlzTG9hZGluZz17aXNEZWxldGluZ31cclxuICAgICAgLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuQ2hhdEludGVyZmFjZS5kaXNwbGF5TmFtZSA9ICdDaGF0SW50ZXJmYWNlJztcclxuXHJcbmV4cG9ydCB7IENoYXRJbnRlcmZhY2UgfTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VDYWxsYmFjayIsIm1lbW8iLCJjaGF0U2VydmljZSIsImNoYXJhY3RlclNlcnZpY2UiLCJCdXR0b24iLCJJbnB1dCIsIkJhZGdlIiwiU2VuZCIsIkFycm93TGVmdCIsIkluZm8iLCJSb3RhdGVDY3ciLCJmb3JtYXREaXN0YW5jZVRvTm93IiwiaWQiLCJSZWFjdE1hcmtkb3duIiwicmVtYXJrR2ZtIiwidG9hc3QiLCJSZXNldENoYXRNb2RhbCIsIkVtb2ppUGlja2VyIiwiU2ltcGxlQXZhdGFyIiwic3JjIiwiYWx0IiwiY2xhc3NOYW1lIiwiZGl2IiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJpbWciLCJNZXNzYWdlQ29udGVudCIsImNvbnRlbnQiLCJyb2xlIiwicCIsInJlbWFya1BsdWdpbnMiLCJjb21wb25lbnRzIiwiY2hpbGRyZW4iLCJlbSIsInN0cm9uZyIsImNvZGUiLCJpc0lubGluZSIsInVsIiwib2wiLCJsaSIsImJsb2NrcXVvdGUiLCJNZXNzYWdlSXRlbSIsIm1lc3NhZ2UiLCJwcm9maWxlSW1hZ2VVcmwiLCJjaGFyYWN0ZXJOYW1lIiwiZm9ybWF0VGltZSIsImRhdGVTdHJpbmciLCJEYXRlIiwiYWRkU3VmZml4IiwibG9jYWxlIiwiY3JlYXRlZEF0IiwiU3RyZWFtaW5nTWVzc2FnZSIsInN0cmVhbWluZ01lc3NhZ2UiLCJ0aGlua2luZ1RleHQiLCJzZXRUaGlua2luZ1RleHQiLCJtZXNzYWdlcyIsImluZGV4IiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImxlbmd0aCIsImNsZWFySW50ZXJ2YWwiLCJzcGFuIiwic3R5bGUiLCJhbmltYXRpb25EZWxheSIsIldlbGNvbWVNZXNzYWdlIiwiY2hhcmFjdGVyIiwibWVzc2FnZUNvdW50IiwibmFtZSIsImgyIiwidGl0bGUiLCJzdG9yeU1vZGUiLCJ2YXJpYW50Iiwic3RhdHVzIiwiT3BlbmluZ1NjZW5lIiwiaGFzTWVzc2FnZXMiLCJvcGVuaW5nU2NlbmUiLCJDaGF0SW5wdXQiLCJvblNlbmRNZXNzYWdlIiwiZGlzYWJsZWQiLCJpc1N0cmVhbWluZyIsIm5ld01lc3NhZ2UiLCJzZXROZXdNZXNzYWdlIiwiaXNNb2JpbGUiLCJzZXRJc01vYmlsZSIsImlucHV0UmVmIiwibWVzc2FnZVJlZiIsImN1cnJlbnQiLCJjaGVja01vYmlsZSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZVNlbmRNZXNzYWdlIiwiY3VycmVudE1lc3NhZ2UiLCJ0cmltIiwiaGFuZGxlS2V5UHJlc3MiLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZUlucHV0Q2hhbmdlIiwidmFsdWUiLCJ0YXJnZXQiLCJoYW5kbGVFbW9qaVNlbGVjdCIsImVtb2ppIiwibmV3VmFsdWUiLCJmb2N1cyIsInJlZiIsIm9uQ2hhbmdlIiwib25LZXlQcmVzcyIsInBsYWNlaG9sZGVyIiwiZm9udFNpemUiLCJvbkVtb2ppU2VsZWN0Iiwib25DbGljayIsIkNoYXRJbnRlcmZhY2UiLCJjaGF0Iiwib25CYWNrIiwib25Ub2dnbGVQcm9maWxlIiwib25DaGF0UmVzZXQiLCJzZWxlY3RlZEJhY2tncm91bmQiLCJzZXRNZXNzYWdlcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2VuZGluZyIsInNldFNlbmRpbmciLCJzZXRTdHJlYW1pbmdNZXNzYWdlIiwic2V0SXNTdHJlYW1pbmciLCJjaGFyYWN0ZXJBc3NldHMiLCJzZXRDaGFyYWN0ZXJBc3NldHMiLCJiYWNrZ3JvdW5kQXNzZXRVcmwiLCJzZXRCYWNrZ3JvdW5kQXNzZXRVcmwiLCJpc0RlbGV0aW5nIiwic2V0SXNEZWxldGluZyIsInNldENoYXJhY3RlciIsInNob3dSZXNldE1vZGFsIiwic2V0U2hvd1Jlc2V0TW9kYWwiLCJzZXRQcm9maWxlSW1hZ2VVcmwiLCJpc0ltbWVyc2l2ZU1vZGUiLCJzZXRJc0ltbWVyc2l2ZU1vZGUiLCJtZXNzYWdlc0VuZFJlZiIsInN0cmVhbUNvbm5lY3Rpb25SZWYiLCJzdHJlYW1pbmdUaW1lb3V0UmVmIiwic3RyZWFtVGltZW91dFJlZiIsImhhbmRsZVJlc2V0Q2xpY2siLCJoYW5kbGVEZWxldGVNZXNzYWdlcyIsImRlbGV0ZUNoYXRNZXNzYWdlcyIsInN1Y2Nlc3MiLCJkZXNjcmlwdGlvbiIsImR1cmF0aW9uIiwiZXJyb3IiLCJjb25zb2xlIiwiY2hhcmFjdGVySW1hZ2VSZWYiLCJpbWFnZSIsImNoYXJhY3Rlck5hbWVSZWYiLCJsb2FkQ2hhcmFjdGVyRGF0YSIsImNoYXJhY3RlckRhdGEiLCJnZXRDaGFyYWN0ZXJCeUlkIiwiY2hhcmFjdGVySWQiLCJhc3NldHMiLCJnZXRDaGFyYWN0ZXJBc3NldHMiLCJwcm9maWxlQXNzZXRzIiwiZmlsdGVyIiwiYXNzZXQiLCJwdXJwb3NlIiwidHlwZSIsImlzUHVibGlzaGVkIiwidXJsIiwiYmFja2dyb3VuZEFzc2V0IiwiZmluZCIsImNsb3NlIiwiY2xlYXJUaW1lb3V0IiwibG9hZE1lc3NhZ2VzIiwic2Nyb2xsVG9Cb3R0b21SZWYiLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwibWVzc2FnZXNDb3VudFJlZiIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJpc1N0cmVhbWluZ1JlZiIsInJlc3BvbnNlIiwiZ2V0Q2hhdE1lc3NhZ2VzIiwibGltaXQiLCJkYXRhIiwicmV2ZXJzZSIsIm1lc3NhZ2VUZXh0IiwiY3VycmVudENoYXRJZCIsInRlbXBJZCIsIm5vdyIsInVzZXJNZXNzYWdlIiwiY29udGVudFR5cGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZWRBdCIsInByZXYiLCJ0aGlua2luZ0RlbGF5IiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwibG9nIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzdHJlYW1pbmciLCJzZW5kTWVzc2FnZSIsIm1hcCIsIm1zZyIsInN0YXJ0U3RyZWFtaW5nIiwiYWxlcnQiLCJleHBlY3RlZENoYXRJZCIsInRhcmdldENoYXRJZCIsImN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlIiwibGFzdFVwZGF0ZVRpbWUiLCJ1cGRhdGVTdHJlYW1pbmdNZXNzYWdlIiwib25NZXNzYWdlIiwiZXZlbnQiLCJmaW5hbE1lc3NhZ2UiLCJjaGF0TWVzc2FnZUlkIiwib25FcnJvciIsImluY2x1ZGVzIiwicmVsb2FkRXJyb3IiLCJjb25uZWN0aW9uIiwiY3JlYXRlU3RyZWFtQ29ubmVjdGlvbiIsIkVycm9yIiwiQXJyYXkiLCJmcm9tIiwiXyIsImkiLCJzaXplIiwiYXJpYS1sYWJlbCIsImJhY2tncm91bmQiLCJiYWNrZ3JvdW5kU2l6ZSIsImJhY2tncm91bmRQb3NpdGlvbiIsImJhY2tncm91bmRSZXBlYXQiLCJpc09wZW4iLCJvbkNsb3NlIiwib25Db25maXJtIiwiaXNMb2FkaW5nIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});