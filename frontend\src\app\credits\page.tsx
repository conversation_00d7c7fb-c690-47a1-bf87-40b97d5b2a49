'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { useAuth } from "@/contexts/auth-context";
import { AuthModal } from "@/components/auth/auth-modal";
import { CreditBalance, CreditPackage } from "@/types/credit";
import { PaymentInvoice } from "@/types/payment";
import { creditService } from "@/services/credit";
import { paymentService } from "@/services/payment";
import { FlexibleCreditPurchase } from "@/components/credit/flexible-credit-purchase";
import { CreditBalanceCard } from "@/components/credit/credit-balance-card";
import { InvoiceTable } from "@/components/payment/invoice-table";
import { Button } from "@/components/ui/button";
import { useMidtrans } from "@/hooks/use-midtrans";
import { toast } from "sonner";
import { Coins, CreditCard, Zap, Shield, Clock, Users } from "lucide-react";

export default function CreditsPage() {
  const { isAuthenticated, user, isLoading } = useAuth();
  const { payWithSnap } = useMidtrans();
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null);
  const [creditPackages, setCreditPackages] = useState<CreditPackage[]>([]);
  const [invoices, setInvoices] = useState<PaymentInvoice[]>([]);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [loadingPackages, setLoadingPackages] = useState(false);
  const [loadingInvoices, setLoadingInvoices] = useState(false);


  // Load credit data when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadCreditBalance();
      loadCreditPackages();
      loadInvoices();
    }
  }, [isAuthenticated]);

  const loadCreditBalance = async () => {
    try {
      setLoadingBalance(true);
      const balance = await creditService.getCreditBalance();
      setCreditBalance(balance);
    } catch (error) {
      console.error('Failed to load credit balance:', error);
    } finally {
      setLoadingBalance(false);
    }
  };

  const loadCreditPackages = async () => {
    try {
      setLoadingPackages(true);
      const packages = await creditService.getCreditPricing();
      setCreditPackages(packages);
    } catch (error) {
      console.error('Failed to load credit packages:', error);
    } finally {
      setLoadingPackages(false);
    }
  };

  const loadInvoices = async () => {
    try {
      setLoadingInvoices(true);
      const response = await paymentService.getInvoices({ limit: 10 });
      setInvoices(response.data);
    } catch (error) {
      console.error('Failed to load invoices:', error);
    } finally {
      setLoadingInvoices(false);
    }
  };

  const handlePurchase = async (quantity: number) => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    try {
      const result = await creditService.buyCredits({ quantity });
      // Refresh invoices to show new invoice
      loadInvoices();
      // Show success toast
      toast.success("Invoice berhasil dibuat!", {
        description: `Invoice ${result.invoice.invoiceCode} telah dibuat. Silakan lakukan pembayaran di bawah.`,
        duration: 5000,
      });
    } catch (error) {
      console.error('Failed to purchase credits:', error);
      toast.error("Gagal membuat invoice", {
        description: "Terjadi kesalahan saat membuat invoice. Silakan coba lagi.",
        duration: 4000,
      });
    }
  };

  const handlePayNow = (invoice: PaymentInvoice) => {
    if (!invoice.paymentToken) {
      toast.error("Payment token tidak tersedia", {
        description: "Silakan refresh halaman dan coba lagi.",
        duration: 4000,
      });
      return;
    }

    // Pakai SNAP modal Midtrans
    payWithSnap(invoice.paymentToken, {
      onSuccess: async (result) => {
        console.log('Payment success:', result);

        try {
          // Validate payment dengan transactionId dari Midtrans
          await paymentService.validatePayment({
            invoiceId: invoice.id,
            transactionId: result.transaction_id,
            invoiceCode: invoice.invoiceCode,
          });

          // Refresh data setelah payment berhasil
          loadCreditBalance();
          loadInvoices();

          toast.success("Pembayaran berhasil!", {
            description: "B Coin telah ditambahkan ke akun Anda.",
            duration: 5000,
          });
        } catch (error) {
          console.error('Failed to validate payment:', error);
          toast.warning("Pembayaran berhasil", {
            description: "Terjadi kesalahan validasi, namun B Coin akan segera ditambahkan ke akun Anda.",
            duration: 5000,
          });
        }
      },
      onPending: (result) => {
        console.log('Payment pending:', result);
        toast.info("Pembayaran sedang diproses", {
          description: "Silakan tunggu konfirmasi dari bank. Status akan diupdate otomatis.",
          duration: 5000,
        });
        // Refresh invoices untuk update status
        loadInvoices();
      },
      onError: (result) => {
        console.log('Payment error:', result);
        toast.error("Pembayaran gagal", {
          description: "Terjadi kesalahan saat pembayaran. Silakan coba lagi atau hubungi customer service.",
          duration: 5000,
        });
      },
      onClose: () => {
        console.log('Payment popup closed');
        // Refresh invoices untuk cek status terbaru
        loadInvoices();
      }
    });
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading...</p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>B Coin</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-6 p-4 pt-0">
          {/* Header */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-bestieku-primary to-bestieku-primary-dark rounded-full flex items-center justify-center">
                <Coins className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold">
              {isAuthenticated ? `Kelola B Coin Anda` : 'Beli B Coin Bestieku'}
            </h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {isAuthenticated
                ? 'B Coin digunakan untuk berinteraksi dengan karakter AI. Beli lebih banyak B Coin untuk pengalaman chat yang lebih panjang.'
                : 'B Coin diperlukan untuk berinteraksi dengan karakter AI kami. Masuk atau daftar untuk mulai membeli B Coin.'
              }
            </p>
          </div>



          {/* Credit Balance - Only show when authenticated */}
          {isAuthenticated && (
            <div className="max-w-md mx-auto w-full">
              <CreditBalanceCard
                balance={creditBalance}
                loading={loadingBalance}
                onRefresh={loadCreditBalance}
              />
            </div>
          )}

          {/* Auth CTA - Only show when not authenticated */}
          {!isAuthenticated && (
            <div className="text-center">
              <Button
                onClick={() => setShowAuthModal(true)}
                className="bg-bestieku-primary hover:bg-bestieku-primary-dark text-white font-semibold px-8 py-3 rounded-xl"
              >
                Mulai Sekarang
              </Button>
            </div>
          )}

          {/* Flexible Credit Purchase */}
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">Beli B Coin</h2>
              <p className="text-muted-foreground">Tentukan jumlah B Coin sesuai kebutuhan Anda dengan sistem tiering yang menguntungkan</p>
            </div>

            <div className="max-w-4xl mx-auto w-full">
              <FlexibleCreditPurchase
                packages={creditPackages}
                onPurchase={handlePurchase}
                isAuthenticated={isAuthenticated}
                loading={loadingPackages}
              />
            </div>
          </div>

          {/* Invoice Table - Only show when authenticated */}
          {isAuthenticated && (
            <div className="max-w-4xl mx-auto w-full">
              <InvoiceTable
                invoices={invoices}
                loading={loadingInvoices}
                onPayNow={handlePayNow}
                onRefresh={loadInvoices}
              />
            </div>
          )}
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />


      </SidebarInset>
    </SidebarProvider>
  );
}
