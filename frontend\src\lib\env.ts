/**
 * Environment configuration for Bestieku frontend
 */

export const env = {
  // API Configuration
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api-staging.bestieku.ai',
  
  // App Configuration
  APP_ENV: process.env.NODE_ENV || 'development',
  
  // Feature Flags (if needed in the future)
  ENABLE_DEBUG: process.env.NODE_ENV === 'development',
} as const;

/**
 * Validate required environment variables
 */
export function validateEnv() {
  const requiredVars = ['NEXT_PUBLIC_API_BASE_URL'] as const;
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      console.warn(`Warning: ${varName} is not set, using default value`);
    }
  }
}

// Validate on module load
if (typeof window === 'undefined') {
  // Only validate on server side to avoid console warnings in browser
  validateEnv();
}
