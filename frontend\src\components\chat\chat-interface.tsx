'use client';

import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Chat, Message, SSEEvent } from '@/types/chat';
import { CharacterAsset } from '@/types/character';
import { chatService } from '@/services/chat';
import { characterService } from '@/services/character';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Badge } from '@/components/ui/badge';
import { Send, ArrowLeft, Info, RotateCcw } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { toast } from 'sonner';
import { ResetChatModal } from './reset-chat-modal';
import { EmojiPicker } from './emoji-picker';

// Simple avatar component that always uses static image
const SimpleAvatar = ({ src, alt, className }: {
  src: string | null;
  alt: string;
  className?: string;
}) => {
  if (!src) {
    return (
      <div className={`bg-bestieku-primary/10 flex size-full items-center justify-center rounded-full text-xs text-bestieku-primary ${className || ''}`}>
        {alt?.charAt(0)?.toUpperCase() || 'C'}
      </div>
    );
  }

  return (
    <img
      src={src}
      alt={alt}
      className={`aspect-square size-full object-cover ${className || ''}`}
    />
  );
};

interface ChatInterfaceProps {
  chat: Chat;
  onBack?: () => void;
  onToggleProfile?: () => void;
  onChatReset?: () => void;
  selectedBackground?: string | null;
}

interface MessageContentProps {
  content: string;
  role: 'user' | 'assistant';
}

const MessageContent = memo(({ content, role }: MessageContentProps) => {
  if (role === 'user') {
    // For user messages, just display as plain text with line breaks
    return <p className="text-sm whitespace-pre-wrap leading-relaxed">{content}</p>;
  }

  // For AI messages, render as Markdown
  return (
    <div className="text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Customize paragraph styling
          p: ({ children }) => (
            <p className="mb-2 last:mb-0 leading-relaxed">{children}</p>
          ),
          // Customize emphasis (italic)
          em: ({ children }) => (
            <em className="italic text-inherit">{children}</em>
          ),
          // Customize strong (bold)
          strong: ({ children }) => (
            <strong className="font-semibold text-inherit">{children}</strong>
          ),
          // Customize code blocks
          code: ({ children, className }) => {
            const isInline = !className;
            if (isInline) {
              return (
                <code className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono">
                  {children}
                </code>
              );
            }
            return (
              <code className="block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto">
                {children}
              </code>
            );
          },
          // Customize lists
          ul: ({ children }) => (
            <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
          ),
          li: ({ children }) => (
            <li className="text-sm">{children}</li>
          ),
          // Customize blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-muted pl-4 italic my-2">
              {children}
            </blockquote>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
});

interface MessageItemProps {
  message: Message;
  profileImageUrl: string | null;
  characterName?: string;
}

const MessageItem = memo(({ message, profileImageUrl, characterName }: MessageItemProps) => {
  const formatTime = useCallback((dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: id });
    } catch {
      return '';
    }
  }, []);

  return (
    <div
      className={`flex items-end gap-2 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      {/* Character Avatar for assistant messages */}
      {message.role === 'assistant' && (
        <div className="w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full">
          <SimpleAvatar
            src={profileImageUrl}
            alt={characterName || 'Character'}
          />
        </div>
      )}

      <div
        className={`max-w-[70%] rounded-2xl p-4 shadow-sm ${message.role === 'user'
            ? 'bg-bestieku-primary text-white rounded-br-md'
            : 'bg-white dark:bg-muted border rounded-bl-md'
          }`}
      >
        <MessageContent content={message.content} role={message.role} />
        <div className={`flex items-center justify-between mt-2 ${message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'
          }`}>
          <p className="text-xs">
            {formatTime(message.createdAt)}
          </p>
          {message.role === 'user' && (
            <div className="flex items-center gap-1">
              <div className="w-1 h-1 bg-white/70 rounded-full"></div>
              <div className="w-1 h-1 bg-white/70 rounded-full"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

interface StreamingMessageProps {
  streamingMessage: string;
  profileImageUrl: string | null;
  characterName?: string;
}

const StreamingMessage = memo(({ streamingMessage, profileImageUrl, characterName }: StreamingMessageProps) => {
  const [thinkingText, setThinkingText] = React.useState('Berpikir');

  // Cycle through thinking messages when not streaming actual content
  React.useEffect(() => {
    if (!streamingMessage) {
      const messages = ['Berpikir', 'Merenungkan', 'Mempertimbangkan', 'Memikirkan jawaban'];
      let index = 0;

      const interval = setInterval(() => {
        index = (index + 1) % messages.length;
        setThinkingText(messages[index]);
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [streamingMessage]);

  return (
    <div className="flex items-end gap-2 justify-start">
      <div className="w-8 h-8 mb-1 relative flex shrink-0 overflow-hidden rounded-full">
        <SimpleAvatar
          src={profileImageUrl}
          alt={characterName || 'Character'}
        />
      </div>

      <div className="max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm">
        {streamingMessage ? (
          <MessageContent content={streamingMessage} role="assistant" />
        ) : (
          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground">{thinkingText}</span>
            <div className="flex space-x-1 ml-2">
              <div className="w-2 h-2 bg-bestieku-primary rounded-full animate-bounce" />
              <div className="w-2 h-2 bg-bestieku-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="w-2 h-2 bg-bestieku-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            </div>
          </div>
        )}

        <div className="flex items-center mt-2 text-muted-foreground">
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-bestieku-primary rounded-full animate-pulse"></div>
            <span className="text-xs">
              {streamingMessage ? 'Mengetik...' : 'Sedang memikirkan respons...'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
});

interface WelcomeMessageProps {
  character: any;
  messageCount: number;
  profileImageUrl: string | null;
}

const WelcomeMessage = memo(({ character, messageCount, profileImageUrl }: WelcomeMessageProps) => {
  if (!character) return null;

  return (
    <div className="flex flex-col items-center justify-center py-8 px-4 space-y-6">
      {/* Character Profile Section */}
      <div className="text-center">
        <div className="relative mb-4">
          <div className="w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20 relative flex shrink-0 overflow-hidden rounded-full">
            <SimpleAvatar
              src={profileImageUrl}
              alt={character.name}
            />
          </div>
          {/* Online indicator */}
          <div className="absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full"></div>
        </div>

        <h2 className="text-2xl font-bold mb-2">{character.name}</h2>

        {character.title && (
          <p className="text-muted-foreground mb-3 font-medium">
            {character.title}
          </p>
        )}

        <div className="flex items-center justify-center gap-2 mb-4">
          {character.storyMode && (
            <Badge className="bg-bestieku-primary text-white">
              Mode Cerita
            </Badge>
          )}
          <Badge variant="outline" className="capitalize">
            {character.status}
          </Badge>
        </div>

        <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
          <span>{messageCount} pesan</span>
          <span>•</span>
          <span className="text-green-500">Online</span>
        </div>
      </div>


    </div>
  );
});

// Komponen terpisah untuk Opening Scene yang selalu muncul
interface OpeningSceneProps {
  character: any;
  hasMessages: boolean;
  profileImageUrl: string | null;
}

const OpeningScene = memo(({ character, hasMessages, profileImageUrl }: OpeningSceneProps) => {
  if (!character?.openingScene) return null;

  return (
    <div className={`px-4 ${hasMessages ? 'pb-4' : 'pt-4'}`}>
      {/* Opening scene as character message */}
      <div className="flex items-start space-x-3 max-w-4xl">
        <div className="w-8 h-8 flex-shrink-0 relative flex shrink-0 overflow-hidden rounded-full">
          <SimpleAvatar
            src={profileImageUrl}
            alt={character.name}
          />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{character.name}</span>
            <span className="text-xs text-muted-foreground">Adegan Pembuka</span>
          </div>
          <div className="bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border">
            <p className="text-sm leading-relaxed">
              {character.openingScene}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
});

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled: boolean;
  characterName?: string;
  isStreaming: boolean;
}

const ChatInput = memo(({ onSendMessage, disabled, characterName, isStreaming }: ChatInputProps) => {
  const [newMessage, setNewMessage] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Use ref to store current message to avoid stale closures
  const messageRef = useRef('');
  messageRef.current = newMessage;

  // Detect mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleSendMessage = useCallback(() => {
    const currentMessage = messageRef.current.trim();
    if (!currentMessage || disabled) return;
    onSendMessage(currentMessage);
    setNewMessage('');
    messageRef.current = '';
  }, [disabled, onSendMessage]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewMessage(value);
    messageRef.current = value;
  }, []);

  const handleEmojiSelect = useCallback((emoji: string) => {
    const newValue = newMessage + emoji;
    setNewMessage(newValue);
    messageRef.current = newValue;
    // Focus back to input after emoji selection
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [newMessage]);

  return (
    <div
      className={`flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${isMobile ? 'p-3 pb-6' : 'p-4'
        }`}
    >
      <div className="flex items-end space-x-2">
        <div className="flex-1 relative">
          <Input
            ref={inputRef}
            value={newMessage}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={`Message ${characterName || 'character'}...`}
            disabled={disabled}
            className={`${isMobile ? 'pr-20' : 'pr-24'} rounded-2xl border-2 focus:border-bestieku-primary transition-colors ${isMobile ? 'py-3 text-base' : 'py-3'
              }`}
            style={isMobile ? { fontSize: '16px' } : {}}
          />

          {/* Input actions container */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
            {/* Emoji Picker */}
            <EmojiPicker
              onEmojiSelect={handleEmojiSelect}
              disabled={disabled}
            />

            {/* Loading indicator */}
            {disabled && (
              <div className="w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin ml-1"></div>
            )}
          </div>
        </div>

        <Button
          onClick={handleSendMessage}
          disabled={!newMessage.trim() || disabled}
          className={`bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl transition-all duration-200 hover:scale-105 disabled:hover:scale-100 ${isMobile ? 'px-4 py-3' : 'px-6 py-3'
            }`}
        >
          <Send className="w-4 h-4" />
        </Button>
      </div>

      {/* Quick actions or suggestions - Hide on mobile to save space */}
      {!isMobile && (
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>Tekan Enter untuk mengirim, Shift+Enter untuk baris baru</span>
          {isStreaming && (
            <span className="text-bestieku-primary animate-pulse">AI sedang merespons...</span>
          )}
        </div>
      )}
    </div>
  );
});

const ChatInterface = memo(({ chat, onBack, onToggleProfile, onChatReset, selectedBackground }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [characterAssets, setCharacterAssets] = useState<CharacterAsset[]>([]);
  const [backgroundAssetUrl, setBackgroundAssetUrl] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [character, setCharacter] = useState<any>(null);
  const [showResetModal, setShowResetModal] = useState(false);
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const streamConnectionRef = useRef<{ close: () => void } | null>(null);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const streamTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Show reset confirmation modal
  const handleResetClick = () => {
    setShowResetModal(true);
  };

  // Delete chat messages function
  const handleDeleteMessages = async () => {
    if (!chat.id) return;

    try {
      setIsDeleting(true);
      await chatService.deleteChatMessages(chat.id);

      // Clear messages locally
      setMessages([]);

      // Close modal
      setShowResetModal(false);

      // Show success toast
      toast.success("Chat berhasil direset!", {
        description: "Semua pesan telah dihapus. Anda bisa memulai percakapan baru.",
        duration: 4000,
      });

      // Call parent callback if provided
      if (onChatReset) {
        onChatReset();
      }
    } catch (error) {
      console.error('Failed to delete messages:', error);
      toast.error("Gagal mereset chat", {
        description: "Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.",
        duration: 4000,
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Stable references for character data to prevent unnecessary re-renders
  const characterImageRef = useRef(chat.character?.image);
  const characterNameRef = useRef(chat.character?.name);

  // Load character data and assets
  useEffect(() => {
    const loadCharacterData = async () => {
      try {
        // Load character details
        const characterData = await characterService.getCharacterById(chat.characterId);
        setCharacter(characterData);

        // Load character assets
        const assets = await characterService.getCharacterAssets(chat.characterId);
        setCharacterAssets(assets);

        // Get profile image from assets (prefer static image over video)
        const profileAssets = assets.filter(asset =>
          asset.purpose === 'profile' &&
          asset.type === 'image' &&
          asset.isPublished
        );

        if (profileAssets.length > 0) {
          setProfileImageUrl(profileAssets[0].url);
        } else {
          // Fallback to character.image if no profile assets
          setProfileImageUrl(characterData.image);
        }
      } catch (error) {
        console.error('Failed to load character data:', error);
      }
    };

    loadCharacterData();
  }, [chat.characterId]);

  // Update background URL when selectedBackground changes
  useEffect(() => {
    if (selectedBackground && characterAssets.length > 0) {
      const backgroundAsset = characterAssets.find(
        asset => asset.id === selectedBackground && asset.purpose === 'background'
      );
      setBackgroundAssetUrl(backgroundAsset?.url || null);
    } else {
      setBackgroundAssetUrl(null);
    }
  }, [selectedBackground, characterAssets]);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Update refs when chat changes
  useEffect(() => {
    characterImageRef.current = chat.character?.image;
    characterNameRef.current = chat.character?.name;
  }, [chat.character?.image, chat.character?.name]);

  useEffect(() => {
    // Reset all streaming states when chat changes
    setIsStreaming(false);
    setStreamingMessage('');
    setSending(false);

    // Cleanup any existing connections and timeouts
    if (streamConnectionRef.current) {
      streamConnectionRef.current.close();
      streamConnectionRef.current = null;
    }
    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
      streamingTimeoutRef.current = null;
    }
    if (streamTimeoutRef.current) {
      clearTimeout(streamTimeoutRef.current);
      streamTimeoutRef.current = null;
    }

    loadMessages();

    return () => {
      // Cleanup stream connection on unmount
      if (streamConnectionRef.current) {
        streamConnectionRef.current.close();
      }
      // Cleanup streaming timeout
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
      // Cleanup stream timeout
      if (streamTimeoutRef.current) {
        clearTimeout(streamTimeoutRef.current);
      }
    };
  }, [chat.id]);

  // Stable scroll function that doesn't cause re-renders
  const scrollToBottomRef = useRef<() => void>(() => { });
  scrollToBottomRef.current = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Only scroll when messages count changes, not on every render
  const messagesCountRef = useRef(messages.length);
  useEffect(() => {
    if (messages.length > messagesCountRef.current) {
      messagesCountRef.current = messages.length;
      const timeoutId = setTimeout(() => {
        scrollToBottomRef.current?.();
      }, 100);
      return () => clearTimeout(timeoutId);
    }
    messagesCountRef.current = messages.length;
  }, [messages.length]);

  // Separate effect for streaming - only scroll when streaming starts or ends
  const isStreamingRef = useRef(isStreaming);
  useEffect(() => {
    if (isStreaming && !isStreamingRef.current) {
      // Streaming just started
      const timeoutId = setTimeout(() => {
        scrollToBottomRef.current?.();
      }, 200);
      isStreamingRef.current = isStreaming;
      return () => clearTimeout(timeoutId);
    }
    isStreamingRef.current = isStreaming;
  }, [isStreaming]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await chatService.getChatMessages(chat.id, { limit: 50 });
      setMessages(response.data.reverse()); // Reverse to show oldest first
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = useCallback(async (messageText: string) => {
    if (!messageText.trim() || sending) return;

    const currentChatId = chat.id; // Capture current chat ID
    const tempId = `temp-${Date.now()}`;
    setSending(true);

    try {
      // Add user message to UI immediately
      const userMessage: Message = {
        id: tempId,
        role: 'user',
        content: messageText,
        contentType: 'text',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setMessages(prev => [...prev, userMessage]);

      // Show AI thinking for 3-9 seconds before sending to backend
      setIsStreaming(true);
      setStreamingMessage('');

      const thinkingDelay = Math.floor(Math.random() * 6000) + 3000; // 3000-9000ms
      console.log(`AI thinking for ${thinkingDelay}ms before processing...`);

      await new Promise(resolve => setTimeout(resolve, thinkingDelay));

      // Check if chat hasn't changed during the thinking delay
      if (chat.id !== currentChatId) {
        console.log('Chat changed during thinking delay, aborting message send');
        return;
      }

      console.log('Sending message to chat:', chat.id, { message: messageText, streaming: true });

      // Send message with streaming enabled
      const response = await chatService.sendMessage(chat.id, {
        message: messageText,
        streaming: true,
      });

      console.log('Message sent successfully:', response);

      // Check again if chat hasn't changed after API call
      if (chat.id !== currentChatId) {
        console.log('Chat changed after message send, aborting streaming');
        return;
      }

      // Update the temporary message with real ID if available
      if (response.id) {
        setMessages(prev => prev.map(msg =>
          msg.id === tempId ? { ...msg, id: response.id } : msg
        ));
      }

      // Start streaming response
      await startStreaming(currentChatId);

    } catch (error) {
      console.error('Failed to send message:', error);
      // Only show error and remove message if we're still on the same chat
      if (chat.id === currentChatId) {
        setMessages(prev => prev.filter(msg => msg.id !== tempId));
        alert('Failed to send message. Please try again.');
      }
    } finally {
      // Only reset sending state if we're still on the same chat
      if (chat.id === currentChatId) {
        setSending(false);
      }
    }
  }, [sending, chat.id]);

  const startStreaming = async (expectedChatId?: string) => {
    const targetChatId = expectedChatId || chat.id;
    console.log('Starting stream for chat:', targetChatId);

    // Check if chat hasn't changed before starting stream
    if (expectedChatId && chat.id !== expectedChatId) {
      console.log('Chat changed before streaming started, aborting');
      return;
    }

    // isStreaming already set to true in handleSendMessage
    // setStreamingMessage(''); // Keep current thinking state

    // Close existing connection and clear timeouts
    if (streamConnectionRef.current) {
      streamConnectionRef.current.close();
    }
    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
    }
    if (streamTimeoutRef.current) {
      clearTimeout(streamTimeoutRef.current);
    }

    let currentStreamingMessage = '';

    // More aggressive throttling for streaming message updates
    let lastUpdateTime = 0;
    const updateStreamingMessage = (message: string) => {
      // Check if chat hasn't changed before updating
      if (expectedChatId && chat.id !== expectedChatId) {
        return;
      }

      const now = Date.now();
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }

      // Immediate update if it's been more than 100ms since last update
      if (now - lastUpdateTime > 100) {
        setStreamingMessage(message);
        lastUpdateTime = now;
      } else {
        // Otherwise, throttle the update
        streamingTimeoutRef.current = setTimeout(() => {
          // Check again if chat hasn't changed before the delayed update
          if (!expectedChatId || chat.id === expectedChatId) {
            setStreamingMessage(message);
            lastUpdateTime = Date.now();
          }
        }, 100);
      }
    };

    const onMessage = (data: SSEEvent) => {
      console.log('Received SSE event:', data);

      // Check if chat hasn't changed during streaming
      if (expectedChatId && chat.id !== expectedChatId) {
        console.log('Chat changed during streaming, ignoring SSE event');
        return;
      }

      switch (data.event) {
        case 'start':
          console.log('Stream started');
          currentStreamingMessage = '';
          setStreamingMessage('');
          break;
        case 'token':
          currentStreamingMessage += data.data;
          updateStreamingMessage(currentStreamingMessage);
          break;
        case 'metadata':
          console.log('Stream metadata:', data.data);
          break;
        case 'end':
          console.log('Stream ended, final message:', currentStreamingMessage);
          // Clear any pending streaming updates
          if (streamingTimeoutRef.current) {
            clearTimeout(streamingTimeoutRef.current);
          }
          // Clear stream timeout to prevent false timeout errors
          if (streamTimeoutRef.current) {
            clearTimeout(streamTimeoutRef.current);
          }
          // Finalize the streaming message only if chat hasn't changed
          if (!expectedChatId || chat.id === expectedChatId) {
            const finalMessage: Message = {
              id: data.data?.chatMessageId || `msg-${Date.now()}`,
              role: 'assistant',
              content: currentStreamingMessage,
              contentType: 'text',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            setMessages(prev => [...prev, finalMessage]);
            setStreamingMessage('');
            setIsStreaming(false);
          }
          if (streamConnectionRef.current) {
            streamConnectionRef.current.close();
          }
          break;
        default:
          console.log('Unknown SSE event:', data.event);
      }
    };

    const onError = async (error: any) => {
      console.error('Stream Error:', error);

      // Clear timeouts first to prevent further errors
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
      if (streamTimeoutRef.current) {
        clearTimeout(streamTimeoutRef.current);
      }

      // Only update state if chat hasn't changed
      if (!expectedChatId || chat.id === expectedChatId) {
        setIsStreaming(false);
        setStreamingMessage('');
      }

      if (streamConnectionRef.current) {
        streamConnectionRef.current.close();
      }

      // Only reload messages if it's not a timeout error, streaming was actually active, and chat hasn't changed
      if (!error.message?.includes('timeout') && isStreaming && (!expectedChatId || chat.id === expectedChatId)) {
        console.log('Attempting to reload messages as fallback...');
        try {
          await loadMessages();
        } catch (reloadError) {
          console.error('Failed to reload messages:', reloadError);
          // Don't show alert for timeout errors to avoid disrupting user
        }
      }
    };

    try {
      // Check one more time if chat hasn't changed before creating connection
      if (expectedChatId && chat.id !== expectedChatId) {
        console.log('Chat changed before creating stream connection, aborting');
        return;
      }

      // Create new stream connection
      const connection = await chatService.createStreamConnection(targetChatId, onMessage, onError);
      streamConnectionRef.current = connection;

      // Set timeout for streaming (30 seconds) - but store reference to clear it
      streamTimeoutRef.current = setTimeout(() => {
        // Only trigger timeout if streaming is still active, connection exists, and chat hasn't changed
        if (isStreaming && streamConnectionRef.current && (!expectedChatId || chat.id === expectedChatId)) {
          console.log('Stream timeout, falling back to message reload');
          onError(new Error('Stream timeout'));
        }
      }, 30000);

    } catch (error) {
      console.error('Failed to create stream connection:', error);
      // Only update state if chat hasn't changed
      if (!expectedChatId || chat.id === expectedChatId) {
        setIsStreaming(false);
        alert('Failed to establish connection for AI response. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col h-full overflow-hidden">
        <div className="flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-muted rounded-full" />
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded w-32" />
              <div className="h-3 bg-muted rounded w-20" />
            </div>
          </div>
        </div>
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-muted rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-4 bg-muted rounded w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Chat Header - Simplified */}
      <div className={`flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full ${isMobile ? 'h-16 p-3' : 'h-20 p-4'
        }`}>
        <div className="flex items-center space-x-3">
          {/* Mobile back button */}
          {isMobile && onBack && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onBack}
              className="hover:bg-muted/50 md:hidden"
              aria-label="Back to chat list"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
          )}

          {/* Simple title */}
          <div>
            <h2 className="font-semibold text-lg">{chat.character?.name || 'Chat'}</h2>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Mobile profile toggle button */}
          {isMobile && onToggleProfile && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleProfile}
              className="hover:bg-muted/50 md:hidden"
              aria-label="View character profile"
            >
              <Info className="w-4 h-4" />
            </Button>
          )}

          {/* Reset Chat Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleResetClick}
            disabled={isDeleting || messages.length === 0}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset
          </Button>
        </div>
      </div>

      {/* Pesan - Scrollable */}
      <div
        className="flex-1 overflow-y-auto overflow-x-hidden relative"
        style={{
          background: backgroundAssetUrl
            ? `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(${backgroundAssetUrl})`
            : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',
          backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',
          backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',
          backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'
        }}
      >
        {/* Welcome Message with Profile - Always show when character is loaded */}
        {character && (
          <WelcomeMessage
            character={character}
            messageCount={chat.messageCount}
            profileImageUrl={profileImageUrl}
          />
        )}

        {/* Opening Scene - Always show after profile */}
        {character && (
          <OpeningScene
            character={character}
            hasMessages={messages.length > 0}
            profileImageUrl={profileImageUrl}
          />
        )}

        {/* Messages */}
        {messages.length > 0 && (
          <div className="px-4 pb-4 space-y-4">
            {messages.map((message) => (
              <MessageItem
                key={message.id}
                message={message}
                profileImageUrl={profileImageUrl}
                characterName={characterNameRef.current}
              />
            ))}

            {/* Streaming Message */}
            {isStreaming && (
              <StreamingMessage
                streamingMessage={streamingMessage}
                profileImageUrl={profileImageUrl}
                characterName={characterNameRef.current}
              />
            )}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input - Fixed */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={sending || isStreaming}
        characterName={characterNameRef.current}
        isStreaming={isStreaming}
      />

      {/* Reset Chat Modal */}
      <ResetChatModal
        isOpen={showResetModal}
        onClose={() => setShowResetModal(false)}
        onConfirm={handleDeleteMessages}
        characterName={character?.name || chat.character?.name}
        messageCount={messages.length}
        isLoading={isDeleting}
      />
    </div>
  );
});

ChatInterface.displayName = 'ChatInterface';

export { ChatInterface };
