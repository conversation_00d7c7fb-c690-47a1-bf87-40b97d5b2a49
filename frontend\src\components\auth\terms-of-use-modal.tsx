'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface TermsOfUseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function TermsOfUseModal({ isOpen, onClose }: TermsOfUseModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden border-0 shadow-2xl bg-background">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Ketentuan Penggunaan</DialogTitle>
          <DialogDescription>
            Terakhir diperbarui: 9 Agustus 2025
          </DialogDescription>
        </DialogHeader>
        
        <div className="overflow-y-auto max-h-[60vh] pr-4 space-y-6 text-sm">
          <section>
            <h3 className="text-lg font-semibold mb-3">1. <PERSON><PERSON><PERSON><PERSON></h3>
            <p className="text-muted-foreground leading-relaxed">
              Dengan mengakses dan menggunakan platform Bestieku, Anda menyetujui untuk terikat 
              dengan ketentuan penggunaan ini. Jika Anda tidak setuju dengan bagian mana pun dari 
              ketentuan ini, Anda tidak diizinkan untuk menggunakan layanan kami.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">2. Deskripsi Layanan</h3>
            <p className="text-muted-foreground leading-relaxed">
              Bestieku menyediakan platform chat AI dengan karakter virtual untuk hiburan dan 
              komunikasi. Layanan kami termasuk:
            </p>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground mt-2">
              <li>Chat dengan karakter AI yang dipersonalisasi</li>
              <li>Sistem kredit untuk fitur premium</li>
              <li>Penyimpanan riwayat percakapan</li>
              <li>Fitur favorit dan personalisasi pengalaman</li>
            </ul>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">3. Persyaratan Usia</h3>
            <p className="text-muted-foreground leading-relaxed">
              Anda harus berusia minimal 13 tahun untuk menggunakan layanan kami. Jika Anda berusia 
              di bawah 18 tahun, Anda harus memiliki izin dari orang tua atau wali hukum untuk 
              menggunakan platform ini.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">4. Akun Pengguna</h3>
            <div className="space-y-2 text-muted-foreground">
              <p><strong>Pendaftaran:</strong> Anda bertanggung jawab untuk memberikan informasi yang akurat dan terkini.</p>
              <p><strong>Keamanan:</strong> Anda bertanggung jawab untuk menjaga kerahasiaan kata sandi dan aktivitas di akun Anda.</p>
              <p><strong>Penggunaan:</strong> Anda setuju untuk tidak menggunakan akun untuk tujuan ilegal atau tidak sah.</p>
              <p><strong>Penghentian:</strong> Kami berhak menangguhkan atau menghentikan akun yang melanggar ketentuan ini.</p>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">5. Konten dan Perilaku Pengguna</h3>
            <p className="text-muted-foreground leading-relaxed">
              Anda setuju untuk tidak:
            </p>
            <ul className="list-disc list-inside space-y-2 text-muted-foreground mt-2">
              <li>Menggunakan layanan untuk tujuan ilegal atau merugikan</li>
              <li>Mengirim konten yang mengandung kebencian, kekerasan, atau pelecehan</li>
              <li>Melanggar hak kekayaan intelektual orang lain</li>
              <li>Mencoba merusak atau mengganggu sistem kami</li>
              <li>Menggunakan bot atau metode otomatis untuk mengakses layanan</li>
              <li>Berpartisipasi dalam aktivitas penipuan atau phishing</li>
            </ul>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">6. Kredit dan Pembayaran</h3>
            <div className="space-y-2 text-muted-foreground">
              <p><strong>Pembelian:</strong> Kredit dibeli melalui sistem pembayaran yang tersedia dan tidak dapat dikembalikan.</p>
              <p><strong>Penggunaan:</strong> Kredit digunakan untuk mengakses fitur premium dan dapat berlaku selama akun aktif.</p>
              <p><strong>Transfer:</strong> Kredit tidak dapat ditransfer antar akun kecuali fitur khusus yang disediakan.</p>
              <p><strong>Kadaluarsa:</strong> Kredit tidak memiliki tanggal kadaluarsa selama akun tetap aktif.</p>
            </div>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">7. Hak Kekayaan Intelektual</h3>
            <p className="text-muted-foreground leading-relaxed">
              Semua konten di platform Bestieku, termasuk karakter AI, desain, dan fitur, adalah 
              milik Bestieku atau pemberi lisensi kami. Anda tidak diizinkan untuk menyalin, 
              memodifikasi, atau mendistribusikan konten tanpa izin tertulis.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">8. Batasan Tanggung Jawab</h3>
            <p className="text-muted-foreground leading-relaxed">
              Layanan kami disediakan "sebagaimana adanya" tanpa jaminan apa pun. Kami tidak bertanggung 
              jawab atas kerugian langsung, tidak langsung, insidental, atau konsekuensial yang timbul 
              dari penggunaan layanan kami. Karakter AI adalah virtual dan tidak mewakili orang nyata.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">9. Modifikasi Layanan</h3>
            <p className="text-muted-foreground leading-relaxed">
              Kami berhak untuk mengubah, menangguhkan, atau menghentikan layanan kami kapan saja, 
              termasuk fitur, harga, dan ketentuan penggunaan. Perubahan signifikan akan diberitahukan 
              kepada pengguna.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">10. Penghentian</h3>
            <p className="text-muted-foreground leading-relaxed">
              Kami dapat menghentikan atau menangguhkan akun Anda jika Anda melanggar ketentuan ini 
              atau jika kami percaya tindakan tersebut diperlukan untuk keamanan platform. Anda dapat 
              menghapus akun Anda kapan saja melalui pengaturan profil.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">11. Perubahan Ketentuan</h3>
            <p className="text-muted-foreground leading-relaxed">
              Kami dapat memperbarui ketentuan penggunaan ini dari waktu ke waktu. Perubahan akan 
              dipublikasikan di halaman ini dengan tanggal efektif yang diperbarui. Penggunaan 
              berkelanjutan atas layanan kami setelah perubahan berarti Anda menerima ketentuan baru.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">12. Hukum yang Berlaku</h3>
            <p className="text-muted-foreground leading-relaxed">
              Ketentuan ini diatur oleh hukum Indonesia. Setiap perselisihan akan diselesaikan melalui 
              arbitrase di Jakarta.
            </p>
          </section>

          <section>
            <h3 className="text-lg font-semibold mb-3">13. Kontak</h3>
            <p className="text-muted-foreground leading-relaxed">
              Untuk pertanyaan tentang ketentuan penggunaan ini, hubungi kami di: 
              <a href="mailto:<EMAIL>" className="text-bestieku-primary hover:underline ml-1">
                <EMAIL>
              </a>
            </p>
          </section>
        </div>

        <div className="mt-6 flex justify-end">
          <Button onClick={onClose} className="bg-bestieku-primary hover:bg-bestieku-primary-dark">
            Mengerti
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}